const express = require("express");
const router = express.Router();

module.exports = (pool) => {
  // Get all survey responses
  router.get("/", async (req, res) => {
    try {
      pool.query(
        "SELECT * FROM customer_survey ORDER BY created_at DESC",
        (err, results) => {
          if (err) {
            console.error("Error fetching surveys:", err);
            return res.status(500).json({
              message: "An error occurred while fetching survey responses.",
            });
          }
          res.json(results);
        }
      );
    } catch (error) {
      console.error("Unexpected error:", error);
      res.status(500).json({
        message: "An unexpected error occurred.",
      });
    }
  });

  // Create a new survey response
  router.post("/", async (req, res) => {
    const {
      customer_name,
      plot_no,
      primary_phone,
      alternative_phone,
      email,
      service_feedback,
      service_rating,
      server_name,
      server_rating,
      received_title,
      improvement_areas,
    } = req.body;

    try {
      pool.query(
        `INSERT INTO customer_survey 
         (customer_name, plot_no, primary_phone, alternative_phone, email, 
          service_feedback, service_rating, server_name, server_rating, 
          received_title, improvement_areas) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          customer_name,
          plot_no,
          primary_phone,
          alternative_phone,
          email,
          service_feedback,
          service_rating,
          server_name,
          server_rating,
          received_title,
          improvement_areas,
        ],
        (err, result) => {
          if (err) {
            console.error("Error inserting survey response:", err);
            return res.status(500).json({
              message: "An error occurred while creating survey response.",
            });
          }

          res.status(201).json({ message: "Survey response created successfully" });
        }
      );
    } catch (error) {
      console.error("Unexpected error:", error);
      res.status(500).json({
        message: "An unexpected error occurred.",
      });
    }
  });

  // Update a survey response
  router.put("/:id", async (req, res) => {
    const { id } = req.params;
    const {
      customer_name,
      plot_no,
      primary_phone,
      alternative_phone,
      email,
      service_feedback,
      service_rating,
      server_name,
      server_rating,
      received_title,
      improvement_areas,
    } = req.body;

    try {
      pool.query(
        `UPDATE customer_survey 
         SET customer_name = ?, plot_no = ?, primary_phone = ?, alternative_phone = ?, 
             email = ?, service_feedback = ?, service_rating = ?, server_name = ?, 
             server_rating = ?, received_title = ?, improvement_areas = ? 
         WHERE id = ?`,
        [
          customer_name,
          plot_no,
          primary_phone,
          alternative_phone,
          email,
          service_feedback,
          service_rating,
          server_name,
          server_rating,
          received_title,
          improvement_areas,
          id,
        ],
        (err, result) => {
          if (err) {
            console.error("Error updating survey response:", err);
            return res.status(500).json({
              message: "An error occurred while updating survey response.",
            });
          }

          if (result.affectedRows === 0) {
            return res.status(404).json({ message: "Survey response not found." });
          }

          res.json({ message: "Survey response updated successfully" });
        }
      );
    } catch (error) {
      console.error("Unexpected error:", error);
      res.status(500).json({
        message: "An unexpected error occurred.",
      });
    }
  });

  // Delete a survey response
  router.delete("/:id", async (req, res) => {
    const { id } = req.params;

    try {
      pool.query(
        "DELETE FROM customer_survey WHERE id = ?",
        [id],
        (err, result) => {
          if (err) {
            console.error("Error deleting survey response:", err);
            return res.status(500).json({
              message: "An error occurred while deleting survey response.",
            });
          }

          if (result.affectedRows === 0) {
            return res.status(404).json({ message: "Survey response not found." });
          }

          res.json({ message: "Survey response deleted successfully" });
        }
      );
    } catch (error) {
      console.error("Unexpected error:", error);
      res.status(500).json({
        message: "An unexpected error occurred.",
      });
    }
  });

  return router;
};
