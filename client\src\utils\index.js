export { default as ActivityReports } from "../workplan/pages/reports/ActivityReports";
export { default as AllClientsContacts } from "../logistics/pages/clients/AllClientsContacts";
export { default as AllPayments } from "../foundation/pages/Reports/AllPayments";
export { default as AllSiteVisits } from "../logistics/pages/site-visit-requests/AllSiteVisits";
export { default as AppMenu } from "../common/pages/AppMenu";
export { default as ApprovedBookings } from "../logistics/pages/site-visit-requests/ApprovedBookings";
export { default as ApprovedSiteVisitsReports } from "../logistics/pages/reports/ApprovedSiteVisitsReports";
export { default as ApprovedSVDetails } from "../logistics/pages/site-visit-requests/ApprovedSVDetails";
export { default as ApproveWorkplans } from "../workplan/pages/workplans/ApproveWorkplans";
export { default as AssignedBookings } from "../logistics/pages/site-visit-requests/AssignedBookings";
export { default as AssignedSpecialAssignments } from "../logistics/pages/special-assignments/AssignedSpecialAssignments";
export { default as AssignedVehicleRequests } from "../logistics/pages/vehicles/AssignedVehicleRequests";
export { default as ClientsFeedback } from "../logistics/pages/clients/ClientsFeedback";
export { default as CreateBooking } from "../logistics/pages/site-visit-requests/CreateBooking";
export { default as CreateSpecialAssignment } from "../logistics/pages/special-assignments/CreateSpecialAssignment";
export { default as CreateActivity } from "../workplan/pages/activities/CreateActivity";
export { default as CreateVehicle } from "../logistics/pages/vehicles/CreateVehicle";
export { default as CreateWorkPlan } from "../workplan/pages/workplans/CreateWorkPlan";
export { default as Dashboard } from "../logistics/pages/Dashboard";
export { default as DonatedBooks } from "../foundation/pages/Books/DonatedBooks";
export { default as DriverItinerary } from "../logistics/pages/reports/DriverItinerary";
export { default as EditScheduledInterviews } from "../visitors-management/pages/interviews/EditScheduledInterviews";
export { default as EditSiteVisit } from "../logistics/pages/site-visit-requests/EditSiteVisit";
export { default as EditSpecialAssignment } from "../logistics/pages/special-assignments/EditSpecialAssignment";
export { default as EditVehicle } from "../logistics/pages/vehicles/EditVehicle";
export { default as EditVisitor } from "../visitors-management/pages/visitors/EditVisitor";
export { default as Feedback } from "../common/pages/Feedback";
export { default as HighSchoolPayments } from "../foundation/pages/Reports/HighSchoolPayments.js";
export { default as Home } from "../logistics/pages/Home";
export { default as InterviewsReports } from "../visitors-management/pages/reports/InterviewsReports";
export { default as IndividualReports } from "../workplan/pages/reports/IndividualReports";
export { default as Initiatives } from "../foundation/pages/pillars/Initiatives";
export { default as InitiativesReports } from "../foundation/pages/Reports/InitiativesReports";
export { default as Login } from "../common/pages/Login";
export { default as MarketersFeedback } from "../logistics/pages/reports/MarketersFeedback";
export { default as MostBookedSitesReports } from "../logistics/pages/reports/MostBookedSitesReports";
export { default as MyClientContacts } from "../logistics/pages/clients/MyClientContacts";
export { default as MySiteVisits } from "../logistics/pages/site-visit-requests/MySiteVisits";
export { default as Notifications } from "../logistics/pages/Notification";
export { default as PastRequests } from "../logistics/pages/vehicles/PastRequests";
export { default as ProductivityReports } from "../workplan/pages/reports/ProductivityReports";
export { default as Profile } from "../common/pages/Profile";
export { default as PendingWorkplanDetails } from "../workplan/pages/workplans/PendingWorkplanDetails";
export { default as ReserveParking } from "../visitors-management/pages/parking/ReserveParking";
export { default as RegionReports } from "../workplan/pages/reports/RegionReports";
export { default as RegionalManagerRemarks } from "../workplan/pages/RegionalManagerRemarks";
export { default as RegisterVisitor } from "../visitors-management/pages/visitors/RegisterVisitor";
export { default as RequestVehicle } from "../logistics/pages/vehicles/RequestVehicle";
export { default as SalesManagerRemarks } from "../workplan/pages/SalesManagerRemarks";
export { default as ScheduleInterview } from "../visitors-management/pages/interviews/ScheduleInterview";
export { default as SiteVisitDetails } from "../logistics/pages/site-visit-requests/SiteVisitDetails";
export { default as SiteVisitRequests } from "../logistics/pages/site-visit-requests/SiteVisitRequests";
export { default as SiteVisitsSummary } from "../logistics/pages/reports/SiteVisitsSummaryReports";
export { default as Survey } from "../logistics/pages/Survey";
export { default as Users } from "../logistics/pages/Users";
export { default as UpdateActivities } from "../workplan/pages/activities/UpdateActivities";
export { default as VehicleRequestDetails } from "../logistics/pages/vehicles/VehicleRequestDetails";
export { default as VehicleRequests } from "../logistics/pages/vehicles/VehicleRequests";
export { default as ViewDrivers } from "../logistics/pages/drivers/ViewDrivers";
export { default as ViewReservedParking } from "../visitors-management/pages/parking/ViewReservedParking";
export { default as ViewScheduleInterviews } from "../visitors-management/pages/interviews/ViewScheduledInterviews";
export { default as ViewSites } from "../logistics/pages/sites/ViewSites";
export { default as ViewSpecialAssignments } from "../logistics/pages/special-assignments/ViewSpecialAssignments";
export { default as ViewVehicles } from "../logistics/pages/vehicles/ViewVehicles";
export { default as ViewVisitors } from "../visitors-management/pages/visitors/ViewVisitors";
export { default as VisitorsManagementHome } from "../visitors-management/pages/VisitorsManagementHome";
export { default as VisitorsReports } from "../visitors-management/pages/reports/VisitorsReports";
export { default as ViewActivities } from "../workplan/pages/activities/ViewActivities";
export { default as ViewFeedback } from "../common/pages/ViewFeedback";
export { default as ViewWorkPlans } from "../workplan/pages/workplans/ViewWorkPlans";
export { default as WorkPlanDashboard } from "../workplan/pages/WorkPlanDashboard";
export { default as WorkPlanHome } from "../workplan/pages/WorkPlanHome";
export { default as JoyLovers } from "../map/pages/JoyLovers";
export { default as MapHome } from "../map/pages/MapsHome";
export { default as OasisGardens } from "../map/pages/OasisGardens";
export { default as WorkPlanDetails } from "../workplan/pages/workplans/WorkplanDetails";
export { default as KumpaSprings } from "../map/pages/KumpaSprings";
export { default as SuccessGardens } from "../map/pages/SuccessGardens";
export { default as UshindiGardens } from "../map/pages/UshindiGardens";
export { default as HekimaPhase3 } from "../map/pages/HekimaPhase3";
export { default as VipingoRidge } from "../map/pages/VipingoRidge";
export { default as AchieversPark } from "../map/pages/AchieversPark";
export { default as WemaGardens } from "../map/pages/WemaGardens";
export { default as TulivuGardens } from "../map/pages/TulivuPhase3";
export { default as VuyanziGardens } from "../map/pages/VuyanziGardens";
export { default as Malindi7 } from "../map/pages/Malindi7";
export { default as HekimaPhase4 } from "../map/pages/HekimaPhase4";
export { default as VictoryGardens } from "../map/pages/VictoryGardens6.js";
export { default as AchieversPhase2 } from "../map/pages/AchieversPhase2.js";
export { default as KithimaniSprings } from "../map/pages/KithimaniSprings.js";
export { default as Malindi5 } from "../map/pages/Malindi5.js";
export { default as Victory1 } from "../map/pages/Victory1.js";
export { default as Victory2 } from "../map/pages/Victory2.js";
export { default as Victory3 } from "../map/pages/Victory3.js";
export { default as WisdomPhase2 } from "../map/pages/KonzaWisdom.js";
export { default as VipingoPrime } from "../map/pages/VipingoPrime.js";
export { default as Og9 } from "../map/pages/Og9.js";

export { default as WetuGardens } from "../map/pages/WetuGardens";
export { default as Malindi } from "../map/pages/paths/Malindi";
export { default as Nairobi } from "../map/pages/paths/Nairobi";
export { default as Kajiado } from "../map/pages/paths/Kajiado";
export { default as Kiambu } from "../map/pages/paths/Kiambu";
export { default as Nakuru } from "../map/pages/paths/Nakuru";
export { default as Nyeri } from "../map/pages/paths/Nyeri";
export { default as Laikipia } from "../map/pages/paths/Laikipia";
export { default as Kisumu } from "../map/pages/paths/Kisumu";
export { default as Machakos } from "../map/pages/paths/Machakos";
export { default as Eldoret } from "../map/pages/paths/Eldoret";
export { default as Kitale } from "../map/pages/paths/Kitale";
//Foundation
export { default as BookReports } from "../foundation/pages/Reports/BookReports";

export { default as ClearedSale } from "../foundation/pages/Books/ClearedSale";

export { default as DonorReport } from "../foundation/pages/Reports/DonorReport";

export { default as Education } from "../foundation/pages/pillars/education/Education";

export { default as EducationReport } from "../foundation/pages/Reports/EducationReport";

export { default as Environment } from "../foundation/pages/pillars/Environment";

export { default as EnvironmentReport } from "../foundation/pages/Reports/EnvironmentReport";

export { default as EventReport } from "../foundation/pages/Reports/EventReport";

export { default as FoundationHome } from "../foundation/pages/FoundationHome";

export { default as Health } from "../foundation/pages/pillars/Health";

export { default as HealthReport } from "../foundation/pages/Reports/HealthReport";

export { default as HighSchoolSpecific } from "../foundation/pages/Reports/HighSchoolSpecific";

export { default as Issuance } from "../foundation/pages/Books/Issuance";

export { default as Payments } from "../foundation/pages/pillars/education/Payments";

export { default as PovertyAlleviation } from "../foundation/pages/pillars/PovertyAlleviation";

export { default as PovertyReport } from "../foundation/pages/Reports/povertyReport";

export { default as PaymentReports } from "../foundation/pages/Reports/PaymentReports";

export { default as Sales } from "../foundation/pages/Books/Sales";

export { default as Specific } from "../foundation/pages/pillars/education/Specific";

export { default as Store } from "../foundation/pages/Books/Store";

export { default as ViewDonors } from "../foundation/pages/Donors/ViewDonors";

// kpi
export { default as ViewEvents } from "../foundation/pages/Events/ViewEvents";
export { default as CreateTasks } from "../performance- report/pages/dash/CreateTasks";
export { default as AdminDashboard } from "../performance- report/pages/dash/AdminDashboard";
export { default as ViewTasks } from "../performance- report/pages/dash/ViewAssignedTasks";
export { default as MonthlySales } from "../performance- report/pages/forms/MonthlySales";
export { default as ReferralBusiness } from "../performance- report/pages/forms/ReferalBusiness";
export { default as ViewMonthlySales } from "../performance- report/pages/forms/ViewMonthlySales";
export { default as ViewReferal } from "../performance- report/pages/forms/ViewReferal";
export { default as County } from "../performance- report/pages/forms/County";
export { default as RegionalEvent } from "../performance- report/pages/forms/RegionalEvents";
export { default as UserDashboard } from "../performance- report/pages/dash/Dashboard";
export { default as LandingPage } from "../performance- report/pages/dash/Home";
export { default as ViewRegional } from "../performance- report/pages/forms/ViewRegionalEvents";
export { default as MonthlyReports } from "../performance- report/pages/Reports/MonthlySalesReport";
export { default as ReferralReports } from "../performance- report/pages/Reports/ReferralReports";
export { default as RegionalEventReport } from "../performance- report/pages/Reports/EventReport";
export { default as MultiTableReports } from "../performance- report/pages/Reports/ActivationReport";
export { default as ViewCounty } from "../performance- report/pages/forms/ViewCounty";
export { default as SalesDashboard } from "../performance- report/pages/dash/SalesDashboard";
export { default as CreateFow } from "../performance- report/pages/forms/Focus Of The Week/CreateFow";
export { default as ViewFow } from "../performance- report/pages/forms/Focus Of The Week/ViewFow";
export { default as ApproveFow } from "../performance- report/pages/forms/Focus Of The Week/ApproveFow";
//export { default as CustomKanban } from "../performance- report/pages/forms/Focus Of The Week/Kanban";

// value addition
export { default as CreateProject } from "../value-additions/pages/CreateProject";
export { default as CreateSupplier } from "../value-additions/pages/CreateSupplier";
export { default as CreateTask } from "../value-additions/pages/CreateTask";
export { default as CreateValueAddition } from "../value-additions/pages/CreateValueAddition";
export { default as ValueHome } from "../value-additions/pages/Home";
export { default as EditProject } from "../value-additions/pages/EditProject";
export { default as EditSupplier } from "../value-additions/pages/EditSupplier";
//export { default as Login } from "../value-additions/pages/Login";
export { default as ProjectInfo } from "../value-additions/pages/ProjectInfo";
export { default as Projects } from "../value-additions/pages/Projects";
//export { default as Register } from "../value-additions/pages/Register";
export { default as ValueAdditionDetails } from "../value-additions/pages/ValueAdditionDetails";
export { default as ViewSuppliers } from "../value-additions/pages/ViewSuppliers";
export { default as EditTask } from "../value-additions/pages/EditTask";
export { default as CreateLabour } from "../value-additions/pages/CreateLabour";
export { default as CreateCasualLabourers } from "../value-additions/pages/CreateCasualLabourers";
export { default as ViewCasualLabourers } from "../value-additions/pages/ViewCasualLabourers";
export { default as ViewLabour } from "../value-additions/pages/ViewLabour";
export { default as EditValueAddition } from "../value-additions/pages/EditValueAddition";
export { default as CreateConstructionManager } from "../value-additions/pages/CreateConstructionManagers";
export { default as EditCasualLabourer } from "../value-additions/pages/EditCasualLabourer";
export { default as ViewConstructionManagers } from "../value-additions/pages/ViewConstructionManagers";
export { default as CreateRequisition } from "../value-additions/pages/CreateRequisition";
export { default as ViewRequisition } from "../value-additions/pages/ViewRequisition";
export { default as ApproveRequisition } from "../value-additions/pages/ApproveRequisitions";
export { default as ApprovedRequisitions } from "../value-additions/pages/ApprovedRequisitions";
export { default as CreatePayments } from "../value-additions/pages/CreatePayments";
export { default as ViewPayments } from "../value-additions/pages/ViewPayments";
export { default as FilterPayments } from "../value-additions/pages/FilterPayments";
export { default as EditConstructionManagers } from "../value-additions/pages/EditConstructionManagers";
export { default as EditLabour } from "../value-additions/pages/EditLabour";
export { default as ValueAdditionReports } from "../value-additions/pages/ValueAdditionReports";
export { default as ViewValueAdditions } from "../value-additions/pages/ViewValueAdditions";
export { default as UpdatePayments } from "../value-additions/pages/UpdatePayments";
export { default as ArchiveCasualLabourers } from "../value-additions/pages/ArchiveCasualLabourers";
export { default as Summary } from "../value-additions/pages/Summary";
export { default as CreateSupplierPayments } from "../value-additions/pages/CreateSupplierPayments";
export { default as ViewSupplierPayments } from "../value-additions/pages/ViewSupplierPayments";
export { default as MaintenanceFilterPayments } from "../value-additions/pages/MaintenanceFilterPayments.js";
export { default as CasualLabourerPaymentReports } from "../value-additions/pages/CasualLabourerPaymentReports";
export { default as SupplierReport } from "../value-additions/pages/SupplierReport";
export { default as ValueAdditionPaymentUpdates } from "../value-additions/pages/ValueAdditionPaymentUpdates";
export { default as ViewMaintenancePayments } from "../value-additions/pages/ViewMaintenancePayments";
export { default as ViewValueAdditionPayments } from "../value-additions/pages/ViewValueAdditionPayments";
export { default as ValueAdditionFilterPayments } from "../value-additions/pages/ValueAdditionFilterPayments";
export { default as CreateUsers } from "../value-additions/pages/CreateUsers.js";
export { default as ViewUsers } from "../value-additions/pages/ViewUsers.js";
export { default as EditUsers } from "../value-additions/pages/EditUsers.js";
export { default as MaintenanceSupplies } from "../value-additions/pages/MaintenanceSupplies.js";
export { default as ViewMaintenanceSupply } from "../value-additions/pages/ViewMaintenanceSupply";
export { default as ViewMaintenanceSuppliers } from "../value-additions/pages/ViewMaintenanceSuppliers.js";

// Optiven Homes
export { default as OptivenHomes } from "../Optiven-Homes/pages/Landing-Page.js";
export { default as HomesDash } from "../Optiven-Homes/pages/clients/Homes-Dash.js";
export { default as ClientDetails } from "../Optiven-Homes/pages/clients/ClientDetails.js";
export { default as ConstructionPhasePage } from "../Optiven-Homes/pages/ConstructionPhase.js";
export { default as ConstClients } from "../Optiven-Homes/pages/clients/ConstClients.js";
export { default as DesignClients } from "../Optiven-Homes/pages/clients/DesignClients.js";
export { default as TerminatedClients } from "../Optiven-Homes/pages/clients/TerminatedClients.js";
export { default as UploadDesign } from "../Optiven-Homes/pages/docs/UploadDesign.js";
export { default as ViewDesign } from "../Optiven-Homes/pages/docs/ViewDesign.js";
export { default as UploadConstruction } from "../Optiven-Homes/pages/docs/UploadConstruction.js";
export { default as ViewConstruction } from "../Optiven-Homes/pages/docs/ViewConstruction.js";
export { default as UploadImages } from "../Optiven-Homes/pages/docs/UploadImages.js";
export { default as ViewImages } from "../Optiven-Homes/pages/docs/ViewImages.js";
export { default as ClientReports } from "../Optiven-Homes/pages/Reports/ClientReports.js";
export { default as ClientNotification } from "../Optiven-Homes/pages/ClientNotification.js";
export { default as ViewConstProgress } from "../Optiven-Homes/pages/ViewConstProgress.js";
// booking/conference
// booking/conference
export { default as BookingForm } from "../visitors-management/pages/booking/BookingForm.js";
export { default as ConferenceRooms } from "../visitors-management/pages/booking/ConferenceRooms.js";
export { default as RoomSchedule } from "../visitors-management/pages/booking/RoomSchedule.js";
export { default as RoomBookingForm } from "../visitors-management/pages/booking/RoomBooking.js";
export { default as ConferenceRoom } from "../visitors-management/pages/booking/Conference.js";
export { default as AllRoomSchedule } from "../visitors-management/pages/booking/AllRoomsSchedule.js";
export { default as EventBooking } from "../visitors-management/pages/booking/EventScheduling.js";
export { default as BookingSummary } from "../visitors-management/pages/booking/BookingSummary.js";
// system survey
export { default as SystemSurvey } from "../SystemsSurvey/pages/SystemSurvey.js";
export { default as ViewSurvey } from "../SystemsSurvey/pages/ViewSurvey.js";
export { default as SurveyReports } from "../SystemsSurvey/pages/SurveyReports.js";

export { default as SurveyDashboard } from "../SystemsSurvey/pages/SurveyDashboard.js";
export { default as CustomerSurvey } from "../SystemsSurvey/pages/TeleMarketersSurvey.js";
export { default as ViewCustomerSurvey } from "../SystemsSurvey/pages/CustomerSurveyReports.js";

// wORK dIARY
export { default as WorkDiary } from "../work-diary/components/WorkDiaryHero.js";

// calendar page
export { default as MyCalendar } from "../work-diary/components/Calendar.js";

// dashboard page
export { default as DashboardWorkDiary } from "../work-diary/components/MyDashBoard.js";

// work diary page
export { default as WorkDiaryEntry } from "../work-diary/components/WorkDiaryEntry.js";

// dashboard page
export { default as ReportsSection } from "../work-diary/components/ReportsSection.js";

// Supervisor Dashboard page
export { default as SupervisorDashboard } from "../work-diary/components/SupervisorDashboard";

// HR Admin Dashboard page
export { default as HRAdminDashboard } from "../work-diary/components/HRAdminDashboard";

// Loyalty
export { default as LoyaltyHome } from "../loyalty/pages/LoyaltyHome.jsx";
export { default as ViewCustomers } from "../loyalty/pages/ViewCustomers.jsx";
export { default as CustomerDetails } from "../loyalty/pages/CustomerDetails.jsx";
export { default as CustomerReports } from "../loyalty/pages/CustomerReports.jsx";

// Referrals
export { default as ReferralForm } from "../optiven-connect/pages/ReferralForm.js";

// Discharge
export { default as DischargeApprovalsPage } from "../Discharge Reconciliation/pages/DischargeApprovalPage.js";
export { default as DischargeInitiated } from "../Discharge Reconciliation/pages/DischargeInitiated.js";
export { default as PlotDetails } from "../Discharge Reconciliation/pages/PlotDetails.js";
export { default as PlotDischarge } from "../Discharge Reconciliation/pages/PlotDischarge.js";
export { default as PlotsPage } from "../Discharge Reconciliation/pages/PlotsPage.js";
export { default as ProjectsDashboard } from "../Discharge Reconciliation/pages/ProjectsDashboard";
export { default as TransactionsPage } from "../Discharge Reconciliation/pages/TransactionPage";
export { default as KBNgong } from "../map/pages/KBNgong.js"