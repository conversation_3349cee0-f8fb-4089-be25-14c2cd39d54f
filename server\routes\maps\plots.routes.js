const express = require("express");
const router = express.Router();

module.exports = (pool) => {
  const queryDatabase = async (sql, params, res) => {
    try {
      const [results] = await pool.query(sql, params);
      return results;
    } catch (error) {
      console.error(error);
      res.status(500).json({
        message: "An error occurred while fetching data.",
      });
    }
  };

  router.get("/", async (req, res) => {
    const sql = "SELECT * FROM `crm2.0`.inventory_plot";
    const results = await queryDatabase(sql, [], res);
    res.json(results);
  });

  router.get("/:project_id", async (req, res) => {
    const { project_id } = req.params;
    const sql = "SELECT * FROM `crm2.0`.inventory_plot WHERE project_id = ?";
    const results = await queryDatabase(sql, [project_id], res);
    res.json(results);
  });

  // Counter
  const calculateSoldPlotsCount = (results) => {
    return results.filter((plot) => plot.plot_status === "Sold").length;
  };

  const calculateReservedPlotsCount = (results) => {
    return results.filter((plot) => plot.plot_status === "Reserved").length;
  };

  const calculateOpenPlotsCount = (results) => {
    return results.filter((plot) => plot.plot_status === "Open").length;
  };
  router.get("/all/:project_id", async (req, res) => {
    const { project_id } = req.params;
    const sql = "SELECT * FROM `crm2.0`.inventory_plot WHERE project_id = ?";
    const results = await queryDatabase(sql, [project_id], res);

    const soldPlotsCount = calculateSoldPlotsCount(results);
    const reservedPlotsCount = calculateReservedPlotsCount(results);
    const openPlotsCount = calculateOpenPlotsCount(results);

    res.json({
      soldPlotsCount,
      reservedPlotsCount,
      openPlotsCount,
    });
  });

  router.get("/:plot_no", async (req, res) => {
    const { plot_no } = req.params;
    const sql = "SELECT * FROM `crm2.0`.inventory_plot WHERE plot_no = ?";
    const results = await queryDatabase(sql, [plot_no], res);
    res.json(results);
  });

  return router;
};
