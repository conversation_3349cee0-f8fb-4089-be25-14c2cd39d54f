const express = require("express");
const router = express.Router();
const pdfMakePrinter = require("pdfmake/src/printer");
const authenticateJWT = require("../../middleware/authenticateJWT");
function formatDate(inputDate) {
  const date = new Date(inputDate);

  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0"); // Months are zero-based
  const day = date.getDate().toString().padStart(2, "0");

  return `${year}-${month}-${day}`;
}
const fonts = {
  Roboto: {
    normal: "node_modules/roboto-font/fonts/Roboto/roboto-regular-webfont.ttf",
    bold: "node_modules/roboto-font/fonts/Roboto/roboto-bold-webfont.ttf",
    italic: "node_modules/roboto-font/fonts/Roboto/roboto-italic-webfont.ttf",
    bolditalics:
      "node_modules/roboto-font/fonts/Roboto/roboto-bolditalic-webfont.ttf",
  },
};
const printer = new pdfMakePrinter(fonts);

// Function to format data into PDF rows
function dataToPdfRows(data) {
  const pdfRows = [];

  // Iterate over the data and map it to rows
  let totalPayments = 0; // Initialize total budget

  data.forEach((item) => {
    const paymentBudget = parseFloat(item.payment_total_amount); // Convert to float if necessary
    if (!isNaN(paymentBudget)) {
        totalPayments += paymentBudget; // Add task actual budget to total

        pdfRows.push([
            { text: item.project_name, style: "tableCell" },
            { text: item.payment_name, style: "tableCell" },
            { text: item.payment_phone.toString(), style: "tableCell" },
            {
                text: formatDate(item.payment_start_date.toString()),
                style: "tableCell",
            },
            {
                text: formatDate(item.payment_end_date.toString()),
                style: "tableCell",
            },
            { text: item.payment_payment_type, style: "tableCell" },
            { text: item.payment_work_days, style: "tableCell" },
            { text: item.payment_total_amount, style: "tableCell" },
        ]);
    }
});

pdfRows.push([
    // Add a row for the total budget at the end
    { text: "Total Payments", style: "tableCell", colSpan: 6 },
    {},
    {},
    {},
    {},
    {},
    {}, // Empty cells for formatting
    { text: totalPayments, style: "tableCell" }, // Display total budget
]);

return pdfRows;
}

module.exports = (pool) => {
  router.post("/",authenticateJWT, (req, res) => {
    try {
      const { start_date, end_date,payment_type } = req.body;

      let employeeData = {};
      // Automatically filled employee data
      const {
        name,
        phone,
        id_number,
        rate_per_day,
        work_description,
        project_id,
      } = req.body.employee;
      employeeData = {
        name,
        phone,
        id_number,
        rate_per_day,
        work_description,
        project_id,
      };

      // Insert the data into the payments table
      pool.query(
        "INSERT INTO payments (name, phone, id_number,rate_per_day, project_data,work_description, start_date, end_date,payment_type) VALUES (?, ?, ?, ?, ?, ?,?,?,?)",
        [
          employeeData.name,
          employeeData.phone,
          employeeData.id_number,
          employeeData.rate_per_day || null,
          employeeData.project_id,
          employeeData.work_description,
          start_date,
          end_date,
          payment_type,
        ],
        (error) => {
          if (error) {
            console.error(error);
            return res.status(500).json({ message: "Internal Server Error" });
          }

          res.status(201).json({ message: "Data posted successfully" });
        }
      );
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Internal Server Error" });
    }
  });
  // get data for maintenance payments
  router.get("/casual-labourers-payments",authenticateJWT, (req, res) => {
    try {
      // Fetch payments that have not been updated for the particular date
      // and also fall within the date range between the current date and end date
      pool.query(
        "SELECT * FROM payments WHERE NOT EXISTS (SELECT 1 FROM payments AS p2 WHERE p2.updated_at >= CURDATE() AND p2.id = payments.id) AND (CURDATE() BETWEEN start_date AND end_date) AND payment_type ='maintenance'",
        (err, result) => {
          if (err) {
            console.error(err);
            return res.status(500).json({ message: "Error fetching data" });
          }
          res.status(200).json(result);
        }
      );
    } catch (error) {
      console.error(error);
      res
        .status(500)
        .json({ message: "Error generating date range or fetching data" });
    }
  });
  //get data for value addition payments
  router.get("/casual-labourers-value-addition-payments",authenticateJWT, (req, res) => {
    try {
      // Fetch payments that have not been updated for the particular date
      // and also fall within the date range between the current date and end date
      pool.query(
        "SELECT * FROM payments WHERE NOT EXISTS (SELECT 1 FROM payments AS p2 WHERE p2.updated_at >= CURDATE() AND p2.id = payments.id) AND (CURDATE() BETWEEN start_date AND end_date) AND payment_type ='value addition'",
        (err, result) => {
          if (err) {
            console.error(err);
            return res.status(500).json({ message: "Error fetching data" });
          }
          res.status(200).json(result);
        }
      );
    } catch (error) {
      console.error(error);
      res
        .status(500)
        .json({ message: "Error generating date range or fetching data" });
    }
  });
  // filter only maintenance payments on date inputted
  router.get("/maintenance-filter",authenticateJWT, (req, res) => {
    try {
      const { date } = req.query;
  
      if (!date) {
        return res.status(400).json({ message: "Missing 'date' parameter" });
      }
  
      pool.query(
        "SELECT * FROM payments WHERE DATE(date) = DATE(?) AND payment_type = 'maintenance' ORDER BY project_data DESC",
        [date],
        (err, result) => {
          if (err) {
            console.error(err);
            return res.status(500).json({ message: "Internal Server Error" });
          }
          res.json(result);
        }
      );
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Internal Server Error" });
    }
  });
   // filter only value-addition payments on date inputted
   router.get("/value-addition-filter",authenticateJWT, (req, res) => {
    try {
      const { date } = req.query;
  
      if (!date) {
        return res.status(400).json({ message: "Missing 'date' parameter" });
      }
  
      pool.query(
        "SELECT * FROM payments WHERE DATE(date) = DATE(?) AND payment_type = 'maintenance' ORDER BY project_data DESC",
        [date],
        (err, result) => {
          if (err) {
            console.error(err);
            return res.status(500).json({ message: "Internal Server Error" });
          }
          res.json(result);
        }
      );
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Internal Server Error" });
    }
  });
  //filter maintenance by date range
  router.get("/maintenance-filter-range", authenticateJWT, (req, res) => {
    try {
      const { startDate, endDate } = req.query;
  
      if (!startDate || !endDate) {
        return res
          .status(400)
          .json({ message: "Missing 'startDate' or 'endDate' parameter" });
      }
  
      if (new Date(startDate) > new Date(endDate)) {
        return res
          .status(400)
          .json({ message: "'startDate' cannot be after 'endDate'" });
      }
  
      pool.query(
        "SELECT * FROM payments WHERE DATE(date) BETWEEN DATE(?) AND DATE(?) AND payment_type='maintenance' ORDER BY project_data DESC",
        [startDate, endDate],
        (err, result) => {
          if (err) {
            console.error(err);
            return res.status(500).json({ message: "Internal Server Error" });
          }
          res.json(result);
        }
      );
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Internal Server Error" });
    }
  });
   //filter value-addition by date range
   router.get("/value-addition-filter-range", authenticateJWT, (req, res) => {
    try {
      const { startDate, endDate } = req.query;
  
      if (!startDate || !endDate) {
        return res
          .status(400)
          .json({ message: "Missing 'startDate' or 'endDate' parameter" });
      }
  
      if (new Date(startDate) > new Date(endDate)) {
        return res
          .status(400)
          .json({ message: "'startDate' cannot be after 'endDate'" });
      }
  
      pool.query(
        "SELECT * FROM payments WHERE DATE(date) BETWEEN DATE(?) AND DATE(?) AND payment_type='value addition' ORDER BY project_data DESC",
        [startDate, endDate],
        (err, result) => {
          if (err) {
            console.error(err);
            return res.status(500).json({ message: "Internal Server Error" });
          }
          res.json(result);
        }
      );
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Internal Server Error" });
    }
  });
  
  // filter payments depending on the date inputted
  router.get("/filter",authenticateJWT, (req, res) => {
    try {
      const { date } = req.query;

      if (!date) {
        return res.status(400).json({ message: "Missing 'date' parameter" });
      }

      pool.query(
        "SELECT * FROM payments WHERE DATE(date) = DATE(?) ORDER BY project_data DESC",
        [date],
        (err, result) => {
          if (err) {
            console.error(err);
            return res.status(500).json({ message: "Internal Server Error" });
          }
          res.json(result);
        }
      );
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Internal Server Error" });
    }
  });

  // get route by  having a date range
  router.get("/filter-range",authenticateJWT, (req, res) => {
    try {
      const { startDate, endDate } = req.query;

      if (!startDate || !endDate) {
        return res
          .status(400)
          .json({ message: "Missing 'startDate' or 'endDate' parameter" });
      }

      pool.query(
        "SELECT * FROM payments WHERE DATE(date) BETWEEN DATE(?) AND DATE(?) ORDER BY project_data DESC",
        [startDate, endDate],
        (err, result) => {
          if (err) {
            console.error(err);
            return res.status(500).json({ message: "Internal Server Error" });
          }
          res.json(result);
        }
      );
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Internal Server Error" });
    }
  });
// get reports for payments
router.get("/report",authenticateJWT, (req, res) => {
  let vaQuery = `SELECT 
      py.name AS payment_name,
      py.phone AS payment_phone,
      py.start_date AS payment_start_date,
      py.end_date  AS payment_end_date,
      py.payment_type  AS payment_payment_type,
      py.work_days AS payment_work_days,
      py.total_amount AS payment_total_amount,
      p.project_name AS project_name 
      FROM payments py 
      LEFT JOIN projects p ON py.project_data = p.id`;

  const { start_date,end_date } = req.query;

  if (start_date,end_date) {
    // Filter by start_date,end_date if provided
    vaQuery += ` WHERE p.start_date,end_date = '${start_date,end_date}'`;
  }

  try {
    // Execute the SQL query
    pool.query(vaQuery, (err, result) => {
      if (err) {
        console.error(err);
        res
          .status(500)
          .json({ error: "An error occurred while fetching data." });
      } else {
        // Define the document definition for the PDF
        const docDefinition = {
          pageSize: "A4",
          pageOrientation: "landscape",
          content: [
            {
              table: {
                headerRows: 1,
                widths: ["*", "*", "*", "*", "*", "*", "*","*"], // Adjust column widths as needed
                body: [
                  [
                    { text: "Project Name", style: "tableHeader" },
                    { text: "Casual Labourer Name", style: "tableHeader" },
                    { text: "Casual Labourer Contact", style: "tableHeader" },
                    {
                      text: "Work start Date",
                      style: "tableHeader",
                    },
                    { text: "Work End Date", style: "tableHeader" },
                    { text: "Category", style: "tableHeader" },
                    { text: "Number of work Days", style: "tableHeader" },
                    { text: "Total paid", style: "tableHeader" },
                   
                  ],
                  ...dataToPdfRows(result), // Add data rows
                ],
              },
            },
          ],
          styles: {
            tableHeader: {
              bold: true,
              fontSize: 12,
              fillColor: "#CCCCCC", // Header background color
            },
            tableCell: {
              fontSize: 10,
            },
          },
        };

        // Create the PDF document using pdfmake
        const pdfDoc = printer.createPdfKitDocument(docDefinition);

        // Set the response headers to indicate a PDF file
        res.setHeader("Content-Type", "application/pdf");

        // Stream the PDF document as the response
        pdfDoc.pipe(res);
        pdfDoc.end();
      }
    });
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ error: "An error occurred while processing the request." });
  }
});
//get payments for maintenance payment type
router.get("/maintenance-payments",authenticateJWT,(req, res) => {
  try {
    pool.query(
      "SELECT * FROM payments WHERE payment_type = 'maintenance' ORDER BY updated_at DESC",
      (err, result) => {
        if (err) {
          console.error(err);
          return res.status(500).json({ message: "Internal Server Error" });
        }
        res.status(200).json(result);
      }
    );
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Internal Server Error" });
  }
});
//get payments for value addition payment type
router.get("/value-addition-payments",authenticateJWT,(req, res) => {
  try {
    pool.query(
      "SELECT * FROM payments WHERE payment_type = 'value addition' ORDER BY updated_at DESC",
      (err, result) => {
        if (err) {
          console.error(err);
          return res.status(500).json({ message: "Internal Server Error" });
        }
        res.status(200).json(result);
      }
    );
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Internal Server Error" });
  }
});

  //   get all payments
  router.get("/",authenticateJWT, (req, res) => {
    try {
      pool.query(
        "SELECT * FROM payments  ORDER BY updated_at DESC",
        (err, result) => {
          if (err) throw err;
          res.status(200).json(result);
        }
      );
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Internal Server Error" });
    }
  });
  // update payments where payment type is maintenance
  router.patch("/:id/updatePay",authenticateJWT, (req, res) => {
    const { id } = req.params;
    const { work_days, total_amount } = req.body;

    try {
      // Check if an update has already been made for today's date
      pool.query(
        "SELECT * FROM payments WHERE id = ? AND DATE(updated_at) = CURDATE()",
        [id],
        (error, results) => {
          if (error) {
            console.error("Error checking last update date:", error);
            res.status(500).json({ message: "Internal server error" });
          } else {
            if (results.length > 0) {
              // An update has already been made for today's date
              res
                .status(400)
                .json({ message: "Update already made for today's date" });
            } else {
              // No update has been made for today's date, proceed with the update
              pool.query(
                "UPDATE payments SET work_days = ?, total_amount = ?, updated_at = NOW() WHERE id = ?",
                [work_days, total_amount, id],
                (error, results) => {
                  if (error) {
                    console.error(
                      "Error updating work days and total amount:",
                      error
                    );
                    res.status(500).json({ message: "Internal server error" });
                  } else {
                    res
                      .status(200)
                      .json({
                        message:
                          "Work days and total amount updated successfully",
                      });
                  }
                }
              );
            }
          }
        }
      );
    } catch (error) {
      console.error("Error updating work days and total amount:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });
  router.delete("/:id",authenticateJWT,(req, res) => {
    try {
      const { id } = req.params;
      pool.query("DELETE FROM payments WHERE id = ?", [id], (err, result) => {
        if (err) throw err;
        res.status(200).json({ message: "Payment deleted successfully" });
      });
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Internal server error" });
    }
  });
  

  return router;
};
