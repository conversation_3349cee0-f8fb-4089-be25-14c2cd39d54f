const express = require("express");
const router = express.Router();
const multer = require("multer");
const path = require("path");
const fs = require("fs");

// Define the uploads directory relative to the project root
const uploadsDir = path.join(__dirname, "..", "..", "uploads");

// Multer config for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + "-" + file.originalname);
  },
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB size limit
  },
});

// Create 'uploads' directory if it doesn't exist
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir);
}

module.exports = (pool) => {
  // Serve static files from the 'uploads' directory
  router.use("/uploads", express.static(uploadsDir));

  // Upload a design contract
  router.post(
    "/upload/design-contract",
    upload.single("designContract"),
    (req, res) => {
      if (req.file) {
        let sql =
          "INSERT INTO client_docs (client_id, doc_type, file_path, upload_date) VALUES (?, 'design_contract', ?, NOW())";
        pool.query(
          sql,
          [req.body.client_id, req.file.filename],
          (err, result) => {
            if (err) {
              return res.status(500).send(err);
            }
            res.send("File uploaded successfully!");
          }
        );
      } else {
        res.status(400).send("No file uploaded");
      }
    }
  );

  // Upload a construction contract
  router.post(
    "/upload/construction-contract",
    upload.single("constructionContract"),
    (req, res) => {
      if (req.file) {
        let sql =
          "INSERT INTO client_docs (client_id, doc_type, file_path, upload_date) VALUES (?, 'construction_contract', ?, NOW())";
        pool.query(
          sql,
          [req.body.client_id, req.file.filename],
          (err, result) => {
            if (err) {
              return res.status(500).send(err);
            }
            res.send("File uploaded successfully!");
          }
        );
      } else {
        res.status(400).send("No file uploaded");
      }
    }
  );

  // Upload multiple images
  router.post(
    "/upload/images",
    upload.array("images", 10), // Up to 10 images
    (req, res) => {
      if (req.files) {
        const client_id = req.body.client_id;
        const files = req.files;
        let sql =
          "INSERT INTO client_docs (client_id, doc_type, file_path, upload_date) VALUES ?";
        const values = files.map((file) => [
          client_id,
          "image",
          file.filename,
          new Date(),
        ]);

        pool.query(sql, [values], (err, result) => {
          if (err) {
            return res.status(500).send(err);
          }
          res.send("Images uploaded successfully!");
        });
      } else {
        res.status(400).send("No images uploaded");
      }
    }
  );

  // Fetch documents for a specific client
  router.get("/client/:client_id/documents", (req, res) => {
    const client_id = req.params.client_id;
    const doc_type = req.query.doc_type || "%"; // Default to any doc_type if not provided
    let sql =
      "SELECT id, client_id, doc_type, CONCAT('/uploads/', file_path) AS file_path, upload_date FROM client_docs WHERE client_id = ? AND doc_type LIKE ?";
    pool.query(sql, [client_id, doc_type], (err, results) => {
      if (err) {
        return res.status(500).send(err);
      }
      res.json(results);
    });
  });

  // Route to download a document
  router.get("/download/:filename", (req, res) => {
    const filename = req.params.filename;
    const filePath = path.join(uploadsDir, filename);

    res.download(filePath, (err) => {
      if (err) {
        console.error("Error downloading file:", err);
        return res.status(500).send(err);
      }
    });
  });

  // Delete a document
  router.delete("/delete/document/:doc_id", (req, res) => {
    const doc_id = req.params.doc_id;
    let sql = "SELECT file_path FROM client_docs WHERE id = ?";
    pool.query(sql, [doc_id], (err, results) => {
      if (err) {
        return res.status(500).send(err);
      }
      if (results.length > 0) {
        const filePath = path.join(uploadsDir, results[0].file_path);
        fs.unlink(filePath, (err) => {
          if (err) {
            return res.status(500).send(err);
          }
          let deleteSql = "DELETE FROM client_docs WHERE id = ?";
          pool.query(deleteSql, [doc_id], (err, result) => {
            if (err) {
              return res.status(500).send(err);
            }
            res.send("Document deleted successfully!");
          });
        });
      } else {
        res.status(404).send("Document not found");
      }
    });
  });

  return router;
};
