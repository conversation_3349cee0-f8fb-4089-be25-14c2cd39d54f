const express = require("express");
const router = express.Router();
const authenticateJWT = require("../../middleware/authenticateJWT");

// get all construction managers
module.exports = (pool) => {
  router.get("/",authenticateJWT, (req, res) => {
    try {
      pool.query(
        "SELECT * FROM construction_manager ORDER BY  constructor_name ASC",
        (err, results) => {
          if (err) throw err;
          res.json(results);
        }
      );
    } catch (err) {
      console.error(err);
      res.status(500).json({
        message: "Error in fetching construction managers",
      });
    }
  });
  //   post construction managers
  router.post("/",authenticateJWT, (req, res) => {
    const { constructor_name, email, phone } = req.body;

    try {
      pool.query(
        "INSERT INTO construction_manager(constructor_name, email, phone) VALUES (?, ?, ?)",
        [constructor_name, email, phone],
        (err, result) => {
          if (err) {
            console.error(err);
            return res.status(500).json({
              message: "Error in posting construction manager data",
            });
          }

          const newConstructionManager = {
            id: result.insertId,
            constructor_name,
            email,
            phone,
            
          };

          res.status(201).json({
            newConstructionManager,
            message: "Construction manager added successfully",
          });
        }
      );
    } catch (error) {
      console.error(error);
      res.status(500).json({
        message: "Error in posting construction manager data",
      });
    }
  });
  // get single construction manager by id
  router.get('/:id', authenticateJWT,(req, res) => {
    const { id } = req.params;
    try {
      pool.query(
        "SELECT * FROM construction_manager WHERE id = ?",
        id,
        (err, results) => {
          if (err) {
            throw err;
          }
          // res.status(200).json({ results, message: "Construction manager fetched Successfully" });
          res.json(results[0])
        }
      );
    } catch (err) {
      res.status(500).json({ message: "Error in fetching construction Manager" });
    }
  });
  
  // update construction managers
  router.patch('/:id',authenticateJWT,(req,res) =>{
    const{constructor_name,email,phone} = req.body
    const {id} =req.params
    try{
      const result = pool.query("UPDATE construction_manager SET constructor_name=?, email=?, phone=? WHERE id =?",
      [constructor_name,email,phone,id]
      )
      if(result.affectedRows === 0){
        res.status(404).json({message:"Construction manager not found"})
      }
      res.status(200).json({message:"Construction manage updated successfully"})
      

    }catch(error){
      console.error(error)
      res.status(500).json({
        message:"Error updating construction managers"
      })

    }
  })
  // delete construction manager
   router.delete('/:id',authenticateJWT,(req,res) =>{
    try{
     const result = pool.query(
        'DELETE FROM construction_manager WHERE id =?',[req.params.id])
        if(result.affectedRows === 0){
          res.status(404).json({message:"Construction manager not found"})
        }
        res.status(200).json({message:"Construction manage updated successfully"})
      
      

    }catch(error){
      console.error
      res.status(500).json({message:"Construction Manager not deleted"})
    }
   })
  return router
};
