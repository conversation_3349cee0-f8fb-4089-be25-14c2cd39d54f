const express = require("express");
const router = express.Router();

module.exports = (pool) => {
  // GET combined values for radar chart
  router.get("/", (req, res) => {
    const query = `
      SELECT 
          m.team,
          m.high_value_plots,
          m.affordable_plots,
          m.total_sales,
          r.total_referrals,
          r.sales_achieved,
          r.site_visits
      FROM monthly_sales m
      INNER JOIN referrals r
       ON m.team = r.team`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        res.json(results);
      }
    });
  });

  // Other routes for updating, deleting, etc., can be added similarly

  return router;
};
