const express = require("express");
const router = express.Router();
const ExcelJS = require('exceljs');
const authenticateJWT = require("../../middleware/authenticateJWT");
module.exports = (pool) => {
  // post a requisitions
  router.post("/", authenticateJWT,async (req, res) => {
    const { commodity_name, quantity, description, status, caretaker_id } =
      req.body;
    try {
      pool.query(
        "INSERT INTO requisitions(commodity_name, quantity, description,status, caretaker_id) VALUES (?, ?, ?,?,?)",
        [commodity_name, quantity, description, status, caretaker_id]
      );
      res.status(200).json({ message: "Requisition posted successfully" });
    } catch (error) {
      console.error("Error posting requisitions:", error);
      res.status(500).json({ message: "Error posting requisitions" });
    }
  });
   // get those with status pending
  router.get("/pending", authenticateJWT,async (req, res) => {
    try {
      pool.query("SELECT * FROM `requisitions` WHERE status = 'Pending'", (err, result) => {
        if (err) throw err;
  
        res.status(200).json(result);
      });
    } catch (error) {
      console.error("Error in fetching requisitions", error);
      res.status(500).json({ message: "Error fetching requisitions" });
    }
  });
  // get requisitions that have been approved so as to be accessed by the operations team
  router.get("/approved",authenticateJWT,(req,res) =>{
    try {
      pool.query("SELECT * FROM `requisitions` WHERE status = 'Approved'", (err, result) => {
        if (err) throw err;
  
        res.status(200).json(result);
      });
    } catch (error) {
      console.error("Error in fetching requisitions", error);
      res.status(500).json({ message: "Error fetching requisitions" });
    }
  })
  // get requisitions
  router.get("/all", authenticateJWT,async (req, res) => {
    try {
      pool.query("SELECT * FROM requisitions", (err, result) => {
        if (err) throw err;

        res.status(200).json(result);
      });
    } catch (error) {
      console.error("Error in fetching requisitions", error);
      res.status(500).json({ message: "Error fetching requisitions" });
    }
  });
  //   get requisition for the logged in user
  router.get("/",authenticateJWT, (req, res) => {
    const { user_id } = req.query;
    const query = "SELECT * FROM requisitions WHERE caretaker_id = ? ";
    pool.query(query, [user_id], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        res.json(results);
      }
    });
  });
  // get single requisition
  router.get("/:id",authenticateJWT, (req, res) => {
    const { id } = req.params;
    try {
      pool.query(
        "SELECT * FROM requisitions WHERE id =?",
        [id],
        (err, result) => {
          if (err) throw err;
          if (result.affectedRows === 0) {
            res.status(400).json({ message: "Requisition not found" });
          } else {
            res.status(200).json(result);
          }
        }
      );
    } catch (error) {
      console.error("Error in fetching requisition", error);
      res.status(500).json({ message: "Error in fetching requisition" });
    }
  });
  // get route for downloading requisitions in excel
 

router.get("/excel", authenticateJWT,async (req, res) => {
  try {
    const result = await pool.query("SELECT * FROM `requisitions` WHERE status = 'Approved'");
    
    // Create a new workbook and add a worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Approved Requisitions');
    
    // Add header row
    const headerRow = worksheet.addRow(Object.keys(result[0]));

    // Add data rows
    result.forEach(row => {
      const dataRow = worksheet.addRow(Object.values(row));
    });

    // Set content type and disposition including a filename
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=approved_requisitions.xlsx');

    // Write the workbook to the response
    await workbook.xlsx.write(res);

    // End the response
    res.end();
  } catch (error) {
    console.error("Error in fetching and exporting requisitions", error);
    res.status(500).json({ message: "Error fetching and exporting requisitions" });
  }
});



  // update route for approval
  router.put("/:id/pending",authenticateJWT, (req, res) => {
    const { id } = req.params;

    try {
      pool.query(
        "UPDATE requisitions SET status = 'Approved' WHERE id = ?",
        [id],
        (err, result) => {
          if (err) {
            console.error(err);
            res
              .status(500)
              .json({ message: "Error in updating requisition status" });
          } else {
            if (result.affectedRows > 0) {
              res.status(200).json({ message: "Requisition Approved" });
            } else {
              res.status(404).json({ message: "Requisition not found" });
            }
          }
        }
      );
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Error in approving requisition" });
    }
  });
  // reject route
  router.put("/:id/reject",authenticateJWT, (req, res) => {
    const { id } = req.params;

    try {
      pool.query(
        "UPDATE requisitions SET status = 'Rejected' WHERE id = ?",
        [id],
        (err, result) => {
          if (err) {
            console.error(err);
            res
              .status(500)
              .json({ message: "Error in updating requisition status" });
          } else {
            if (result.affectedRows > 0) {
              res.status(200).json({ message: "Requisition Rejected" });
            } else {
              res.status(404).json({ message: "Requisition not found" });
            }
          }
        }
      );
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Error in approving requisition" });
    }
  });
 


  return router;
};
