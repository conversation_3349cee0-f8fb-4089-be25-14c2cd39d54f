const express = require("express");
const router = express.Router();
const pdfMake = require("pdfmake/build/pdfmake");
const vfsFonts = require("pdfmake/build/vfs_fonts");

// Register fonts
pdfMake.vfs = vfsFonts.pdfMake.vfs;

module.exports = (pool) => {
  // GET all referral records
  router.get("/", (req, res) => {
    const query = `
    SELECT * 
    FROM referrals`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        res.json(results);
      }
    });
  });

  // GET referral records for Team 1
  router.get("/absa", (req, res) => {
    const teams = ["LEOPARDS", "LIONS", "HQ PLATINUM", "TIGERS"];
    const teamQuery = teams.map((team) => `'${team}'`).join(", ");

    const query = `
    SELECT * 
    FROM referrals
    WHERE team IN (${teamQuery})`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        res.json(results);
      }
    });
  });

  // GET referral records for Team 2
  router.get("/karen", (req, res) => {
    const teams = [
      "PUMA GREEN",
      "PUMA YELLOW",
      "JAGUAR GREEN",
      "JAGUAR YELLOW",
      "GLOBAL PLATINUM",
    ];
    const teamQuery = teams.map((team) => `'${team}'`).join(", ");

    const query = `
    SELECT * 
    FROM referrals
    WHERE team IN (${teamQuery})`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        res.json(results);
      }
    });
  });

  // GET a specific referral record by ID
  router.get("/:id", (req, res) => {
    const { id } = req.params;
    const query = `
      SELECT * 
      FROM referrals 
      WHERE id = ?`;

    pool.query(query, [id], (err, result) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else if (result.length > 0) {
        res.json(result[0]);
      } else {
        res.status(404).json({ message: "Referral record not found" });
      }
    });
  });

  // UPDATE an existing referral record by ID
  router.patch("/:id", (req, res) => {
    const { id } = req.params;
    const {
      team,
      totalReferrals,
      salesAchieved,
      siteVisits,
      observation,
      recommendation,
    } = req.body;

    const query =
      "UPDATE referrals SET  team = ?, total_referrals = ?, sales_achieved = ?, site_visits = ?, observation = ?, recommendation = ? WHERE id = ?";

    pool.query(
      query,
      [
        team,
        totalReferrals,
        salesAchieved,
        siteVisits,
        observation,
        recommendation,
        id,
      ],
      (err, result) => {
        if (err) {
          console.error(err);
          res.status(500).json({ message: "Server Error" });
        } else if (result.affectedRows > 0) {
          res.json({ message: "Referral record updated successfully" });
        } else {
          res.status(404).json({ message: "Referral record not found" });
        }
      }
    );
  });
  // CREATE multiple referral records
  router.post("/", (req, res) => {
    const referralForms = req.body;

    const query =
      "INSERT INTO referrals (startDate, endDate, team, total_referrals, sales_achieved, site_visits, observation, recommendation) VALUES (?,?,?, ?, ?, ?, ?, ?)";

    // Array to store the results of each INSERT query
    const results = [];
    // Iterate over the referral forms and execute the INSERT query for each form
    referralForms.forEach((form) => {
      const {
        startDate,
        endDate,
        team,
        totalReferrals,
        salesAchieved,
        siteVisits,
        observation,
        recommendation,
      } = form;

      pool.query(
        query,
        [
          startDate,
          endDate,
          team,
          totalReferrals,
          salesAchieved,
          siteVisits,
          observation,
          recommendation,
        ],
        (err, result) => {
          if (err) {
            console.error(err);
            results.push({
              error: true,
              message: "Error creating referral record",
            });
          } else {
            results.push({
              error: false,
              message: "Referral record created successfully",
            });
          }

          // Check if all queries have completed
          if (results.length === referralForms.length) {
            // Check if any errors occurred during the queries
            const hasErrors = results.some((result) => result.error);
            if (hasErrors) {
              res.status(500).json({ message: "Server Error" });
            } else {
              res.json({ message: "Referral records created successfully" });
            }
          }
        }
      );
    });
  });

  // NEW: Generate a PDF report for referrals using pdfMake
  router.get("/download-pdf/referralreport", (req, res) => {
    const { startDate, endDate } = req.query;
    // Fetch data from the database
    const query = `SELECT * FROM referrals
    WHERE startDate >= '${startDate}' AND endDate <= '${endDate}'`;
    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        return res.status(500).json({ message: "Server Error" });
      }

      // Create a definition for the table with zebra stripe pattern
      const docDefinition = {
        pageSize: "A4",
        pageOrientation: "landscape",
        content: [
          {
            text: "Referral Report",
            style: "header",
            color: "black",
            decoration: "underline", // Add underline
          },
          { text: "\n" },
          {
            table: {
              headerRows: 1,
              widths: ["auto", "auto", "auto", "auto", "auto", "auto", "auto"], // Adjust the widths as needed
              body: [
                [
                  {
                    text: "ID",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Team",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Total Referrals",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Sales Achieved",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Site Visits",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Observation",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Recommendation",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                ],
                ...results.map((record, index) => [
                  {
                    text: index + 1, // Add an index column
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.team,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.total_referrals.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.sales_achieved.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.site_visits.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.observation,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.recommendation,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                ]),
              ],
              // Center the table
              alignment: "center",
            },
          },
        ],
        styles: {
          header: {
            fontSize: 16,
            bold: true,
            alignment: "center",
          },
          tableHeader: {
            bold: true,
            fontSize: 13,
            color: "white",
          },
          tableCell: {
            fontSize: 9,
            margin: [0, 5],
          },
        },
      };

      // Create the PDF document
      const pdfDoc = pdfMake.createPdf(docDefinition);

      // Set response headers
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        'attachment; filename="referral_report.pdf"'
      );

      // Pipe the PDF content to the response
      pdfDoc.getBuffer((buffer) => {
        res.end(buffer);
      });
    });
  });

  // Download PDF report for referrals - HOS
  router.get("/download-pdf/referralreport-hos", (req, res) => {
    const { startDate, endDate } = req.query;

    // Fetch data from the database - Adjust the query based on your data structure
    const query = `
    SELECT 
      *
    FROM referrals 
    WHERE startDate >= '${startDate}' AND endDate <= '${endDate}' AND team IN ('LEOPARDS', 'LIONS', 'HQ P', 'TIGERS', 'STAFF')`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        return res.status(500).json({ message: "Server Error" });
      }

      const docDefinition = {
        pageSize: "A4",
        pageOrientation: "landscape",
        content: [
          {
            text: "Referral Report",
            style: "header",
            color: "black",
            decoration: "underline", // Add underline
          },
          { text: "\n" },
          {
            table: {
              headerRows: 1,
              widths: ["auto", "auto", "auto", "auto", "auto", "auto", "auto"], // Adjust the widths as needed
              body: [
                [
                  {
                    text: "ID",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Team",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Total Referrals",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Sales Achieved",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Site Visits",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Observation",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Recommendation",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                ],
                ...results.map((record, index) => [
                  {
                    text: index + 1, // Add an index column
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.team,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.total_referrals.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.sales_achieved.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.site_visits.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.observation,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.recommendation,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                ]),
              ],
              // Center the table
              alignment: "center",
            },
          },
        ],
        styles: {
          header: {
            fontSize: 16,
            bold: true,
            alignment: "center",
          },
          tableHeader: {
            bold: true,
            fontSize: 13,
            color: "white",
          },
          tableCell: {
            fontSize: 9,
            margin: [0, 5],
          },
        },
      };

      // Create the PDF document
      const pdfDoc = pdfMake.createPdf(docDefinition);

      // Set response headers
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        'attachment; filename="referral_report_hos.pdf"'
      );

      // Pipe the PDF content to the response
      pdfDoc.getBuffer((buffer) => {
        res.end(buffer);
      });
    });
  });

  // Download PDF report for referrals - GM
  router.get("/download-pdf/referralreport-gm", (req, res) => {
    const { startDate, endDate } = req.query;

    // Fetch data from the database - Adjust the query based on your data structure
    const query = `
    SELECT 
      *
    FROM referrals 
    WHERE startDate >= '${startDate}' AND endDate <= '${endDate}' AND team IN ('PUMA', 'JAGUAR', 'REGION KAREN')`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        return res.status(500).json({ message: "Server Error" });
      }

      const docDefinition = {
        pageSize: "A4",
        pageOrientation: "landscape",
        content: [
          {
            text: "Referral Report",
            style: "header",
            color: "black",
            decoration: "underline", // Add underline
          },
          { text: "\n" },
          {
            table: {
              headerRows: 1,
              widths: ["auto", "auto", "auto", "auto", "auto", "auto", "auto"], // Adjust the widths as needed
              body: [
                [
                  {
                    text: "ID",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Team",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Total Referrals",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Sales Achieved",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Site Visits",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Observation",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Recommendation",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                ],
                ...results.map((record, index) => [
                  {
                    text: index + 1, // Add an index column
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.team,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.total_referrals.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.sales_achieved.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.site_visits.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.observation,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.recommendation,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                ]),
              ],
              // Center the table
              alignment: "center",
            },
          },
        ],
        styles: {
          header: {
            fontSize: 16,
            bold: true,
            alignment: "center",
          },
          tableHeader: {
            bold: true,
            fontSize: 13,
            color: "white",
          },
          tableCell: {
            fontSize: 9,
            margin: [0, 5],
          },
        },
      };

      // Create the PDF document
      const pdfDoc = pdfMake.createPdf(docDefinition);

      // Set response headers
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        'attachment; filename="referral_report_gm.pdf"'
      );

      // Pipe the PDF content to the response
      pdfDoc.getBuffer((buffer) => {
        res.end(buffer);
      });
    });
  });
  // Other routes like creating records, deleting, etc. can be added similarly

  return router;
};
