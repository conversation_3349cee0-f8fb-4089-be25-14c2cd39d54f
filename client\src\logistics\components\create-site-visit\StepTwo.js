import { ChevronLeft, ChevronRight, Trash } from "lucide-react";
import React from "react";

function StepTwo({ clients, setClients, onPrevious, onNext }) {
  // Add a new empty client row
  const handleAddClient = () => {
    setClients((prev) => [
      ...prev,
      { firstName: "", lastName: "", phone: "", email: "" },
    ]);
  };

  // Remove one client (if more than 1 remain)
  const handleRemoveClient = (index) => {
    setClients((prev) => {
      if (prev.length === 1) return prev; // can't remove the only one
      return prev.filter((_, i) => i !== index);
    });
  };

  // Update a single client's data
  const handleChange = (index, field, value) => {
    setClients((prev) =>
      prev.map((client, i) =>
        i === index ? { ...client, [field]: value } : client
      )
    );
  };

  return (
    <div className="space-y-4">
      {clients.map((client, i) => (
        <div
          key={i}
          className="border border-base-200 rounded p-4 mb-2 space-y-2"
        >
          <div className="flex justify-between items-center">
            <h2 className="font-semibold">Client {i + 1}</h2>
            {clients.length > 1 && (
              <button
                type="button"
                className="btn bg-gray-100 border-none rounded-full hover:bg-gray-200 text-error"
                onClick={() => handleRemoveClient(i)}
              >
                <Trash />
              </button>
            )}
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* First Name */}
            <div className="form-control">
              <label className="label">
                <span className="label-text">First Name*</span>
              </label>
              <input
                type="text"
                className="input input-bordered"
                value={client.firstName}
                onChange={(e) => handleChange(i, "firstName", e.target.value)}
                required
              />
            </div>

            {/* Last Name */}
            <div className="form-control">
              <label className="label">
                <span className="label-text">Last Name*</span>
              </label>
              <input
                type="text"
                className="input input-bordered"
                value={client.lastName}
                onChange={(e) => handleChange(i, "lastName", e.target.value)}
                required
              />
            </div>

            {/* Email (Optional) */}
            <div className="form-control">
              <label className="label">
                <span className="label-text">Email (Optional)</span>
              </label>
              <input
                type="email"
                className="input input-bordered"
                value={client.email}
                onChange={(e) => handleChange(i, "email", e.target.value)}
              />
            </div>

            {/* Phone (Must Start with +) */}
            <div className="form-control">
              <label className="label">
                <span className="label-text">Phone Number*</span>
              </label>
              <input
                type="tel"
                className="input input-bordered"
                placeholder="+254700000000"
                value={client.phone}
                onChange={(e) => handleChange(i, "phone", e.target.value)}
                required
              />
            </div>
          </div>
        </div>
      ))}

      <button
        type="button"
        className="btn btn-outline btn-sm"
        onClick={handleAddClient}
      >
        Add Client
      </button>

      {/* Prev / Next Buttons */}
      <div className="flex justify-between">
        <button
          type="button"
          onClick={onPrevious}
          className="btn btn-secondary rounded-full"
        >
          <ChevronLeft />
        </button>
        <button
          type="button"
          onClick={onNext}
          className="btn btn-primary rounded-full text-white"
        >
          <ChevronRight />
        </button>
      </div>
    </div>
  );
}

export default StepTwo;
