const express = require("express");
const multer = require("multer");
const path = require("path");
const fs = require("fs");

module.exports = (pool) => {
  const router = express.Router();

  // storage config: uploads/sales-agreements/{plotNo}/
  const storage = multer.diskStorage({
    destination: (req, file, cb) => {
      const plotNo = req.params.plotNo;
      const dir = path.join(
        __dirname,
        "../../../uploads/sales-agreements",
        plotNo
      );
      fs.mkdirSync(dir, { recursive: true });
      cb(null, dir);
    },
    filename: (req, file, cb) => {
      cb(null, `${Date.now()}_${file.originalname}`);
    },
  });
  const upload = multer({ storage });

  // GET list of agreements for a plot
  router.get("/plots/:plotNo/agreements", async (req, res) => {
    const { plotNo } = req.params;
    pool.query(
      "SELECT id, original_name, file_path, uploaded_at FROM sales_agreements WHERE plot_no = ? ORDER BY uploaded_at DESC",
      [plotNo],
      (err, results) => {
        if (err) return res.status(500).json({ error: err });
        res.json(results);
      }
    );
  });

  // GET/download a single file
  router.get("/plots/:plotNo/agreements/:id", async (req, res) => {
    const { id } = req.params;
    pool.query(
      "SELECT file_path, original_name FROM sales_agreements WHERE id = ?",
      [id],
      (err, results) => {
        if (err) return res.status(500).json({ error: err });
        if (!results.length) return res.status(404).send("Not found");
        const { file_path, original_name } = results[0];
        res.download(file_path, original_name);
      }
    );
  });

  // POST upload a new agreement
  router.post(
    "/plots/:plotNo/agreements",
    upload.single("agreement"),
    (req, res) => {
      if (!req.file) {
        console.error("⚠️ Multer did not attach a file:", req.body);
        return res.status(400).json({ message: "No file received." });
      }

      console.log("✅ Got file:", req.file.path);
      const { plotNo } = req.params;
      const filePath = req.file.path;
      const originalName = req.file.originalname;

      pool.query(
        "INSERT INTO sales_agreements (plot_no, file_path, original_name) VALUES (?,?,?)",
        [plotNo, filePath, originalName],
        (err, result) => {
          if (err) {
            console.error("❌ SQL error inserting agreement:", err);
            return res
              .status(500)
              .json({ message: "DB insert failed", error: err.message });
          }
          res
            .status(201)
            .json({ id: result.insertId, original_name: originalName });
        }
      );
    }
  );

  // DELETE an agreement
  router.delete("/plots/:plotNo/agreements/:id", async (req, res) => {
    const { id } = req.params;
    // first fetch path
    pool.query(
      "SELECT file_path FROM sales_agreements WHERE id = ?",
      [id],
      (err, results) => {
        if (err) return res.status(500).json({ error: err });
        if (!results.length) return res.status(404).send("Not found");
        const filePath = results[0].file_path;
        // delete file
        fs.unlink(filePath, (fsErr) => {
          if (fsErr) console.error(fsErr);
          // then delete db record
          pool.query(
            "DELETE FROM sales_agreements WHERE id = ?",
            [id],
            (delErr) => {
              if (delErr) return res.status(500).json({ error: delErr });
              res.sendStatus(204);
            }
          );
        });
      }
    );
  });

  return router;
};
