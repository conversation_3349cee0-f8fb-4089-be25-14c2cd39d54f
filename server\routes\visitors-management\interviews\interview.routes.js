const express = require("express");
const nodemailer = require("nodemailer");
const authenticateJWT = require("../../../middleware/authenticateJWT");
const router = express.Router();
const pdfMakePrinter = require("pdfmake/src/printer");
const multer = require("multer");
const path = require("path");
const fs = require("fs");

// Define the uploads directory relative to the project root
const uploadsDir = path.join(__dirname, "..", "..", "uploads");

// Multer config for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + "-" + file.originalname);
  },
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 40 * 1024 * 1024, // 40MB limit
  },
});

// Create 'uploads' directory if it doesn't exist
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir);
}
// Nodemailer helper function to send email
async function sendEmail(userEmail, subject, text) {
  // create reusable transporter object using the default SMTP transport
  let transporter = nodemailer.createTransport({
    host: "smtp.zoho.com",
    port: 465,
    secure: true, // true for 465, false for other ports
    auth: {
      user: process.env.DOMAIN_EMAIL, // your domain email account from .env
      pass: process.env.DOMAIN_PASSWORD, // your domain email password
    },
  });

  // send mail with defined transport object
  let info = await transporter.sendMail({
    from: '"Optiven Visitors Management Platform 💂" <<EMAIL>>', // sender address
    to: userEmail, // list of receivers
    subject: subject, // Subject line
    text: text, // plain text body
  });
}

// Define fonts
var fonts = {
  Roboto: {
    normal: "node_modules/roboto-font/fonts/Roboto/roboto-regular-webfont.ttf",
    bold: "node_modules/roboto-font/fonts/Roboto/roboto-bold-webfont.ttf",
    italic: "node_modules/roboto-font/fonts/Roboto/roboto-italic-webfont.ttf",
    bolditalics:
      "node_modules/roboto-font/fonts/Roboto/roboto-bolditalic-webfont.ttf",
  },
};

// Create a new printer with the fonts
var printer = new pdfMakePrinter(fonts);

// function to format date to db friendly format
function formatDate(dateString) {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${day}/${month}/${year}`;
}

// Map data to fields that go to the pdf
function dataToPdfRows(data) {
  return data.map((item, index) => {
    return [
      { text: String(index + 1) || "", style: "tableCell" },
      { text: item.name || "", style: "tableCell" },
      { text: item.email || "", style: "tableCell" },
      { text: item.phone_number || "", style: "tableCell" },
      { text: formatDate(item.interview_date) || "", style: "tableCell" },
      { text: item.interview_time || "", style: "tableCell" },
      { text: item.position || "", style: "tableCell" },
    ];
  });
}

module.exports = (pool) => {
  const multerFields = [];
  for (let i = 0; i < 10; i++) {
    multerFields.push({ name: `cv_${i}`, maxCount: 1 });
  }

  router.post(
    "/",
    authenticateJWT,
    upload.fields(multerFields),
    async (req, res) => {
      const interviews = JSON.parse(req.body.interviewees);
      const files = req.files;

      const BATCH_SIZE = 3;
      const failedInterviews = [];

      try {
        for (let i = 0; i < interviews.length; i += BATCH_SIZE) {
          const batch = interviews.slice(i, i + BATCH_SIZE);

          const batchPromises = batch.map((interview, index) => {
            const globalIndex = i + index;

            const {
              name,
              email,
              phone_number,
              interview_date,
              interview_time,
              position,
            } = interview;
            const cvFile = files[`cv_${globalIndex}`]
              ? files[`cv_${globalIndex}`][0]
              : null;
            const cvFilePath = cvFile ? cvFile.filename : null;

            return new Promise((resolve, reject) => {
              pool.query(
                "INSERT INTO interviewees (name, email, phone_number, interview_date, interview_time, position, cv_file_path) VALUES (?, ?, ?, ?, ?, ?, ?)",
                [
                  name,
                  email,
                  phone_number,
                  interview_date,
                  interview_time,
                  position,
                  cvFilePath,
                ],
                async (err) => {
                  if (err) return reject({ error: err, index: globalIndex });
                  try {
                    await sendEmail(
                      email,
                      "Interview Scheduled",
                      `Dear ${name}, your interview is scheduled.`
                    );
                    resolve({ success: true });
                  } catch (emailError) {
                    reject({ error: emailError, index: globalIndex });
                  }
                }
              );
            });
          });

          const results = await Promise.allSettled(batchPromises);

          results.forEach((result, index) => {
            const globalIndex = i + index; // Calculate the global index
            if (result.status === "rejected") {
              failedInterviews.push({
                index: globalIndex, // Use globalIndex here
                error: result.reason.error,
              });
            }
          });
        }

        if (failedInterviews.length > 0) {
          return res.status(207).json({
            message: "Some interviews could not be scheduled.",
            details: failedInterviews,
          });
        }

        res
          .status(201)
          .json({ message: "All interviews scheduled successfully." });
      } catch (err) {
        console.error("Error in POST /api/interviews:", err);
        res
          .status(500)
          .json({ message: "Internal Server Error", error: err.message });
      }
    }
  );

  // Retrieve all interview information
  router.get("/", async (req, res) => {
    try {
      pool.query(
        "SELECT * FROM interviewees ORDER BY id DESC",
        (err, results) => {
          if (err) throw err;

          res.json(results);
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching interview information.",
      });
    }
  });

  // Retrieve a single interview information by id
  router.get("/:id", async (req, res) => {
    const { id } = req.params;

    try {
      pool.query(
        "SELECT * FROM interviewees WHERE id = ?",
        [id],
        (err, results) => {
          if (err) throw err;

          if (results.length === 0) {
            res
              .status(404)
              .json({ message: "Interview information not found." });
          } else {
            const interview = results[0];

            res.json(interview);
          }
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching the interview information.",
      });
    }
  });

  // Update an interview
  router.patch("/:id", upload.single("cv"), async (req, res) => {
    const { id } = req.params;
    const {
      name,
      email,
      phone_number,
      interview_date,
      interview_time,
      position,
    } = req.body;
    const cvFilePath = req.file ? req.file.filename : null; // Get the CV if uploaded

    try {
      const updateFields = [
        name,
        email,
        phone_number,
        interview_date,
        interview_time,
        position,
      ];

      let query = `
        UPDATE interviewees
        SET name = ?, email = ?, phone_number = ?, interview_date = ?, interview_time = ?, position = ?
      `;

      if (cvFilePath) {
        query += `, cv_file_path = ?`; // Add CV file path to query if uploaded
        updateFields.push(cvFilePath);
      }

      query += ` WHERE id = ?`;
      updateFields.push(id);

      pool.query(query, updateFields, (err, result) => {
        if (err) throw err;

        if (result.affectedRows === 0) {
          res.status(404).json({ message: "Interview not found." });
        } else {
          res.json({ message: "Interview updated successfully." });
        }
      });
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while updating the interview.",
      });
    }
  });

  // Delete an interview
  router.delete("/:id", async (req, res) => {
    const { id } = req.params;

    try {
      pool.query(
        "DELETE FROM interviewees WHERE id = ?",
        [id],
        (err, result) => {
          if (err) throw err;

          if (result.affectedRows === 0) {
            res.status(404).json({ message: "Interview not found." });
          } else {
            res.json({ message: "Interview deleted successfully." });
          }
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while deleting the interview.",
      });
    }
  });

  // Admit a candidate based on the current level
  router.patch("/admit/:id", async (req, res) => {
    const { id } = req.params;
    const { report_time } = req.body;

    try {
      if (report_time) {
        pool.query(
          "SELECT current_level FROM interviewees WHERE id = ?",
          [id],
          (err, results) => {
            if (err) throw err;

            if (results.length === 0) {
              return res.status(404).json({ message: "Candidate not found." });
            }

            const currentLevel = results[0].current_level.toLowerCase(); // 'general', 'technical', 'executive'
            let reportTimeColumn;

            if (currentLevel === "general") {
              reportTimeColumn = "report_time";
            } else if (currentLevel === "technical") {
              reportTimeColumn = "technical_report_time";
            } else if (currentLevel === "executive") {
              reportTimeColumn = "executive_report_time";
            }

            const query = `
            UPDATE interviewees
            SET ${reportTimeColumn} = ?
            WHERE id = ?;
          `;

            pool.query(query, [report_time, id], (err, result) => {
              if (err) throw err;

              if (result.affectedRows === 0) {
                res.status(404).json({ message: "Candidate not found." });
              } else {
                res.json({ message: "Candidate admitted successfully." });
              }
            });
          }
        );
      } else {
        res.status(400).json({ message: "Missing report time." });
      }
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while admitting the candidate.",
      });
    }
  });

  // Route to download a document
  router.get("/download/:filename", (req, res) => {
    const filename = req.params.filename;
    const filePath = path.join(uploadsDir, filename);

    res.download(filePath, (err) => {
      if (err) {
        console.error("Error downloading file:", err);
        return res.status(500).send(err);
      }
    });
  });

  // Rating and Progession
  router.patch("/rate/:id", authenticateJWT, async (req, res) => {
    const { id } = req.params;
    const { rating, userId } = req.body;

    // console.log("Received rating:", rating);
    // console.log("Received userId:", userId);

    try {
      pool.query(
        "SELECT current_level, rated_by_users, general_ratings, technical_ratings, executive_ratings FROM interviewees WHERE id = ?",
        [id],
        (err, results) => {
          if (err) {
            console.error("Database query error:", err);
            return res.status(500).json({ message: "Database query error." });
          }

          if (results.length === 0) {
            return res.status(404).json({ message: "Candidate not found." });
          }

          const candidate = results[0];
          const currentLevel = candidate.current_level.toLowerCase(); // "general", "technical", "executive"

          let ratedByUsers = {};

          if (
            typeof candidate.rated_by_users === "string" &&
            candidate.rated_by_users.trim().startsWith("{")
          ) {
            // Try parsing if it's a valid JSON string
            try {
              ratedByUsers = JSON.parse(candidate.rated_by_users);
            } catch (error) {
              console.error("Error parsing rated_by_users JSON:", error);
              return res
                .status(500)
                .json({ message: "Invalid rated_by_users JSON format." });
            }
          } else if (
            typeof candidate.rated_by_users === "object" &&
            candidate.rated_by_users !== null
          ) {
            // If it's already an object, just use it
            ratedByUsers = candidate.rated_by_users;
          } else {
            // Initialize as an empty object if it's neither a string nor a valid object
            ratedByUsers = {};
          }

          // Ensure the current level exists in the object
          if (!ratedByUsers[currentLevel]) {
            ratedByUsers[currentLevel] = [];
          }

          // Check if the user has already rated the candidate at this level
          if (ratedByUsers[currentLevel].includes(userId.toString())) {
            return res
              .status(400)
              .json({ message: "You have already rated this candidate." });
          }

          // Update the ratings and rated_by_users
          let ratings = candidate[`${currentLevel}_ratings`] || "";
          ratings = ratings ? `${ratings},${rating}` : rating;

          // Append the user ID to the list for the current level
          ratedByUsers[currentLevel].push(userId.toString());

          // Stringify the ratedByUsers object before storing it
          const query = `
                UPDATE interviewees
                SET ${currentLevel}_ratings = ?, rated_by_users = ?
                WHERE id = ?;
            `;
          pool.query(
            query,
            [ratings, JSON.stringify(ratedByUsers), id],
            (err, result) => {
              if (err) {
                console.error("Database update error:", err);
                return res
                  .status(500)
                  .json({ message: "Database update error." });
              }

              res.json({ message: "Rating submitted successfully." });
            }
          );
        }
      );
    } catch (error) {
      console.error("General error:", error);
      res.status(500).json({
        message: "An error occurred while rating the candidate.",
      });
    }
  });

  // proceed to next level
  router.patch("/proceed/:id", async (req, res) => {
    const { id } = req.params;
    const { next_interview_date, next_interview_time } = req.body;

    try {
      pool.query(
        "SELECT current_level FROM interviewees WHERE id = ?",
        [id],
        (err, results) => {
          if (err) throw err;

          if (results.length === 0) {
            return res.status(404).json({ message: "Candidate not found." });
          }

          const currentLevel = results[0].current_level.toLowerCase();
          const nextLevel =
            currentLevel === "general" ? "technical" : "executive";

          const query = `
        UPDATE interviewees
        SET current_level = ?,
            ${nextLevel}_interview_date = ?,
            ${nextLevel}_interview_time = ?
        WHERE id = ?;
      `;
          pool.query(
            query,
            [nextLevel, next_interview_date, next_interview_time, id],
            (err, result) => {
              if (err) throw err;

              res.json({
                message: "Candidate proceeded to the next level successfully.",
              });
            }
          );
        }
      );
    } catch (error) {
      res.status(500).json({
        message:
          "An error occurred while proceeding the candidate to the next level.",
      });
    }
  });

  // Drop a candidate
  router.patch("/drop/:id", authenticateJWT, async (req, res) => {
    const { id } = req.params;
    const { drop_reason } = req.body;

    try {
      const query = `
          UPDATE interviewees
          SET status = 'Dropped', drop_reason = ?, decision_by = ?, decision = 'Dropped'
          WHERE id = ?;
      `;
      pool.query(query, [drop_reason, req.user.id, id], (err, result) => {
        if (err) throw err;

        if (result.affectedRows === 0) {
          return res.status(404).json({ message: "Candidate not found." });
        }

        res.json({ message: "Candidate dropped successfully." });
      });
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while dropping the candidate.",
      });
    }
  });

  // Recommend a candidate (completes the process)
  // Fetch decision_by and ensure it is parsed correctly
  router.patch("/recommend/:id", authenticateJWT, async (req, res) => {
    const { id } = req.params;
    const { comment, recommend } = req.body;
    const userId = req.user.id;

    try {
      // Fetch the current decision data
      pool.query(
        "SELECT decision_by, decision_comments, decision_recommend FROM interviewees WHERE id = ?",
        [id],
        (err, results) => {
          if (err) {
            return res.status(500).json({
              success: false,
              message: "Error fetching interview data.",
            });
          }

          let decisionBy = [];
          let decisionComments = [];
          let decisionRecommend = [];

          // Parse the data if it's not null
          if (results.length > 0) {
            const currentData = results[0];

            // Parse decision_by
            if (currentData.decision_by) {
              try {
                decisionBy = JSON.parse(currentData.decision_by);
              } catch (err) {
                console.error("Error parsing decision_by:", err);
              }
            }

            // Parse decision_comments
            if (currentData.decision_comments) {
              try {
                decisionComments = JSON.parse(currentData.decision_comments);
              } catch (err) {
                console.error("Error parsing decision_comments:", err);
              }
            }

            // Parse decision_recommend
            if (currentData.decision_recommend) {
              try {
                decisionRecommend = JSON.parse(currentData.decision_recommend);
              } catch (err) {
                console.error("Error parsing decision_recommend:", err);
              }
            }
          }

          // Ensure decisionBy is an array and push the new decision
          if (!Array.isArray(decisionBy)) {
            decisionBy = [];
          }
          if (!decisionBy.includes(userId)) {
            decisionBy.push(userId);
          }

          // Ensure decisionComments is an array and add the new comment
          if (!Array.isArray(decisionComments)) {
            decisionComments = [];
          }
          decisionComments.push(comment);

          // Ensure decisionRecommend is an array and add the new recommendation
          if (!Array.isArray(decisionRecommend)) {
            decisionRecommend = [];
          }
          decisionRecommend.push(recommend ? "Recommended" : "Not Recommended");

          // Update the interview record with the new data
          const query = `
          UPDATE interviewees
          SET decision_by = ?, decision_comments = ?, decision_recommend = ?, status = 'Completed'
          WHERE id = ?
        `;
          const values = [
            JSON.stringify(decisionBy),
            JSON.stringify(decisionComments),
            JSON.stringify(decisionRecommend),
            id,
          ];

          pool.query(query, values, (err, result) => {
            if (err) {
              return res.status(500).json({
                success: false,
                message: "Error updating interview data.",
              });
            }

            res.json({
              success: true,
              message: "Recommendation updated successfully.",
            });
          });
        }
      );
    } catch (error) {
      console.error("Error updating recommendation:", error);
      res
        .status(500)
        .json({ success: false, message: "Server error occurred." });
    }
  });

  // Download interview details
  router.get("/download-pdf/interview-reports", async (req, res) => {
    try {
      // Start date and end date from the client
      const startDate = req.query.startDate;
      const endDate = req.query.endDate;

      // Define the SQL query to fetch the interview details within the specified date range
      let query = `
  SELECT *
  FROM interviewees
  WHERE interview_date BETWEEN ? AND ?;
  `;

      // Execute the SQL query
      pool.query(query, [startDate, endDate], (err, results) => {
        if (err) throw err;

        // Define the document definition for the PDF
        const docDefinition = {
          pageSize: "A4",
          pageOrientation: "landscape",
          content: [
            {
              text: `Interviews Report from ${startDate} to ${endDate}`,
              fontSize: 20,
              alignment: "center",
              margin: [0, 0, 0, 20],
            },
            {
              table: {
                headerRows: 1,
                widths: [
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                ],
                body: [
                  [
                    {
                      text: "Index",
                      fillColor: "#BBD4E1",
                      style: "tableHeader",
                    },
                    {
                      text: "Name",
                      fillColor: "#BBD4E1",
                      style: "tableHeader",
                    },
                    {
                      text: "Email",
                      fillColor: "#BBD4E1",
                      style: "tableHeader",
                    },
                    {
                      text: "Phone Number",
                      fillColor: "#BBD4E1",
                      style: "tableHeader",
                    },
                    {
                      text: "Interview Date",
                      fillColor: "#BBD4E1",
                      style: "tableHeader",
                    },
                    {
                      text: "Interview Time",
                      fillColor: "#BBD4E1",
                      style: "tableHeader",
                    },
                    {
                      text: "Position",
                      fillColor: "#BBD4E1",
                      style: "tableHeader",
                    },
                  ],
                ],
              },
              layout: {
                hLineWidth: function (i, node) {
                  return 0;
                },
                vLineWidth: function (i, node) {
                  return 0;
                },
                fillColor: function (rowIndex, node, columnIndex) {
                  return rowIndex % 2 === 0 ? "#D3D3D3" : null;
                },
              },
            },
          ],
          styles: {
            tableHeader: {
              bold: true,
              fontSize: 13,
              color: "white",
            },
            tableCell: {
              fontSize: 12,
              margin: [0, 5],
            },
          },
        };

        // Populate the body array of the table with the fetched data
        docDefinition.content[1].table.body.push(...dataToPdfRows(results));
        // Create the PDF document using pdfmake
        const pdfDoc = printer.createPdfKitDocument(docDefinition);
        // Set the response headers to indicate a PDF file
        res.setHeader("Content-Type", "application/pdf");
        // Stream the PDF document as the response
        pdfDoc.pipe(res);
        pdfDoc.end();
      });
    } catch (error) {
      console.error(error);
      res.status(500).send("An error occurred while generating the PDF.");
    }
  });

  return router;
};
