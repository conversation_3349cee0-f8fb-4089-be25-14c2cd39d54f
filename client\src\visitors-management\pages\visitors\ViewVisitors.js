import React, { useEffect, useState } from "react";
import Sidebar from "../../components/sidebar/Sidebar";
import { useSelector } from "react-redux";
import formatTime from "../../../utils/formatTime";
import { Link } from "react-router-dom";
import { toast } from "react-toastify";

const formatDate = (dateString) => {
  if (!dateString) return null;
  const date = new Date(dateString);
  return date
    .toLocaleDateString("en-CA", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    })
    .replace(/\//g, "-");
};

const ViewVisitors = () => {
  const [visitors, setVisitors] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 25;

  const token = useSelector((state) => state.user.token);
  const user = useSelector((state) => state.user.user);
  const office = user.office;
  const accessRole = user.Accessrole;

  const [showModal, setShowModal] = useState(false);
  const [selectedVisitor, setSelectedVisitor] = useState(null);
  const [customMessage, setCustomMessage] = useState("");
  const [loading, setLoading] = useState(false);

  // Fetch visitors on mount
  useEffect(() => {
    const fetchVisitors = async () => {
      try {
        const res = await fetch("https://workspace.optiven.co.ke/api/visitors", {
          headers: { Authorization: `Bearer ${token}` },
        });
        const data = await res.json();
        if (accessRole.split("#").includes("headOfCustomerExp")) {
          setVisitors(data);
        } else {
          setVisitors(
            data.filter((v) => v.office === office || !v.office)
          );
        }
      } catch (err) {
        console.error("Error fetching visitors:", err);
      }
    };
    fetchVisitors();
  }, [token, office, accessRole]);

  // Send custom notification
  const sendCustomNotification = async () => {
    if (!customMessage.trim()) {
      toast.error("Please write a custom message.");
      return;
    }
    setLoading(true);
    try {
      const res = await fetch(
        "https://workspace.optiven.co.ke/api/visitors/send-notification",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            message: customMessage,
            visitorId: selectedVisitor.id,
            phoneNumber: selectedVisitor.phone,
          }),
        }
      );
      const data = await res.json();
      if (res.ok) {
        toast.success("Notification sent successfully!");
        setShowModal(false);
        setCustomMessage("");
      } else {
        toast.error(data.message || "Failed to send notification.");
      }
    } catch (err) {
      console.error("Notification error:", err);
      toast.error("An error occurred. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Handle check-out
  const handleCheckOut = (visitorId) => {
    const now = new Date();
    const timeString = now
      .toTimeString()
      .split(" ")[0]; // "HH:MM:SS"
    fetch(
      `https://workspace.optiven.co.ke/api/visitors/checkout/${visitorId}`,
      {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ check_out_time: timeString }),
      }
    ).catch((err) => console.error("Checkout error:", err));
    setTimeout(() => window.location.reload(), 2000);
  };

  // Delete visitor
  const deleteVisitor = (visitorId) => {
    fetch(`https://workspace.optiven.co.ke/api/visitors/${visitorId}`, {
      method: "DELETE",
      headers: { Authorization: `Bearer ${token}` },
    })
      .then((res) => {
        if (!res.ok) throw new Error("Delete failed");
        setVisitors((prev) =>
          prev.filter((v) => v.id !== visitorId)
        );
      })
      .catch((err) => console.error("Delete error:", err));
  };

  // Search filter
  const filteredVisitors = visitors.filter(
    (v) =>
      v.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      v.phone.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination calculations
  const totalPages = Math.ceil(filteredVisitors.length / itemsPerPage) || 1;
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentPageVisitors = filteredVisitors.slice(
    startIndex,
    endIndex
  );

  // Handlers
  const goToPrevious = () => {
    if (currentPage > 1) {
      setCurrentPage((p) => p - 1);
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };
  const goToNext = () => {
    if (currentPage < totalPages) {
      setCurrentPage((p) => p + 1);
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  return (
    <Sidebar>
      <div className="container px-4 py-6 mx-auto">
        {/* Search */}
        <div className="flex justify-center mb-4">
          <input
            type="text"
            className="border border-gray-300 rounded-md px-3 py-2 mr-2 w-72"
            placeholder="Search visitor by name"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {/* Table */}
        <div className="overflow-x-auto card bg-base-100 shadow-xl">
          <table className="table table-compact w-full">
            <thead>
              <tr>
                <th>#</th>
                <th>Name</th>
                <th>Phone</th>
                <th>Email</th>
                <th>Staff</th>
                <th>Room</th>
                <th>Vehicle</th>
                <th>Purpose</th>
                <th>Dept.</th>
                <th>Date</th>
                <th>Check-in</th>
                <th>Check-out</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {currentPageVisitors.length > 0 ? (
                currentPageVisitors.map((visitor, idx) => (
                  <tr key={visitor.id}>
                    <td>{startIndex + idx + 1}</td>
                    <td>{visitor.name}</td>
                    <td>{visitor.phone}</td>
                    <td>{visitor.email}</td>
                    <td>{visitor.staff_name}</td>
                    <td>{visitor.visitor_room}</td>
                    <td>{visitor.vehicle_registration}</td>
                    <td>{visitor.purpose}</td>
                    <td>{visitor.department}</td>
                    <td>{formatDate(visitor.check_in_date)}</td>
                    <td className="text-center">
                      {formatTime(visitor.check_in_time)}
                    </td>
                    <td>
                      {visitor.check_out_time ? (
                        formatTime(visitor.check_out_time)
                      ) : (
                        <button
                          className="btn btn-outline btn-sm"
                          onClick={() => handleCheckOut(visitor.id)}
                        >
                          Check Out
                        </button>
                      )}
                    </td>
                    <td>
                      <div className="flex gap-2">
                        {visitor.check_out_time === null && (
                          <>
                            <Link
                              to={`/edit-visitor/${visitor.id}`}
                              className="btn btn-warning btn-sm"
                            >
                              Edit
                            </Link>
                            <button
                              onClick={() => deleteVisitor(visitor.id)}
                              className="btn btn-error text-white btn-sm"
                            >
                              Delete
                            </button>
                            <button
                              onClick={() => {
                                setSelectedVisitor(visitor);
                                setShowModal(true);
                              }}
                              className="btn btn-info btn-sm"
                            >
                              Notify
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={13}
                    className="text-center py-4 italic text-gray-500"
                  >
                    No visitors found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination Controls */}
        <div className="flex items-center justify-center space-x-4 mt-4">
          <button
            onClick={goToPrevious}
            disabled={currentPage === 1}
            className="btn btn-sm"
          >
            « Previous
          </button>
          <span>
            Page {currentPage} / {totalPages}
          </span>
          <button
            onClick={goToNext}
            disabled={currentPage === totalPages}
            className="btn btn-sm"
          >
            Next »
          </button>
        </div>

        {/* Notification Modal */}
        {showModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur">
            <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
              <h2 className="text-lg font-bold mb-2">
                Send Custom Notification
              </h2>
              <textarea
                rows="4"
                className="input input-bordered w-full"
                placeholder="Write your message..."
                value={customMessage}
                onChange={(e) => setCustomMessage(e.target.value)}
              />
              <div className="modal-action flex justify-end mt-4">
                <button
                  onClick={sendCustomNotification}
                  disabled={loading}
                  className="btn btn-primary"
                >
                  {loading ? "Sending..." : "Send"}
                </button>
                <button
                  onClick={() => {
                    setShowModal(false);
                    setCustomMessage("");
                  }}
                  className="btn ml-2"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </Sidebar>
  );
};

export default ViewVisitors;
