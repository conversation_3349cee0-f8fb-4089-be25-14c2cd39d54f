const express = require("express");
const util = require("util");
const router = express.Router();
const authenticateJWT = require("../../middleware/authenticateJWT");
module.exports = (pool) => {
  // get projects and query their value addition automatically
  router.get("/:projectId/value-additions",authenticateJWT, (req, res) => {
    const projectId = req.params.projectId;

    // Query the project to get the level_id
    pool.query(
      "SELECT level_id, project_name FROM projects WHERE id = ?",
      [projectId],
      (err, projectResult) => {
        if (err) {
          console.error("Error fetching project details:", err);
          res.status(500).json({ message: "Error fetching project details" });
        } else if (projectResult.length === 0) {
          res.status(404).json({ message: "Project not found" });
        } else {
          const levelId = projectResult[0].level_id;
          const project_name = projectResult[0].project_name;

          // Query the level_valueAddition table to get the value_addition_level_id
          pool.query(
            "SELECT value_addition_level_id FROM level_value_addition WHERE level_id = ?",
            [levelId],
            (err, levelValueAdditionResult) => {
              if (err) {
                console.error(
                  "Error fetching value addition level details:",
                  err
                );
                res.status(500).json({
                  message: "Error fetching value addition level details",
                });
              } else if (levelValueAdditionResult.length === 0) {
                // No value additions found, return only the project name
                res
                  .status(200)
                  .json({ project_name: project_name, valueAdditions: [] });
              } else {
                const valueAdditionLevelIds = levelValueAdditionResult.map(
                  (result) => result.value_addition_level_id
                );

                // Query the value_additions table to get the value additions associated with the value_addition_level_ids
                // You can use IN clause to fetch all value additions corresponding to the extracted level IDs
                pool.query(
                  "SELECT * FROM value_addition_level WHERE id IN (?)",
                  [valueAdditionLevelIds],
                  (err, valueAdditionsResult) => {
                    if (err) {
                      console.error("Error fetching value additions:", err);
                      res
                        .status(500)
                        .json({ message: "Error fetching value additions" });
                    } else {
                      res.status(200).json(valueAdditionsResult);
                    }
                  }
                );
              }
            }
          );
        }
      }
    );
  });

  // Get all projects
  router.get("/all", authenticateJWT,async (req, res) => {
    try {
      pool.query(
        "SELECT * FROM projects ORDER BY project_name ASC",
        (err, results) => {
          if (err) throw err;

          res.json(results);
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching projects.",
      });
    }
  });
  // Get a specific project by ID
  router.get("/:id", authenticateJWT,async (req, res) => {
    const { id } = req.params;

    try {
      pool.query(
        "SELECT * FROM projects WHERE id = ?",
        [id],
        (err, results) => {
          if (err) throw err;

          if (results.length === 0) {
            res.status(404).json({ message: "Project not found." });
          } else {
            res.json(results[0]);
          }
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching the project.",
      });
    }
  });
  // get project associated with particular logged in caretaker or supervisor
  router.get("/", authenticateJWT,(req, res) => {
    const { caretakerId, supervisorId } = req.query;
    const query =
      "SELECT * FROM projects WHERE caretakerId = ? OR supervisorId = ?";
    pool.query(query, [caretakerId, supervisorId], (err, results) => {
      if (err) {
        console.error("Error executing SQL query:", err);
        res.status(500).json({ message: "Server Error" });
      } else {
        res.json(results);
      }
    });
  });

  // Create a new project
  router.post("/", authenticateJWT,async (req, res) => {
    const {
      project_name,
      level_id,
      size_in_acres,
      location,
      
    } = req.body;

    try {
      const result = await pool
        .promise()
        .query(
          "INSERT INTO projects (project_name, level_id, size_in_acres, location) VALUES (?, ?, ?, ?)",
          [
            project_name,
            level_id,
            size_in_acres,
            location,
            
          ]
        );

      const newProject = {
        id: result.insertId,
        project_name,
        level_id,
        size_in_acres,
        location,
      };
      res.status(201).json(newProject);
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });

  // Update a project by ID
  router.patch("/:id", authenticateJWT,async (req, res) => {
    const {
      project_name,
      level_id,
      size_in_acres,
      location, 
    } = req.body;

    try {
      const result = await pool
        .promise()
        .query(
          "UPDATE projects SET project_name=?, level_id=?, size_in_acres=?, location=? WHERE id=?",
          [
            project_name,
            level_id,
            size_in_acres,
            location,
            req.params.id,
          ]
        );

      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Project not found" });
      }

      res.json({ message: "Project updated successfully" });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });

  // Delete a project by Id
  router.delete("/:id", authenticateJWT,async (req, res) => {
    try {
      // Check if the project exists
      const checkProject = await pool
        .promise()
        .query("SELECT id FROM projects WHERE id=?", [req.params.id]);

      if (checkProject[0].length === 0) {
        return res.status(404).json({ message: "Project not found" });
      }

     
     
      await pool
        .promise()
        .query("DELETE FROM projects WHERE level_id=?", [req.params.id]);

      // Now, delete the project
      const result = await pool
        .promise()
        .query("DELETE FROM projects WHERE id=?", [req.params.id]);

      if (result.affectedRows === 0) {
        // The deletion did not affect any rows, meaning the project was not found
        return res.status(404).json({ message: "Project not found" });
      }

      res.json({ message: "Project deleted successfully" });
    } catch (err) {
      console.error(err);
      res.status(500).json({ message: "Internal server error" });
    }
  });

 

  return router;
};
