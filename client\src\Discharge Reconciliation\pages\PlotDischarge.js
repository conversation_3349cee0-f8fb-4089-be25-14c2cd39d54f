// src/pages/PlotDischarge.js
import React, { useEffect, useState } from "react";
import axios from "axios";
import Sidebar from "../components/Sidebar";

function PlotDischarge() {
  const [plots, setPlots] = useState([]);

  useEffect(() => {
    // Example endpoint: GET /plots/discharged
    axios
      .get("https://workspace.optiven.co.ke/api/mib/plots/discharged")
      .then((response) => setPlots(response.data))
      .catch((error) =>
        console.error("Error fetching discharged plots:", error)
      );
  }, []);

  const handleApproval = (plotId, level) => {
    axios
      .post(`https://workspace.optiven.co.ke/api/mib/plots/${plotId}/approve`, {
        level,
      })
      .then(() => alert(`Plot approved at ${level}`))
      .catch((error) => console.error("Error approving plot:", error));
  };

  const handleRejection = (plotId, level) => {
    axios
      .post(`https://workspace.optiven.co.ke/api/mib/plots/${plotId}/reject`, {
        level,
      })
      .then(() => alert(`Plot rejected at ${level}`))
      .catch((error) => console.error("Error rejecting plot:", error));
  };

  return (
    <Sidebar>
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Plot Discharge</h1>
        <table className="table-auto w-full">
          <thead>
            <tr>
              <th className="px-4 py-2">Plot Name</th>
              <th className="px-4 py-2">Bank Account</th>
              <th className="px-4 py-2">Customer Name</th>
              <th className="px-4 py-2">Status</th>
              <th className="px-4 py-2">Action</th>
            </tr>
          </thead>
          <tbody>
            {plots.map((plot) => (
              <tr key={plot.id}>
                <td className="border px-4 py-2">{plot.name}</td>
                <td className="border px-4 py-2">{plot.Bank_Account}</td>
                <td className="border px-4 py-2">{plot.Customer_Name}</td>
                <td className="border px-4 py-2">{plot.status}</td>
                <td className="border px-4 py-2">
                  {["Bank Level 1", "Bank Level 2", "Legal"].map((level) => (
                    <div key={level}>
                      <button
                        onClick={() => handleApproval(plot.id, level)}
                        className="text-green-500"
                      >
                        Approve {level}
                      </button>
                      <button
                        onClick={() => handleRejection(plot.id, level)}
                        className="text-red-500 ml-2"
                      >
                        Reject {level}
                      </button>
                    </div>
                  ))}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </Sidebar>
  );
}

export default PlotDischarge;
