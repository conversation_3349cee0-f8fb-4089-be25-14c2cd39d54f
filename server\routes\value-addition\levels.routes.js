const express = require("express");
const router = express.Router();
const authenticateJWT = require("../../middleware/authenticateJWT");

module.exports = (pool) => {
  router.get("/",authenticateJWT, (req, res) => {
    try {
      pool.query("SELECT * FROM level", (err, result) => {
        if (err) throw err;
        res.status(200).json(result);
        console.log(result);
      });
    } catch (error) {
      console.error(error);
    }
  });

  return router;
};
