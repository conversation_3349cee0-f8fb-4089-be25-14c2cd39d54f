import React from 'react';
import { useForm } from 'react-hook-form';

const TestReport = () => {
  const { register, handleSubmit, formState: { errors } } = useForm();

  const onSubmitFirstForm = (data) => {
    console.log('First Form Data:', data);
    // Add logic to handle the first form data
  };

  const onSubmitSecondForm = (data) => {
    console.log('Second Form Data:', data);
    // Add logic to handle the second form data
  };

  return (
    <div className="flex justify-between space-x-4">
      {/* First Form */}
      <div className="card w-96 bg-base-100 shadow-xl">
        <form onSubmit={handleSubmit(onSubmitFirstForm)}>
          <div className="card-body">
            <h2 className="card-title">First Form Component</h2>
            <div className="form-control">
              <input
                type="text"
                placeholder="High Value Plots"
                className="input input-bordered input-accent w-full max-w-xs"
                {...register('highValuePlots', { required: 'High Value Plots is required' })}
              />
              {errors.highValuePlots && (
                <span className="text-xs text-error">{errors.highValuePlots.message}</span>
              )}
            </div>

            <div className="form-control">
              <input
                type="text"
                placeholder="Affordable Plots"
                className="input input-bordered input-accent w-full max-w-xs"
                {...register('affordablePlots', { required: 'Affordable Plots is required' })}
              />
              {errors.affordablePlots && (
                <span className="text-xs text-error">{errors.affordablePlots.message}</span>
              )}
            </div>

            <div className="form-control">
              <select
                className="select select-bordered select-accent w-full max-w-xs"
                {...register('teamSelect', { required: 'Team selection is required' })}
              >
                <option value="">Select Team</option>
                {/* Add team options */}
                <option value="team1">Team 1</option>
                <option value="team2">Team 2</option>
                {/* Add more options if needed */}
              </select>
              {errors.teamSelect && (
                <span className="text-xs text-error">{errors.teamSelect.message}</span>
              )}
            </div>

            {/* Auto-updating field - total sales */}
            <div className="form-control">
              <input
                type="text"
                placeholder="Total Sales"
                className="input input-bordered input-accent w-full max-w-xs"
                readOnly // As this is an auto-updating field, set as readOnly
                {...register('totalSales')}
              />
            </div>

            <div className="form-control">
              <textarea
                className="textarea textarea-accent"
                placeholder="Observation"
                {...register('observation')}
              ></textarea>
            </div>

            <div className="form-control">
              <textarea
                className="textarea textarea-accent"
                placeholder="Recommendation"
                {...register('recommendation')}
              ></textarea>
            </div>

            <div className="card-actions justify-end">
              <button type="submit" className="btn btn-primary">Submit</button>
            </div>
          </div>
        </form>
      </div>

      {/* Second Form */}
      <div className="card w-96 bg-base-100 shadow-xl">
        <form onSubmit={handleSubmit(onSubmitSecondForm)}>
          <div className="card-body">
            <h2 className="card-title">Second Form Component</h2>
            <div className="form-control">
              <select
                className="select select-bordered select-accent w-full max-w-xs"
                {...register('teamSelectSecond', { required: 'Team selection is required' })}
              >
                <option value="">Select Team</option>
                {/* Add team options */}
                <option value="team1">Team 1</option>
                <option value="team2">Team 2</option>
                {/* Add more options if needed */}
              </select>
              {errors.teamSelectSecond && (
                <span className="text-xs text-error">{errors.teamSelectSecond.message}</span>
              )}
            </div>

            <div className="form-control">
              <input
                type="text"
                placeholder="Total Referrals"
                className="input input-bordered input-accent w-full max-w-xs"
                {...register('totalReferrals', { required: 'Total Referrals is required' })}
              />
              {errors.totalReferrals && (
                <span className="text-xs text-error">{errors.totalReferrals.message}</span>
              )}
            </div>

            <div className="form-control">
              <input
                type="text"
                placeholder="Sales Achieved"
                className="input input-bordered input-accent w-full max-w-xs"
                {...register('salesAchieved', { required: 'Sales Achieved is required' })}
              />
              {errors.salesAchieved && (
                <span className="text-xs text-error">{errors.salesAchieved.message}</span>
              )}
            </div>

            <div className="form-control">
              <input
                type="text"
                placeholder="Site Visits"
                className="input input-bordered input-accent w-full max-w-xs"
                {...register('siteVisits', { required: 'Site Visits is required' })}
              />
              {errors.siteVisits && (
                <span className="text-xs text-error">{errors.siteVisits.message}</span>
              )}
            </div>

            <div className="form-control">
              <textarea
                className="textarea textarea-accent"
                placeholder="Observation"
                {...register('observationSecond')}
              ></textarea>
            </div>

            <div className="form-control">
              <textarea
                className="textarea textarea-accent"
                placeholder="Recommendation"
                {...register('recommendationSecond')}
              ></textarea>
            </div>

            <div className="card-actions justify-end">
              <button type="submit" className="btn btn-primary">Submit</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TestReport;
