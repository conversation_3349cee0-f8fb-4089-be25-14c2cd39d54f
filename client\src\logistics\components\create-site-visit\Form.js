import React, { useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import StepOne from "./StepOne";
import StepTwo from "./StepTwo";
import <PERSON>Three from "./StepThree";

import { createSiteVisitRequest } from "./api"; // Adjust path if needed

function Form() {
  // Track which step we are on (1, 2, or 3)
  const [step, setStep] = useState(1);

  // Gather a token or user info from Redux
  const token = useSelector((state) => state.user?.token);
  const user = useSelector((state) => state.user?.user);
  const marketer_id = user?.user_id || "";

  const navigate = useNavigate();

  // -------------------------
  // MAIN FORM STATE
  // -------------------------
  const [siteId, setSiteId] = useState("");
  const [siteName, setSiteName] = useState("");
  const [pickupLocation, setPickupLocation] = useState("");
  const [pickupDate, setPickupDate] = useState("");
  const [pickupTime, setPickupTime] = useState("");

  // Clients: must have at least one
  const [clients, setClients] = useState([
    { firstName: "", lastName: "", phone: "", email: "" },
  ]);

  // For final confirm
  const [agree, setAgree] = useState(false);

  // -------------------------
  // TOAST HELPER FUNCTIONS
  // -------------------------
  const toastOptions = {
    position: "top-center",
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
  };

  const showError = (message) => {
    toast.error(message, toastOptions);
  };

  const showSuccess = (message) => {
    toast.success(message, toastOptions);
  };

  // -------------------------
  // STEP VALIDATIONS
  // -------------------------
  const validateStepOne = () => {
    if (!siteId.trim() || !siteName.trim()) {
      showError("Please select a site.");
      return false;
    }
    if (!pickupLocation.trim()) {
      showError("Please enter a pickup location (or check self drive).");
      return false;
    }
    if (!pickupDate) {
      showError("Please select a pickup date.");
      return false;
    }
    if (!pickupTime) {
      showError("Please select a pickup time.");
      return false;
    }

    // ----- If using project_id = 70 as the "Achievers Paradise" check:
    if (Number(siteId) === 70) {
      // Allowed hours: 8, 10, 12, 14, 16 (24-hr format)
      const [hourStr] = pickupTime.split(":");
      const hour = parseInt(hourStr, 10);
      const allowedHours = [8, 10, 12, 14, 16];

      if (!allowedHours.includes(hour)) {
        showError(
          "For Achievers Paradise (Phase 2), you can only book 8AM, 10AM, 12PM, 2PM, or 4PM (including any minutes in that hour)."
        );
        return false; // Stop user from proceeding
      }
    }

    // ----- OR If using ERP_id = "PRJ00066":
    //    1. Save the selected site's ERP_id in state (e.g. siteErpId).
    //    2. Check:  if (siteErpId === "PRJ00066") { ...time checks... }

    return true;
  };

  const validateStepTwo = () => {
    if (!clients.length) {
      showError("Add at least one client.");
      return false;
    }

    const phoneRegex = /^\+\d+$/; // Must start with + and digits only
    for (let i = 0; i < clients.length; i++) {
      const c = clients[i];
      if (!c.firstName.trim() || !c.lastName.trim()) {
        showError(`Client #${i + 1} is missing a first or last name.`);
        return false;
      }
      if (!phoneRegex.test(c.phone)) {
        showError(
          `Client ${i + 1}: Phone must start with + and be digits only. e.g. +254712345678`
        );
        return false;
      }
      // Email is optional, but if provided, do a quick check
      if (c.email.trim()) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(c.email)) {
          showError(`Client #${i + 1}: Invalid email format.`);
          return false;
        }
      }
    }
    return true;
  };

  // Step navigation
  const handleNext = () => {
    if (step === 1) {
      // Validate Step One
      const valid = validateStepOne();
      if (!valid) return; // block moving forward if invalid
      setStep(2);
    } else if (step === 2) {
      // Validate Step Two
      const valid = validateStepTwo();
      if (!valid) return; // block moving forward if invalid
      setStep(3);
    }
  };

  const handlePrevious = () => {
    setStep((prev) => prev - 1);
  };

  // Final submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!agree) {
      showError("Please confirm the details before submitting.");
      return;
    }

    // Convert local state to final payload
    const finalClients = clients.map((c) => ({
      name: `${c.firstName} ${c.lastName}`.trim(),
      phone_number: c.phone,
      email: c.email,
    }));

    const formData = {
      marketer_id,
      project_id: siteId,
      pickup_location: pickupLocation,
      pickup_date: pickupDate,
      pickup_time: pickupTime,
      clients: finalClients,
      status: "pending",
    };

    try {
      await createSiteVisitRequest(formData, token);
      showSuccess("Site visit request created successfully!");

      // Reset fields
      setStep(1);
      setSiteId("");
      setSiteName("");
      setPickupLocation("");
      setPickupDate("");
      setPickupTime("");
      setClients([{ firstName: "", lastName: "", phone: "", email: "" }]);
      setAgree(false);

      // Navigate somewhere
      navigate("/logistics-home");
    } catch (error) {
      console.error("Error creating site visit request:", error);
      showError("Failed to create site visit request. Please try again.");
    }
  };

  return (
    <div className="max-w-xl mx-auto p-4">
      {/* Toast Container */}
      <ToastContainer />

      {/* Steps indicator */}
      <div className="flex items-center my-6">
        <ul className="steps w-full">
          <li className={`step ${step >= 1 ? "step-primary" : ""}`}>
            Site Visit Details
          </li>
          <li className={`step ${step >= 2 ? "step-primary" : ""}`}>
            Client Details
          </li>
          <li className={`step ${step >= 3 ? "step-primary" : ""}`}>
            Confirm Details
          </li>
        </ul>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {step === 1 && (
          <StepOne
            siteId={siteId}
            setSiteId={setSiteId}
            siteName={siteName}
            setSiteName={setSiteName}
            pickupLocation={pickupLocation}
            setPickupLocation={setPickupLocation}
            pickupDate={pickupDate}
            setPickupDate={setPickupDate}
            pickupTime={pickupTime}
            setPickupTime={setPickupTime}
            onNext={handleNext}
          />
        )}

        {step === 2 && (
          <StepTwo
            clients={clients}
            setClients={setClients}
            onPrevious={handlePrevious}
            onNext={handleNext}
          />
        )}

        {step === 3 && (
          <StepThree
            siteName={siteName}
            pickupLocation={pickupLocation}
            pickupDate={pickupDate}
            pickupTime={pickupTime}
            clients={clients}
            agree={agree}
            setAgree={setAgree}
            onPrevious={handlePrevious}
            onSubmit={handleSubmit}
          />
        )}
      </form>
    </div>
  );
}

export default Form;
