const express = require("express");
const multer = require("multer");
const pdfMakePrinter = require("pdfmake/src/printer");
const authenticateJWT = require("../../../middleware/authenticateJWT");
const router = express.Router();

// Define your fonts
var fonts = {
  Roboto: {
    normal: "node_modules/roboto-font/fonts/Roboto/roboto-regular-webfont.ttf",
    bold: "node_modules/roboto-font/fonts/Roboto/roboto-bold-webfont.ttf",
    italic: "node_modules/roboto-font/fonts/Roboto/roboto-italic-webfont.ttf",
    bolditalics:
      "node_modules/roboto-font/fonts/Roboto/roboto-bolditalic-webfont.ttf",
  },
};

// Create a new printer with the fonts
var printer = new pdfMakePrinter(fonts);

// Define your dataToPdfRows function
function dataToPdfRows(data) {
  return data.map((item, index) => {
    return [
      { text: index + 1 ?? "", style: "tableCell" },
      { text: item.initiative_name ?? "", style: "tableCell" },
      { text: item.initiative_pillar ?? "", style: "tableCell" },
      { text: item.initiative_assisted_name ?? "", style: "tableCell" },
      { text: item.initiative_assisted_amount ?? "", style: "tableCell" },
      { text: item.initiative_assisted_comment ?? "", style: "tableCell" },
    ];
  });
}
module.exports = (pool, io) => {
  //   Route to get all Pillar Initiative Data
  router.get("/", async (req, res) => {
    try {
      pool.query("SELECT * FROM `initiatives`", (err, results) => {
        if (err) throw err;

        res.json(results);
      });
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching data from the Initiatives",
      });
    }
  });

  router.get("/health", async (req, res) => {
    try {
      pool.query(
        "SELECT * FROM `initiatives` WHERE initiative_pillar ='Health'",
        (err, results) => {
          if (err) throw err;

          res.json(results);
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching data from the Initiatives",
      });
    }
  });

  router.get("/environment", async (req, res) => {
    try {
      pool.query(
        "SELECT * FROM `initiatives` WHERE initiative_pillar ='Environment'",
        (err, results) => {
          if (err) throw err;

          res.json(results);
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching data from the Initiatives",
      });
    }
  });

  router.get("/poverty", async (req, res) => {
    try {
      pool.query(
        "SELECT * FROM `initiatives` WHERE initiative_pillar ='Poverty Alleviation'",
        (err, results) => {
          if (err) throw err;

          res.json(results);
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching data from the Initiatives",
      });
    }
  });

  router.get("/specific", async (req, res) => {
    try {
      pool.query(
        "SELECT DISTINCT initiative_name FROM initiatives;",
        (err, results) => {
          if (err) throw err;

          res.json(results);
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching data from the Initiatives",
      });
    }
  });

  router.get("/download-specific-initiative", async (req, res) => {
    try {
      const { selectedInitiative, startDate, endDate } = req.query;

      if (!selectedInitiative || !startDate || !endDate) {
        return res
          .status(400)
          .json({ error: "Missing required query parameters" });
      }

      const query = `SELECT * FROM initiative_reports WHERE initiative_reports.created_at BETWEEN ? AND ? AND initiative_reports.initiative_name = ? ORDER BY initiative_reports.created_at DESC;`;
      pool.query(
        query,
        [startDate, endDate, selectedInitiative],
        (err, results) => {
          if (err) {
            console.error("Database query error:", err);
            return res.status(500).json({ error: "Database query failed" });
          }
          // Sum up the total amount paid
          const totalAmountPaid = results
            .reduce((sum, item) => {
              const amount =
                typeof item.initiative_assisted_amount === "string"
                  ? parseFloat(
                      item.initiative_assisted_amount.replace(/,/g, "")
                    )
                  : item.initiative_assisted_amount;
              return sum + amount;
            }, 0)
            .toLocaleString();
          // Count the number of payments
          const numberOfPayments = results.length;

          const docDefinition = {
            pageSize: "A4",
            pageOrientation: "landscape",
            content: [
              {
                table: {
                  headerRows: 1,
                  widths: ["auto", "auto", "auto", "auto", "auto", "auto"],
                  body: [
                    [
                      {
                        text: "Index",
                        fillColor: "#202A44",
                        style: "tableHeader",
                        bold: true,
                      },
                      {
                        text: "Name of Initiative",
                        fillColor: "#202A44",
                        style: "tableHeader",
                        bold: true,
                      },
                      {
                        text: "Name of the Pillar",
                        fillColor: "#202A44",
                        style: "tableHeader",
                        bold: true,
                      },
                      {
                        text: "Name",
                        fillColor: "#202A44",
                        style: "tableHeader",
                        bold: true,
                      },
                      {
                        text: "Amount",
                        fillColor: "#202A44",
                        style: "tableHeader",
                        bold: true,
                      },
                      {
                        text: "Comment",
                        fillColor: "#202A44",
                        style: "tableHeader",
                        bold: true,
                      },
                    ],
                  ],
                },
                layout: {
                  hLineWidth: function (i, node) {
                    return 0;
                  },
                  vLineWidth: function (i, node) {
                    return 0;
                  },
                  fillColor: function (rowIndex, node, columnIndex) {
                    return rowIndex % 2 === 0 ? "#D3D3D3" : null;
                  },
                },
              },
              {
                text: `Total Amount Paid: ${totalAmountPaid}`,
                style: "totalAmount",
                margin: [0, 20, 0, 0],
              },
              {
                text: `Number of Payments: ${numberOfPayments}`,
                style: "totalAmount",
                margin: [0, 10, 0, 0],
              },
            ],
            styles: {
              tableHeader: {
                fontSize: 13,
                color: "white",
              },
              tableBody: {
                italic: true,
              },
            },
          };

          docDefinition.content[0].table.body.push(...dataToPdfRows(results));

          const pdfDoc = printer.createPdfKitDocument(docDefinition);
          res.setHeader("Content-Type", "application/pdf");
          pdfDoc.pipe(res);
          pdfDoc.end();
        }
      );
    } catch (error) {
      console.error("Error generating PDF:", error);
      res.status(500).json({ error: "Error generating PDF" });
    }
  });

  router.post("/", async (req, res) => {
    const { initiative_name, initiative_pillar } = req.body;

    try {
      pool.query(
        "INSERT INTO `initiatives`(`initiative_name`, `initiative_pillar`) VALUES (?, ?)",
        [initiative_name, initiative_pillar],
        (err, result) => {
          if (err) {
            console.error("Database Error:", err);
            return res.status(500).json({
              message: "An error occurred while adding the Initiative.",
            });
          }
          res.status(201).json({ message: "Initiative added successfully!" });
        }
      );
    } catch (error) {
      console.error("Error:", error);
      res.status(500).json({
        message: "An error occurred while adding the Initiative.",
      });
    }
  });

  return router;
};
