// PlotsPage.js

import React, { useState, useEffect, useMemo } from "react";
import { usePara<PERSON>, Link } from "react-router-dom";
import axios from "axios";
import Sidebar from "../components/Sidebar";
import { useDebounce } from "use-debounce";

const PlotsPage = () => {
  const { projectName } = useParams();

  // entire dataset
  const [plots, setPlots] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // search
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm] = useDebounce(searchTerm, 300);

  // pagination
  const [page, setPage] = useState(1);
  const perPage = 20;

  // sorting
  const [sortConfig, setSortConfig] = useState({
    key: "Plot_NO",
    direction: "asc",
  });

  // helper to extract sortable value (handles variance)
  const getValue = (row, key) => {
    if (key === "variance") {
      return (row.totalPaid || 0) - (row.purchase_price || 0);
    }
    return row[key] ?? "";
  };

  // 1) Fetch _all_ plots so client-side can sort/filter/paginate
  useEffect(() => {
    const fetchAll = async () => {
      try {
        setLoading(true);
        const { data } = await axios.get(
          `https://workspace.optiven.co.ke/api/mib/projects/${encodeURIComponent(
            projectName
          )}/plots/paginated?page=1&limit=100000`
        );
        setPlots(Array.isArray(data.plots) ? data.plots : []);
      } catch (err) {
        console.error(err);
        setError("Failed to load plots. Please try again later.");
      } finally {
        setLoading(false);
      }
    };
    fetchAll();
  }, [projectName]);

  // reset to first page on search or sort change
  useEffect(() => {
    setPage(1);
  }, [debouncedSearchTerm, sortConfig]);

  // 2) Filter by search
  const filtered = useMemo(() => {
    const term = debouncedSearchTerm.toLowerCase();
    return plots.filter(
      (p) =>
        (p.Plot_NO || "").toLowerCase().includes(term) ||
        (p.Customer_Name || "").toLowerCase().includes(term)
    );
  }, [plots, debouncedSearchTerm]);

  // 3) Sort, including variance
  const sorted = useMemo(() => {
    const arr = [...filtered];
    const { key, direction } = sortConfig;
    arr.sort((a, b) => {
      const aVal = getValue(a, key);
      const bVal = getValue(b, key);

      // numeric compare if both are numbers
      if (typeof aVal === "number" && typeof bVal === "number") {
        return direction === "asc" ? aVal - bVal : bVal - aVal;
      }

      // fallback to string compare
      const aStr = String(aVal).toLowerCase();
      const bStr = String(bVal).toLowerCase();
      if (aStr > bStr) return direction === "asc" ? 1 : -1;
      if (aStr < bStr) return direction === "asc" ? -1 : 1;
      return 0;
    });
    return arr;
  }, [filtered, sortConfig]);

  // 4) Paginate
  const totalPages = Math.max(1, Math.ceil(sorted.length / perPage));
  const currentData = useMemo(() => {
    const start = (page - 1) * perPage;
    return sorted.slice(start, start + perPage);
  }, [sorted, page]);

  // toggle sort on any column
  const requestSort = (key) => {
    let direction = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }
    setSortConfig({ key, direction });
  };
  const getIndicator = (key) =>
    sortConfig.key === key ? (sortConfig.direction === "asc" ? "▲" : "▼") : "";

  // loading / error
  if (loading) {
    return (
      <Sidebar>
        <div className="p-6 flex items-center justify-center">
          <div
            className="radial-progress animate-spin"
            style={{
              "--value": 70,
              "--size": "4rem",
              "--thickness": "0.1rem",
            }}
          />
          <span className="ml-4">Loading plots...</span>
        </div>
      </Sidebar>
    );
  }
  if (error) {
    return (
      <Sidebar>
        <div className="p-6 text-red-500">{error}</div>
      </Sidebar>
    );
  }

  // UI render
  return (
    <Sidebar>
      <div className="p-6 space-y-4">
        <h1 className="text-2xl font-bold">Plots for {projectName}</h1>

        <input
          type="text"
          placeholder="Search by Plot No or Customer…"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="border border-gray-300 rounded-md p-2 w-full max-w-lg"
        />

        <div className="overflow-x-auto bg-white rounded-lg shadow">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {[
                  { key: "Plot_NO", label: "Plot No", align: "left" },
                  { key: "Customer_Name", label: "Customer", align: "left" },
                  { key: "purchase_price", label: "Purchase Price", align: "right" },
                  { key: "totalPaid", label: "Total Paid", align: "right" },
                  { key: "variance", label: "Variance", align: "right" },
                ].map(({ key, label, align }) => (
                  <th
                    key={key}
                    onClick={() => requestSort(key)}
                    className={`px-6 py-3 text-${align} text-xs font-medium text-gray-500 uppercase cursor-pointer`}
                  >
                    {label} {getIndicator(key)}
                  </th>
                ))}
                <th className="px-6 py-3" />
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentData.map((row) => {
                const price = row.purchase_price || 0;
                const paid = row.totalPaid || 0;
                const diff = paid - price;
                let badge;
                if (diff > 0) {
                  badge = (
                    <span className="badge badge-warning">
                      + Ksh {diff.toLocaleString()}
                    </span>
                  );
                } else if (diff < 0) {
                  badge = (
                    <span className="badge badge-error">
                      − Ksh {Math.abs(diff).toLocaleString()}
                    </span>
                  );
                } else {
                  badge = (
                    <span className="badge badge-success">Exact</span>
                  );
                }

                return (
                  <tr key={row.Plot_NO}>
                    <td className="px-6 py-4 whitespace-nowrap">{row.Plot_NO}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {row.Customer_Name || "N/A"}
                    </td>
                    <td className="px-6 py-4 text-right whitespace-nowrap">
                      {price
                        ? `Ksh ${price.toLocaleString()}`
                        : "N/A"}
                    </td>
                    <td className="px-6 py-4 text-right whitespace-nowrap">
                      {paid
                        ? `Ksh ${paid.toLocaleString()}`
                        : "N/A"}
                    </td>
                    <td className="px-6 py-4 text-right whitespace-nowrap">
                      {badge}
                    </td>
                    <td className="px-6 py-4 text-right whitespace-nowrap">
                      <Link
                        to={`/projects/${encodeURIComponent(
                          projectName
                        )}/plots/${encodeURIComponent(row.Plot_NO)}`}
                        className="btn btn-sm btn-outline btn-success"
                      >
                        View
                      </Link>
                    </td>
                  </tr>
                );
              })}
              {currentData.length === 0 && (
                <tr>
                  <td colSpan="6" className="px-6 py-4 text-center">
                    No plots match your search.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        <div className="flex items-center justify-between">
          <button
            onClick={() => setPage((p) => Math.max(p - 1, 1))}
            disabled={page === 1}
            className="px-4 py-2 bg-gray-200 rounded disabled:opacity-50"
          >
            Prev
          </button>
          <span>
            Page <strong>{page}</strong> of <strong>{totalPages}</strong>
          </span>
          <button
            onClick={() => setPage((p) => Math.min(p + 1, totalPages))}
            disabled={page === totalPages}
            className="px-4 py-2 bg-gray-200 rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
      </div>
    </Sidebar>
  );
};

export default PlotsPage;
