import Sidebar from "./Sidebar";
import axios from "axios";
import jsPDF from "jspdf";
import React, { useState, useEffect, useCallback, useMemo } from "react";
import DatePicker from "react-datepicker";
import { useSelector } from "react-redux";
import Select from "react-select";
import { toast } from "react-toastify";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  Cell,
} from "recharts";
import * as XLSX from "xlsx";
import "react-datepicker/dist/react-datepicker.css";
import "jspdf-autotable";
import "react-toastify/dist/ReactToastify.css";

// Ensure react-toastify styles are imported

const COLORS = [
  "#4F46E5",
  "#22C55E",
  "#F97316",
  "#EF4444",
  "#3B82F6",
  "#F59E0B",
  "#10B981",
  "#8B5CF6",
  "#EC4899",
  "#6366F1",
  "#14B8A6",
  "#D946EF",
  "#F43F5E",
  "#0EA5E9",
  "#84CC16",
  "#A855F7",
  "#FB923C",
  "#64748B",
  "#06B6D4",
  "#DC2626",
];

const API_BASE_URL = "https://workspace.optiven.co.ke/api/work-diary";
const getISOWeek = (date) => {
  const d = new Date(date);
  d.setHours(0, 0, 0, 0);
  d.setDate(d.getDate() + 3 - (d.getDay() + 6) % 7);
  const week1 = new Date(d.getFullYear(), 0, 4);
  return 1 + Math.round(((d - week1) / 86400000 - 3 + (week1.getDay() + 6) % 7) / 7);
};

const getDateRangeForWeek = (weekNumber, year = new Date().getFullYear()) => {
  // Find the first day of the year
  const firstDayOfYear = new Date(year, 0, 1);
  
  // Find day of the week for the first day of the year
  const dayOfWeek = firstDayOfYear.getDay(); // 0 for Sunday, 1 for Monday, etc.
  
  // Calculate the date of the first day of the first ISO week of the year
  // In ISO week system, the first week is the week containing January 4th
  const firstDayOfFirstWeek = new Date(year, 0, 1 + (1 - dayOfWeek === 0 ? -6 : 1) - dayOfWeek);
  
  // Calculate the start date of the requested week
  const startDate = new Date(firstDayOfFirstWeek);
  startDate.setDate(firstDayOfFirstWeek.getDate() + (weekNumber - 1) * 7);
  
  // Calculate the end date of the requested week (6 days after the start date)
  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + 6);
  
  return { startDate, endDate };
};

const SupervisorDashboard = () => {
  const { token, user } = useSelector((state) => state.user);
  const [state, setState] = useState({
    teamMembersList: [],
    selectedTeamMembers: [],
    focusAreas: [],
    selectedFocusAreas: [],
    startDate: null,
    endDate: null,
    reportData: [],
    chartData: [],
    complianceData: [],
    loading: false,
    sendingReminders: false, // Added this line
    expandedEmployee: null,
    employeeActivities: {},
    loadingActivities: false, 
    modalOpen: false,
    selectedEmployee: null,
  });

  const updateState = (newState) =>
    setState((prevState) => ({ ...prevState, ...newState }));
  const [weekMode, setWeekMode] = useState(false);
  const [selectedWeekNumber, setSelectedWeekNumber] = useState(getISOWeek(new Date()));


    const setDatesFromWeek = (weekNumber) => {
      const { startDate, endDate } = getDateRangeForWeek(weekNumber);
      updateState({
        startDate,
        endDate
      });
      setSelectedWeekNumber(weekNumber);

      setTimeout(() => {
        fetchReportData();
          fetchComplianceData();
      }, 100)
    };

    const handleWeekModeToggle = () => {
      const newWeekMode = !weekMode;
      setWeekMode(newWeekMode);
      

      if (newWeekMode) {
        setDatesFromWeek(selectedWeekNumber);
      }
    };

    const fetchEmployeeActivities = useCallback(async (employeeId) => {
      if (!state.startDate || !state.endDate) {
        toast.error("Please select both start date and end date.");
        return;
      }
      
      updateState({ loadingActivities: true, selectedEmployee: employeeId, modalOpen: true });
      
      try {
        const params = {
          startDate: state.startDate.toISOString().split("T")[0],
          endDate: state.endDate.toISOString().split("T")[0],
        };
        
        // Fetch detailed activities for the selected employee
        const response = await axios.get(
          `${API_BASE_URL}/supervisor-activities/${employeeId}`,
          {
            headers: { Authorization: `Bearer ${token}` },
            params,
          }
        );
        
        // Update state with the fetched activities
        updateState({ 
          employeeActivities: {
            ...state.employeeActivities,
            [employeeId]: response.data
          }
        });
      } catch (err) {
        console.error(`Error fetching activities for employee ${employeeId}:`, err);
        toast.error(`Failed to fetch activities data.`);
        updateState({ modalOpen: false });
      } finally {
        updateState({ loadingActivities: false });
      }
    }, [token, state.startDate, state.endDate]);

    const toggleEmployeeDetails = useCallback((employeeId) => {
      if (state.expandedEmployee === employeeId) {
        updateState({ expandedEmployee: null });
        return;
      }
      
      if (!state.employeeActivities[employeeId]) {
        fetchEmployeeActivities(employeeId);
      } else {
        updateState({ expandedEmployee: employeeId });
      }
    }, [state.expandedEmployee, state.employeeActivities, fetchEmployeeActivities]);
    
    const formatTime = (timeString) => {
      return new Date(`2000-01-01T${timeString}`).toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit'
      });
    };

  const fetchData = useCallback(
    async (url, params = {}) => {
      try {
        const response = await axios.get(url, {
          headers: { Authorization: `Bearer ${token}` },
          params,
        });
        return response.data;
      } catch (err) {
        console.error(`Error fetching data from ${url}:`, err);
        toast.error(`Failed to fetch data from ${url}.`);
        throw err;
      }
    },
    [token]
  );

  useEffect(() => {
    const fetchInitialData = async () => {
      updateState({ loading: true });
      try {
        const [teamMembers, focusAreas] = await Promise.all([
          fetchData(`${API_BASE_URL}/team-members/${user.user_id}`),
          fetchData(
            `${API_BASE_URL}/focus-areas-by-supervisor/${user.user_id}`
          ),
        ]);
        updateState({
          teamMembersList: teamMembers.map((member) => ({
            value: member.user_id,
            label: member.name,
            email: member.email,
          })),
          focusAreas: focusAreas.map((fa) => ({
            value: fa.focus_area,
            label: fa.focus_area,
          })),
        });
      } catch (err) {
        // Error already logged and toasted in fetchData
      } finally {
        updateState({ loading: false });
      }
    };
    fetchInitialData();
  }, [fetchData, user.user_id]);

  const fetchComplianceData = useCallback(async () => {
    updateState({ loading: true });
    try {
      const params = {
        employees: state.selectedTeamMembers.map((e) => e.value).join(","),
        startDate: state.startDate?.toISOString().split("T")[0],
        endDate: state.endDate?.toISOString().split("T")[0],
      };
      const data = await fetchData(`${API_BASE_URL}/compliance-report`, params);
      

      const processedData = data.map(item => ({
        ...item,
        compliance_percentage: parseFloat(item.compliance_percentage).toFixed(1)
      }));
      
     
      const sortedData = processedData.sort((a, b) => 
        parseFloat(a.compliance_percentage) - parseFloat(b.compliance_percentage)
      );
      
      updateState({ complianceData: sortedData });
    } catch (err) {
      // Error already logged and toasted in fetchData
    } finally {
      updateState({ loading: false });
    }
  }, [fetchData, state.selectedTeamMembers, state.startDate, state.endDate]);

  const fetchReportData = useCallback(async () => {
    if (!state.startDate || !state.endDate) {
      toast.error("Please select both start date and end date.");
      return;
    }
    updateState({ loading: true });
    try {
      const params = {
        startDate: state.startDate.toISOString().split("T")[0],
        endDate: state.endDate.toISOString().split("T")[0],
      };
      
      // Add employee filters only if there are selected employees
      if (state.selectedTeamMembers.length > 0) {
        params.employees = state.selectedTeamMembers.map((e) => e.value).join(",");
      } else {
        // If no employees are selected, we will fetch data for all employees under the supervisor.
      }
      
      // Add focus area filters only if there are selected focus areas
      if (state.selectedFocusAreas.length > 0) {
        params.focusAreas = state.selectedFocusAreas.map((fa) => fa.value).join(",");
      }
      
      const data = await fetchData(
        `${API_BASE_URL}/supervisor/${user.user_id}/focus-areas/report`,
        params
      );
      const filteredData = data.filter((item) => item.focus_area !== "Break");
      updateState({ reportData: filteredData });
      prepareChartData(filteredData);
    } catch (err) {
      // Error already logged and toasted in fetchData
    } finally {
      updateState({ loading: false });
    }
  }, [
    fetchData,
    state.selectedTeamMembers,
    state.selectedFocusAreas,
    state.startDate,
    state.endDate,
    user.user_id,
  ]);

  const prepareChartData = useCallback((data) => {
    const aggregatedData = data.reduce((acc, curr) => {
      const key = curr.focus_area;
      if (!acc[key]) {
        acc[key] = { focus_area: curr.focus_area, hours: 0 };
      }
      acc[key].hours += parseFloat(curr.hours_logged);
      return acc;
    }, {});

    const totalHours = Object.values(aggregatedData).reduce(
      (sum, curr) => sum + curr.hours,
      0
    );
    const chartDataArray = Object.values(aggregatedData);
    const combinedChartData = combineSmallSegments(
      chartDataArray,
      0.02,
      totalHours
    );
    updateState({ chartData: combinedChartData });
  }, []);

  const combineSmallSegments = useCallback((data, threshold, totalHours) => {
    const { data: mainData, other } = data.reduce(
      (acc, curr) => {
        if (curr.hours / totalHours < threshold) {
          acc.other += curr.hours;
        } else {
          acc.data.push(curr);
        }
        return acc;
      },
      { data: [], other: 0 }
    );

    if (other > 0) {
      mainData.push({ focus_area: "Other", hours: other });
    }

    return mainData;
  }, []);

  const sendReminders = useCallback(async () => {
    updateState({ sendingReminders: true }); // Added this line
    try {
      const emails =
        state.selectedTeamMembers.length > 0
          ? state.selectedTeamMembers
              .filter((member) => member.email)
              .map((member) => member.email)
          : state.complianceData
              .filter((item) => !item.is_compliant && item.email)
              .map((item) => item.email);

      if (emails.length === 0) {
        toast.warn("No valid emails to send reminders to.");
        return;
      }

      await axios.post(
        `${API_BASE_URL}/send-reminders`,
        { emails },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      toast.success("Reminders sent successfully.");
    } catch (err) {
      console.error("Error sending reminders:", err);
      toast.error("Error sending reminders.");
    } finally {
      updateState({ sendingReminders: false }); // Added this line
    }
  }, [state.selectedTeamMembers, state.complianceData, token]);

  const exportToExcel = useCallback(() => {
    if (state.reportData.length === 0) {
      toast.warn("No data available to export.");
      return;
    }

    const worksheetData = state.reportData.map((item) => ({
      Employee: item.employee_name,
      Department: item.department,
      "Focus Area": item.focus_area,
      "Hours Logged": formatHours(item.hours_logged),
    }));
    const worksheet = XLSX.utils.json_to_sheet(worksheetData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Team Members Report");
    XLSX.writeFile(workbook, "TeamMembersReport.xlsx");
  }, [state.reportData]);

  const exportToPDF = useCallback(() => {
    if (state.reportData.length === 0) {
      toast.warn("No data available to export.");
      return;
    }

    const doc = new jsPDF();
    doc.text("Team Members Report", 14, 16);
    doc.autoTable({
      startY: 20,
      head: [["Employee", "Department", "Focus Area", "Hours Logged"]],
      body: state.reportData.map((item) => [
        item.employee_name,
        item.department,
        item.focus_area,
        formatHours(item.hours_logged),
      ]),
    });
    doc.save("TeamMembersReport.pdf");
  }, [state.reportData]);

  const formatHours = useMemo(
    () => (hours) => {
      hours = parseFloat(hours);
      const h = Math.floor(hours);
      const m = Math.round((hours - h) * 60);
      return `${h} hrs ${m} min`;
    },
    []
  );

  const getColorForFocusArea = useMemo(
    () => (focusArea) => {
      const hash = focusArea
        .split("")
        .reduce((acc, char) => char.charCodeAt(0) + ((acc << 5) - acc), 0);
      return COLORS[Math.abs(hash) % COLORS.length];
    },
    []
  );

  return (
    <Sidebar>
      {/* Activity Modal */}
        {state.modalOpen && state.selectedEmployee && (
          <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-base-100 rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] flex flex-col">
              <div className="flex justify-between items-center p-4 border-b">
                <h3 className="text-xl font-bold">
                  {state.teamMembersList.find(m => m.value === state.selectedEmployee)?.label || "Employee"} Activities
                </h3>
                <button onClick={() => updateState({ modalOpen: false })} className="btn btn-sm btn-circle">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18" />
                    <line x1="6" y1="6" x2="18" y2="18" />
                  </svg>
                </button>
              </div>
              <div className="p-4 overflow-y-auto flex-grow">
                {state.loadingActivities ? (
                  <div className="text-center py-4">
                    <span className="loading loading-spinner loading-lg"></span>
                    <div className="mt-2">Loading activities...</div>
                  </div>
                ) : state.employeeActivities[state.selectedEmployee]?.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="table w-full">
                      <thead>
                        <tr>
                          <th>Date</th>
                          <th>Time</th>
                          <th>Focus Area</th>
                          <th>Activity Description</th>
                          <th>Duration</th>
                        </tr>
                      </thead>
                      <tbody>
                        {state.employeeActivities[state.selectedEmployee].map((activity, index) => (
                          <tr key={index} className="hover">
                            <td>{new Date(activity.date).toLocaleDateString()}</td>
                            <td>{formatTime(activity.start_time)} - {formatTime(activity.end_time)}</td>
                            <td>
                              <span 
                                className="badge" 
                                style={{backgroundColor: getColorForFocusArea(activity.focus_area), color: 'white'}}
                              >
                                {activity.focus_area}
                              </span>
                            </td>
                            <td>
                              <div className="max-w-md overflow-hidden text-ellipsis">
                                {activity.activity_description}
                              </div>
                            </td>
                            <td>
                              {formatHours(activity.hours_logged)}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colSpan="4" className="text-right font-semibold">Total Hours:</td>
                          <td className="font-semibold">
                            {formatHours(
                              state.employeeActivities[state.selectedEmployee].reduce(
                                (sum, activity) => sum + parseFloat(activity.hours_logged), 
                                0
                              )
                            )}
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-4 text-base-content/60">
                    No detailed activities available for this employee.
                  </div>
                )}
              </div>
              <div className="border-t p-4 flex justify-end">
                <button onClick={() => updateState({ modalOpen: false })} className="btn">Close</button>
              </div>
            </div>
          </div>
        )}
      <div className="p-6 bg-base-200 min-h-screen">
        <h1 className="text-4xl font-bold mb-8 text-base-content">
          Supervisor Dashboard
        </h1>

        {/* Filter Section */}
        <div className="card bg-base-100 shadow-xl mb-6">
          <div className="card-body">
            {/* Rearranged Filter Layout: Date Range First */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Date Range Pickers */}
              <div className="col-span-1 md:col-span-2 lg:col-span-3">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text font-semibold">
                        Start Date *
                      </span>
                    </label>
                    <DatePicker
                      selected={state.startDate}
                      onChange={(date) => updateState({ startDate: date })}
                      placeholderText="Select Start Date"
                      dateFormat="yyyy-MM-dd"
                      className="input input-bordered w-full"
                      maxDate={state.endDate || new Date()}
                      isClearable
                      showMonthDropdown
                      showYearDropdown
                      dropdownMode="select"
                    />
                  </div>
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text font-semibold">
                        End Date *
                      </span>
                    </label>
                    <DatePicker
                      selected={state.endDate}
                      onChange={(date) => updateState({ endDate: date })}
                      placeholderText="Select End Date"
                      dateFormat="yyyy-MM-dd"
                      className="input input-bordered w-full"
                      minDate={state.startDate}
                      maxDate={new Date()}
                      isClearable
                      showMonthDropdown
                      showYearDropdown
                      dropdownMode="select"
                    />
                  </div>
                  <div className="form-control col-span-1 md:col-span-2 lg:col-span-3">
                      <div className="flex items-center mb-2">
                        <label className="label cursor-pointer">
                          <span className="label-text mr-2">Use week selection for accurate employee dashboard comparison</span> 
                          <input 
                            type="checkbox" 
                            className="toggle toggle-primary" 
                            checked={weekMode} 
                            onChange={handleWeekModeToggle}
                          />
                        </label>
                      </div>
                      
                      {weekMode && (
                        <div className="flex space-x-4 mt-2">
                          <select 
                            className="select select-bordered w-full max-w-xs"
                            value={selectedWeekNumber}
                            onChange={(e) => setDatesFromWeek(parseInt(e.target.value))}
                          >
                            {Array.from({ length: 10 }, (_, i) => {
                              const weekNum = getISOWeek(new Date()) - i;
                              return (
                                <option key={weekNum} value={weekNum}>
                                  Week {weekNum}
                                </option>
                              );
                            })}
                          </select>
                          <div className="text-sm opacity-70">
                            Selected date range: {state.startDate?.toLocaleDateString()} - {state.endDate?.toLocaleDateString()}
                          </div>
                        </div>
                      )}
                    </div>
                </div>
                {/* Alert message for missing dates */}
                {(!state.startDate || !state.endDate) && (
                  <div className="alert alert-warning mt-4">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="stroke-current flex-shrink-0 h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M12 8v4m0 4h.01"
                      />
                    </svg>
                    <span>
                      Please select both start date and end date to proceed.
                    </span>
                  </div>
                )}
              </div>

              {/* Team Members Selector */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-semibold">Team Members</span>
                </label>
                <Select
                  options={state.teamMembersList}
                  isMulti
                  onChange={(options) =>
                    updateState({ selectedTeamMembers: options || [] })
                  }
                  placeholder="Select Team Members"
                  className="react-select-container"
                  classNamePrefix="react-select"
                  isDisabled={
                    !state.startDate || !state.endDate || state.loading
                  }
                />
              </div>

              {/* Focus Areas Selector */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-semibold">Focus Areas</span>
                </label>
                <Select
                  options={state.focusAreas}
                  isMulti
                  onChange={(options) =>
                    updateState({ selectedFocusAreas: options || [] })
                  }
                  placeholder="Select Focus Areas"
                  className="react-select-container"
                  classNamePrefix="react-select"
                  isDisabled={
                    !state.startDate ||
                    !state.endDate ||
                    state.focusAreas.length === 0 ||
                    state.loading
                  }
                />
              </div>

              {/* Fetch Data Button */}
              <div className="form-control mt-4 col-span-1 md:col-span-2 lg:col-span-3">
                <button
                  onClick={() => {
                    fetchReportData();
                    fetchComplianceData();
                  }}
                  className={`btn btn-primary w-full ${
                    (!state.startDate || !state.endDate || state.loading) &&
                    "btn-disabled"
                  }`}
                  disabled={!state.startDate || !state.endDate || state.loading}
                  aria-disabled={
                    !state.startDate || !state.endDate || state.loading
                  }
                >
                  {state.loading ? (
                    <>
                      <span className="loading loading-spinner"></span>
                      Fetching Data...
                    </>
                  ) : (
                    "Fetch Data"
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Export and Reminder Buttons */}
        <div className="flex flex-wrap justify-end space-x-4 mb-6">
          <button
            onClick={exportToExcel}
            className="btn btn-success"
            disabled={state.reportData.length === 0 || state.loading}
            aria-disabled={state.reportData.length === 0 || state.loading}
          >
            Export to Excel
          </button>
          <button
            onClick={exportToPDF}
            className="btn btn-error"
            disabled={state.reportData.length === 0 || state.loading}
            aria-disabled={state.reportData.length === 0 || state.loading}
          >
            Export to PDF
          </button>
          <button
            onClick={sendReminders}
            className="btn btn-warning"
            disabled={
              state.complianceData.length === 0 ||
              state.loading ||
              state.sendingReminders
            }
            aria-disabled={
              state.complianceData.length === 0 ||
              state.loading ||
              state.sendingReminders
            }
          >
            {state.sendingReminders ? (
              <>
                <span className="loading loading-spinner"></span>
                Sending Reminders...
              </>
            ) : (
              "Send Reminders"
            )}
          </button>
        </div>

        {/* Compliance Section */}
        <div className="card bg-base-100 shadow-xl mb-8">
          <div className="card-body">
            <h2 className="card-title text-2xl mb-4">Compliance Status</h2>
            {state.loading ? (
              <div className="text-center text-base-content/60">
                <span className="loading loading-spinner loading-lg"></span>
                <div className="mt-4">Loading data...</div>
              </div>
            ) : state.complianceData.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="table w-full">
                  <thead>
                    <tr>
                      <th>Employee</th>
                      <th>Compliance %</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                  {state.complianceData.map((item, index) => (
                    <tr key={index}>
                      <td>{item.employee_name}</td>
                      <td>{parseFloat(item.compliance_percentage).toFixed(1)}%</td>
                      <td>
                        {parseFloat(item.compliance_percentage) >= 100 ? (
                          <span className="badge badge-success">
                            Compliant
                          </span>
                        ) : (
                          <span className="badge badge-error">
                            Non-Compliant
                          </span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center text-base-content/60">
                No compliance data available.
              </div>
            )}
          </div>
        </div>

        {/* Focus Areas Chart */}
        <div className="card bg-base-100 shadow-xl mb-8">
          <div className="card-body">
            <h2 className="card-title text-2xl mb-4">Focus Areas</h2>
            {state.loading ? (
              <div className="text-center text-base-content/60">
                <span className="loading loading-spinner loading-lg"></span>
                <div className="mt-4">Loading data...</div>
              </div>
            ) : state.chartData.length > 0 ? (
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={state.chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="focus_area" />
                  <YAxis allowDecimals={false} />
                  <Tooltip
                    formatter={(value) => formatHours(value)}
                    labelFormatter={(label) => `Focus Area: ${label}`}
                  />
                  <Legend />
                  <Bar dataKey="hours" name="Hours Logged">
                    {state.chartData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={getColorForFocusArea(entry.focus_area)}
                      />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="text-center text-base-content/60">
                No data available for the selected filters.
              </div>
            )}
          </div>
        </div>

        {/* Data Table */}
        <div className="card bg-base-100 shadow-xl">
      <div className="card-body">
        <h2 className="card-title text-2xl mb-4">Team Members Report</h2>
        
            {state.loading ? (
              <div className="text-center text-base-content/60">
                <span className="loading loading-spinner loading-lg"></span>
                <div className="mt-4">Loading data...</div>
              </div>
            ) : state.reportData.length > 0 ? (
              <div className="overflow-x-auto">
                {/* Group report data by employee */}
                {(() => {
                  // Group data by employee
                  const employeeData = state.reportData.reduce((acc, item) => {
                    if (!acc[item.user_id]) {
                      acc[item.user_id] = {
                        id: item.user_id,
                        name: item.employee_name,
                        department: item.department,
                        totalHours: 0,
                        focusAreas: []
                      };
                    }
                    
                    acc[item.user_id].totalHours += parseFloat(item.hours_logged);
                    acc[item.user_id].focusAreas.push({
                      focus_area: item.focus_area,
                      hours_logged: item.hours_logged
                    });
                    
                    return acc;
                  }, {});
                  
                  // Convert to array for rendering
                  const employees = Object.values(employeeData);
                  
                  return (
                    <div className="divide-y divide-base-300">
                      {employees.map((employee) => (
  <div key={employee.id} className="py-4">
                          {/* Employee Summary Row */}
                          <div 
                            className="flex justify-between items-center cursor-pointer hover:bg-base-200 p-2 rounded"
                            onClick={() => fetchEmployeeActivities(employee.id)}
                          >
                            <div className="flex flex-col">
                              <div className="font-semibold text-lg">{employee.name}</div>
                              <div className="text-sm opacity-70">{employee.department}</div>
                            </div>
                            <div className="flex items-center space-x-4">
                              <div className="text-right">
                                <div className="font-semibold">{formatHours(employee.totalHours)}</div>
                                <div className="text-sm opacity-70">Total Hours</div>
                              </div>
                              <button className="btn btn-sm btn-primary">
                                View Activities
                              </button>
                            </div>
                          </div>
                          </div>
                      ))}
                    </div>
                  );
                })()}
              </div>
            ) : (
          <div className="text-center text-base-content/60">
            No data available for the selected filters.
          </div>
        )}
      </div>
        </div>
          </div>
    </Sidebar>
  );
};

export default SupervisorDashboard;
