import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

const initialState = {
  visits: [],
  activeVisits: [],
  pendingVisits: [],
  assignedVisits: [],
  status: "idle",
  error: null,
};

export const fetchActiveSiteVisits = createAsyncThunk(
  "siteVisit/fetchActiveSiteVisits",
  async (_, { getState, rejectWithValue }) => {
    const token = getState().user.token;
    const userId = getState().user.user.user_id;

    try {
      const response = await axios.get(
        `https://workspace.optiven.co.ke/api/site-visit-requests/active/active?user_id=${userId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      console.log("Server error:", error.response);
      return rejectWithValue(
        error.response?.data || "Error fetching site visits"
      );
    }
  }
);

export const approveSiteVisit = createAsyncThunk(
  "siteVisit/approveSiteVisit",
  async ({ id, data }, { getState, rejectWithValue }) => {
    try {
      const token = getState().user.token;
      const response = await axios.patch(
        `https://workspace.optiven.co.ke/api/site-visit-requests/pending-site-visits/${id}`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data || "Error approving site visit"
      );
    }
  }
);

export const completeSiteVisit = createAsyncThunk(
  "siteVisit/completeSiteVisit",
  async ({ id }, { getState, rejectWithValue }) => {
    try {
      const token = getState().user.token;
      const response = await axios.patch(
        `https://workspace.optiven.co.ke/api/site-visit-requests/pending-site-visits/${id}`,
        { status: "complete" },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data || "Error completing site visit"
      );
    }
  }
);

export const fetchPendingSiteVisits = createAsyncThunk(
  "siteVisit/fetchPendingSiteVisits",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getState().user.token;
      const response = await axios.get(
        "https://workspace.optiven.co.ke/api/site-visit-requests/pending-site-visits/all",
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      console.log("Server error:", error.response);
      return rejectWithValue(
        error.response?.data || "Error fetching pending visits"
      );
    }
  }
);

export const fetchAssignedSiteVisits = createAsyncThunk(
  "siteVisit/fetchAssignedSiteVisits",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getState().user.token;
      const response = await axios.get(
        `https://workspace.optiven.co.ke/api/drivers/assigned-site-visits`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      console.log("Server error:", error.response);
      return rejectWithValue(
        error.response?.data || "Error fetching assigned visits"
      );
    }
  }
);

const siteVisitSlice = createSlice({
  name: "siteVisit",
  initialState,
  reducers: {
    updateSiteVisit: (state, action) => {
      // Example of updating a single site visit
      state.activeVisits = state.activeVisits.map((siteVisit) =>
        siteVisit.id === action.payload.id ? action.payload : siteVisit
      );
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchActiveSiteVisits
      .addCase(fetchActiveSiteVisits.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchActiveSiteVisits.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.activeVisits = action.payload;
      })
      .addCase(fetchActiveSiteVisits.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload;
      })

      // approveSiteVisit
      .addCase(approveSiteVisit.pending, (state) => {
        state.status = "loading";
      })
      .addCase(approveSiteVisit.fulfilled, (state, action) => {
        state.status = "succeeded";
        // If it was in activeVisits, update it:
        state.activeVisits = state.activeVisits.map((siteVisit) =>
          siteVisit.id === action.payload.id ? action.payload : siteVisit
        );
      })
      .addCase(approveSiteVisit.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload;
      })

      // completeSiteVisit
      .addCase(completeSiteVisit.pending, (state) => {
        state.status = "loading";
      })
      .addCase(completeSiteVisit.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.activeVisits = state.activeVisits.map((siteVisit) =>
          siteVisit.id === action.payload.id ? action.payload : siteVisit
        );
      })
      .addCase(completeSiteVisit.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload;
      })

      // fetchPendingSiteVisits
      .addCase(fetchPendingSiteVisits.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchPendingSiteVisits.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.pendingVisits = action.payload;
      })
      .addCase(fetchPendingSiteVisits.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload;
      })

      // fetchAssignedSiteVisits
      .addCase(fetchAssignedSiteVisits.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchAssignedSiteVisits.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.assignedVisits = action.payload;
      })
      .addCase(fetchAssignedSiteVisits.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload;
      });
  },
});

export const { updateSiteVisit } = siteVisitSlice.actions;

export const selectActiveSiteVisits = (state) => state.siteVisit.activeVisits;
export const selectAssignedSiteVisits = (state) =>
  state.siteVisit.assignedVisits;

export default siteVisitSlice.reducer;
