const express = require("express");
const authenticateJWT = require("../../../middleware/authenticateJWT");
const router = express.Router();

module.exports = (pool) => {
  // Get all sites
  router.get("/", authenticateJWT, async (req, res) => {
    try {
      pool.query("SELECT * FROM Projects ORDER BY name ASC", (err, results) => {
        if (err) throw err;
        res.json(results);
      });
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching sites.",
      });
    }
  });

  // Get a single project by ID
  router.get("/:id", authenticateJWT, async (req, res) => {
    const { id } = req.params;
    try {
      pool.query(
        "SELECT * FROM Projects WHERE project_id = ?",
        [id],
        (err, results) => {
          if (err) throw err;
          if (results.length === 0) {
            return res.status(404).json({ message: "Project not found." });
          }
          res.json(results[0]);
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching the project.",
      });
    }
  });

  // Update a project by ID
  router.put("/:id", authenticateJWT, async (req, res) => {
    const { id } = req.params;
    const { is_featured, banner, description, website_link } = req.body;

    try {
      // Build the update query dynamically based on provided fields
      const fields = [];
      const values = [];

      if (typeof is_featured !== "undefined") {
        fields.push("is_featured = ?");
        values.push(is_featured);
      }
      if (typeof banner !== "undefined") {
        fields.push("banner = ?");
        values.push(banner);
      }
      if (typeof description !== "undefined") {
        fields.push("description = ?");
        values.push(description);
      }
      if (typeof website_link !== "undefined") {
        fields.push("website_link = ?");
        values.push(website_link);
      }

      if (fields.length === 0) {
        return res.status(400).json({ message: "No fields to update." });
      }

      values.push(id);

      const sql = `UPDATE Projects SET ${fields.join(
        ", "
      )} WHERE project_id = ?`;

      pool.query(sql, values, (err, result) => {
        if (err) {
          console.error("Error updating project:", err);
          return res.status(500).json({
            message: "An error occurred while updating the project.",
          });
        }
        if (result.affectedRows === 0) {
          return res.status(404).json({ message: "Project not found." });
        }
        res.json({ message: "Project updated successfully." });
      });
    } catch (error) {
      console.error("Exception updating project:", error);
      res.status(500).json({
        message: "An error occurred while updating the project.",
      });
    }
  });

  return router;
};
