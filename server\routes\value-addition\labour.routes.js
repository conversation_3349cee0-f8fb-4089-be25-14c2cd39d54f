const express = require("express");
//const util = require("util");
const router = express.Router();
const authenticateJWT = require("../../middleware/authenticateJWT");

module.exports = (pool) => {
  // Get all labour
  router.get("/", authenticateJWT,async (req, res) => {
    try {
      pool.query(
        "SELECT * FROM labour",
        (err, results) => {
          if (err) throw err;

          res.json(results);
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching  groups.",
      });
    }
  });

  // Get a specific labour by ID
  router.get("/:id", authenticateJWT,async (req, res) => {
    try {
      const jobGroup = await pool
        .promise()
        .query("SELECT * FROM labour WHERE id = ?", [req.params.id]);

      if (jobGroup.length === 0) {
        return res.status(404).json({ message: "Job labour not found" });
      }

      res.json(jobGroup[0][0]);
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });

  // Create a new labour
  router.post("/", authenticateJWT,async (req, res) => {
    const { labour_type, rate_per_day, wage_type } = req.body;

    try {
      const result = await pool
        .promise()
        .query(
          "INSERT INTO labour (labour_type, rate_per_day, wage_type) VALUES ( ?, ?, ?)",
          [ labour_type, rate_per_day, wage_type]
        );

      const newJobGroup = {
        id: result.insertId,
        labour_type,
        rate_per_day,
        wage_type,
      };
      res.json(newJobGroup);
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });

  // Update a LABOUR by ID
  router.patch("/:id", authenticateJWT,async (req, res) => {
    const {labour_type, rate_per_day, wage_type } = req.body;

    try {
      const result = await pool
        .promise()
        .query(
          "UPDATE labour SET labour_type=?, rate_per_day=?, wage_type=? WHERE id=?",
          [labour_type, rate_per_day, wage_type, req.params.id]
        );

      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Job labour not found" });
      }

      res.status(200).json({ message: "Job labour updated successfully" });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });

  // Delete labour by ID
  
  router.delete("/:id", authenticateJWT,async (req, res) => {
    try {
      // Check if the  labour exists
      const labour = await pool
        .promise()
        .query("SELECT id FROM labour WHERE id=?", [req.params.id]);
  
      if (labour[0].length === 0) {
        return res.status(404).json({ message: "Labour not found" });
      }
  
      // Now, delete the  labour
      const result = await pool
        .promise()
        .query("DELETE FROM labour WHERE id=?", [req.params.id]);
  
      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Labour not found" });
      }
  
      res.json({ message: " Labour deleted successfully" });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });

  

  return router;
};
