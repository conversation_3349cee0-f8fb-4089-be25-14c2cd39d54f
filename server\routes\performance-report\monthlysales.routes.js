const express = require("express");
const router = express.Router();
const pdfMake = require("pdfmake/build/pdfmake");
const vfsFonts = require("pdfmake/build/vfs_fonts");

// Register fonts
pdfMake.vfs = vfsFonts.pdfMake.vfs;

module.exports = (pool) => {
  // GET all monthly sales records
  router.get("/", (req, res) => {
    const query = `
      SELECT * 
      FROM monthly_sales`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        res.json(results);
      }
    });
  });

  // GET monthly sales records for Team 1
  router.get("/team1", (req, res) => {
    const teams = ["LEOPARDS", "LIONS", "HQ PLATINUM", "TIGERS"];
    const teamQuery = teams.map((team) => `'${team}'`).join(", ");

    const query = `
    SELECT * 
    FROM monthly_sales
    WHERE team IN (${teamQuery})`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        res.json(results);
      }
    });
  });

  // GET monthly sales records for Team 2
  router.get("/team2", (req, res) => {
    const teams = [
      "PUMA GREEN",
      "PUMA YELLOW",
      "JAGUAR GREEN",
      "JAGUAR YELLOW",
      "GLOBAL PLATINUM",
    ];
    const teamQuery = teams.map((team) => `'${team}'`).join(", ");

    const query = `
    SELECT * 
    FROM monthly_sales
    WHERE team IN (${teamQuery})`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        res.json(results);
      }
    });
  });

  // GET a specific monthly sales record by ID
  router.get("/:id", (req, res) => {
    const { id } = req.params;
    const query = `
      SELECT * 
      FROM monthly_sales 
      WHERE id = ?`;

    pool.query(query, [id], (err, result) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else if (result.length > 0) {
        res.json(result[0]);
      } else {
        res.status(404).json({ message: "Monthly sales record not found" });
      }
    });
  });

  // UPDATE an existing monthly sales record by ID
  router.patch("/:id", (req, res) => {
    const { id } = req.params;
    const {
      highValuePlots,
      affordablePlots,
      team,
      totalSales,
      observation,
      recommendation,
    } = req.body;

    const query =
      "UPDATE monthly_sales SET high_value_plots = ?, affordable_plots = ?, team = ?, total_sales = ?, observation = ?, recommendation = ? WHERE id = ?";

    pool.query(
      query,
      [
        highValuePlots,
        affordablePlots,
        team,
        totalSales,
        observation,
        recommendation,
        id,
      ],
      (err, result) => {
        if (err) {
          console.error(err);
          res.status(500).json({ message: "Server Error" });
        } else if (result.affectedRows > 0) {
          res.json({ message: "Monthly sales record updated successfully" });
        } else {
          res.status(404).json({ message: "Monthly sales record not found" });
        }
      }
    );
  });

  // CREATE multiple monthly sales records
  router.post("/", (req, res) => {
    const salesForms = req.body;

    // Updated INSERT query to include the 'startDate' and 'endDate' columns
    const query =
      "INSERT INTO monthly_sales (startDate, endDate, OceanViewRidge, Malindi, GreatOasis, Ushindi,AchieversCommercial,Wema,JoyLovers,kithimani, team, total_sales, observation, recommendation) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    // Array to store the results of each INSERT query
    const results = [];

    // Iterate over the sales forms and execute the INSERT query for each form
    salesForms.forEach((form) => {
      const {
        startDate,
        endDate,
        OceanViewRidge,
        Malindi,
        GreatOasis,
        Ushindi,
        AchieversCommercial,
        Wema,
        JoyLovers,
        kithimani,
        team,
        totalSales,
        observation,
        recommendation,
      } = form;

      pool.query(
        query,
        [
          startDate,
          endDate,
          OceanViewRidge,
          Malindi,
          GreatOasis,
          Ushindi,
          AchieversCommercial,
          Wema,
          JoyLovers,
          kithimani,
          team,
          totalSales,
          observation,
          recommendation,
        ],
        (err, result) => {
          if (err) {
            console.error(err);
            results.push({
              error: true,
              message: "Error creating monthly sales record",
            });
          } else {
            results.push({
              error: false,
              message: "Monthly sales record created successfully",
            });
          }

          // Check if all queries have completed
          if (results.length === salesForms.length) {
            // Check if any errors occurred during the queries
            const hasErrors = results.some((result) => result.error);
            if (hasErrors) {
              res.status(500).json({ message: "Server Error" });
            } else {
              res.json({
                message: "Monthly sales records created successfully",
              });
            }
          }
        }
      );
    });
  });

  // NEW: Generate a PDF report for monthly sales using pdfMake
  router.get("/download-pdf/monthlyreport", (req, res) => {
    const { startDate, endDate } = req.query; // Assuming you pass the dates in the query parameters

    // Fetch data from the database
    const query = `
  SELECT 
   *
  FROM monthly_sales 
  WHERE startDate >= '${startDate}' AND endDate <= '${endDate}'`;
    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        return res.status(500).json({ message: "Server Error" });
      }

      // Create a definition for the table with zebra stripe pattern, auto width, and centered alignment
      const tableDefinition = {
        pageSize: "A4",
        pageOrientation: "landscape",
        content: [
          {
            text: `Monthly Sales Report (${startDate} - ${endDate})`,
            style: "header",
            color: "black",
            alignment: "center",
          },
          { text: "\n" },
          {
            table: {
              headerRows: 1,
              widths: ["auto", "auto", "auto", "auto", "auto", "auto", "auto"], // Adjust the widths as needed
              body: [
                [
                  {
                    text: "ID",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Team",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "High Value Plots",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Affordable Plots",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Total Sales",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Observation",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Recommendation",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                ],
                ...results.map((record, index) => [
                  {
                    text: record.id.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.team,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.high_value_plots.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.affordable_plots.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.total_sales.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.observation,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.recommendation,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                ]),
              ],
              // Center the table
              alignment: "center",
            },
          },
        ],
        styles: {
          header: {
            fontSize: 16,
            bold: true,
          },
        },
      };

      // Create the PDF document
      const pdfDoc = pdfMake.createPdf(tableDefinition);

      // Set response headers
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        'attachment; filename="monthly_sales_report.pdf"'
      );

      // Pipe the PDF content to the response
      pdfDoc.getBuffer((buffer) => {
        res.end(buffer);
      });
    });
  });

  // Download PDF report for monthly sales - HOS
  router.get("/download-pdf/monthlyreport-hos", (req, res) => {
    const { startDate, endDate } = req.query;

    // Fetch data from the database - Adjust the query based on your data structure
    const query = `
    SELECT 
      *
    FROM monthly_sales 
    WHERE startDate >= '${startDate}' AND endDate <= '${endDate}' AND team IN ('LEOPARDS', 'LIONS', 'HQ P', 'TIGERS', 'STAFF', 'HQ BLUE')`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        return res.status(500).json({ message: "Server Error" });
      }

      const tableDefinition = {
        pageSize: "A4",
        pageOrientation: "landscape",
        content: [
          {
            text: `Monthly Sales Report (${startDate} - ${endDate})`,
            style: "header",
            color: "black",
            alignment: "center",
          },
          { text: "\n" },
          {
            table: {
              headerRows: 1,
              widths: ["auto", "auto", "auto", "auto", "auto", "auto", "auto"], // Adjust the widths as needed
              body: [
                [
                  {
                    text: "ID",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Team",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "High Value Plots",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Affordable Plots",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Total Sales",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Observation",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Recommendation",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                ],
                ...results.map((record, index) => [
                  {
                    text: record.id.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.team,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.high_value_plots.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.affordable_plots.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.total_sales.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.observation,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.recommendation,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                ]),
              ],
              // Center the table
              alignment: "center",
            },
          },
        ],
        styles: {
          header: {
            fontSize: 16,
            bold: true,
          },
        },
      };

      // Create the PDF document
      const pdfDoc = pdfMake.createPdf(tableDefinition);

      // Set response headers
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        'attachment; filename="monthly_sales_report_hos.pdf"'
      );

      // Pipe the PDF content to the response
      pdfDoc.getBuffer((buffer) => {
        res.end(buffer);
      });
    });
  });

  // Download PDF report for monthly sales - GM
  router.get("/download-pdf/monthlyreport-gm", (req, res) => {
    const { startDate, endDate } = req.query;

    // Fetch data from the database - Adjust the query based on your data structure
    const query = `
    SELECT 
      *
    FROM monthly_sales 
    WHERE startDate >= '${startDate}' AND endDate <= '${endDate}' AND team IN ('PUMA', 'JAGUAR', 'REGION KAREN', 'KAREN BLUE')`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        return res.status(500).json({ message: "Server Error" });
      }

      const tableDefinition = {
        pageSize: "A4",
        pageOrientation: "landscape",
        content: [
          {
            text: `Monthly Sales Report (${startDate} - ${endDate})`,
            style: "header",
            color: "black",
            alignment: "center",
          },
          { text: "\n" },
          {
            table: {
              headerRows: 1,
              widths: ["auto", "auto", "auto", "auto", "auto", "auto", "auto"], // Adjust the widths as needed
              body: [
                [
                  {
                    text: "ID",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Team",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "High Value Plots",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Affordable Plots",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Total Sales",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Observation",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Recommendation",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                ],
                ...results.map((record, index) => [
                  {
                    text: record.id.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.team,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.high_value_plots.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.affordable_plots.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.total_sales.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.observation,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.recommendation,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                ]),
              ],
              // Center the table
              alignment: "center",
            },
          },
        ],
        styles: {
          header: {
            fontSize: 16,
            bold: true,
          },
        },
      };

      // Create the PDF document
      const pdfDoc = pdfMake.createPdf(tableDefinition);

      // Set response headers
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        'attachment; filename="monthly_sales_report_gm.pdf"'
      );

      // Pipe the PDF content to the response
      pdfDoc.getBuffer((buffer) => {
        res.end(buffer);
      });
    });
  });

  // Other routes like creating records, deleting, etc. can be added similarly

  return router;
};
