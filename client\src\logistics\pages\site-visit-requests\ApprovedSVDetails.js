import React, { useState, useEffect } from "react";
import Sidebar from "../../components/Sidebar";
import { useParams } from "react-router-dom";
import { useSelector } from "react-redux";
import formatTime from "../../../utils/formatTime";
import formatDate from "../../../utils/formatDate";
import { TailSpin } from "react-loader-spinner";
import { BadgeCheck, User, Phone, Truck, Calendar, Clock } from "lucide-react";

const ApprovedSVDetails = () => {
  const [siteVisit, setSiteVisit] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const token = useSelector((state) => state.user.token);
  const { id } = useParams();

  useEffect(() => {
    const fetchSiteVisit = async () => {
      try {
        const response = await fetch(
          `https://workspace.optiven.co.ke/api/site-visit-requests/${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (!response.ok) {
          throw new Error("Failed to fetch site visit details.");
        }

        const data = await response.json();
        setSiteVisit(data);
      } catch (err) {
        console.error("Error:", err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchSiteVisit();
  }, [id, token]);

  if (loading) {
    return (
      <Sidebar>
        <div className="flex flex-col items-center justify-center min-h-screen">
          <TailSpin
            height="80"
            width="80"
            color="#4fa94d"
            ariaLabel="Loading"
          />
          <p className="mt-4 text-gray-700">Loading site visit details...</p>
        </div>
      </Sidebar>
    );
  }

  if (error) {
    return (
      <Sidebar>
        <div className="flex flex-col items-center justify-center min-h-screen">
          <p className="text-red-500 text-lg">{error}</p>
        </div>
      </Sidebar>
    );
  }

  if (!siteVisit) {
    return (
      <Sidebar>
        <div className="flex flex-col items-center justify-center min-h-screen">
          <p className="text-gray-700 text-lg">
            No site visit details available.
          </p>
        </div>
      </Sidebar>
    );
  }

  return (
    <Sidebar>
      <div className="flex flex-col items-center justify-center p-6 bg-gray-100 min-h-screen">
        {/* Site Visit Details Card */}
        <div className="w-full max-w-4xl bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">
            Site Visit Details
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {/* Site Name */}
            <div className="flex items-center">
              <BadgeCheck className="w-6 h-6 text-blue-500 mr-3" />
              <div>
                <h3 className="text-gray-700 font-semibold">Site Name</h3>
                <p className="text-gray-600">{siteVisit.site_name || "N/A"}</p>
              </div>
            </div>
            {/* Chauffeur Name */}
            <div className="flex items-center">
              <User className="w-6 h-6 text-green-500 mr-3" />
              <div>
                <h3 className="text-gray-700 font-semibold">Chauffeur Name</h3>
                <p className="text-gray-600">
                  {siteVisit.driver_name || "N/A"}
                </p>
              </div>
            </div>
            {/* Chauffeur Contact */}
            <div className="flex items-center">
              <Phone className="w-6 h-6 text-yellow-500 mr-3" />
              <div>
                <h3 className="text-gray-700 font-semibold">
                  Chauffeur Contact
                </h3>
                <p className="text-gray-600">
                  {siteVisit.driver_contact ? siteVisit.driver_contact : "N/A"}
                </p>
              </div>
            </div>
            {/* Vehicle Registration */}
            <div className="flex items-center">
              <Truck className="w-6 h-6 text-purple-500 mr-3" />
              <div>
                <h3 className="text-gray-700 font-semibold">
                  Vehicle Registration
                </h3>
                <p className="text-gray-600">
                  {siteVisit.vehicle_name || "N/A"}
                </p>
              </div>
            </div>
            {/* Pickup Date */}
            <div className="flex items-center">
              <Calendar className="w-6 h-6 text-pink-500 mr-3" />
              <div>
                <h3 className="text-gray-700 font-semibold">Pickup Date</h3>
                <p className="text-gray-600">
                  {formatDate(siteVisit.pickup_date)}
                </p>
              </div>
            </div>
            {/* Pickup Time */}
            <div className="flex items-center">
              <Clock className="w-6 h-6 text-indigo-500 mr-3" />
              <div>
                <h3 className="text-gray-700 font-semibold">Pickup Time</h3>
                <p className="text-gray-600">
                  {formatTime(siteVisit.pickup_time)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Clients Information */}
        <div className="w-full max-w-4xl bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">
            Client Information
          </h2>
          {siteVisit.clients && siteVisit.clients.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {siteVisit.clients.map((client, index) => (
                <div
                  key={index}
                  className="flex flex-col items-start p-4 bg-gray-50 rounded-lg shadow hover:shadow-md transition-shadow"
                >
                  <h3 className="text-xl font-semibold text-gray-700 mb-2">
                    {client.client_name.toUpperCase()}
                  </h3>
                  <p className="text-gray-600">
                    <strong>Email:</strong> {client.client_email || "N/A"}
                  </p>
                  <p className="text-gray-600">
                    <strong>Phone:</strong> {client.client_phone || "N/A"}
                  </p>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-600">No client information available.</p>
          )}
        </div>
      </div>
    </Sidebar>
  );
};

export default ApprovedSVDetails;
