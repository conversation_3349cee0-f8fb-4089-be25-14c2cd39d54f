import React, { useEffect, useState, useMemo } from "react";
import { useSelector, useDispatch } from "react-redux";
import moment from "moment";
import {
  fetchActiveSiteVisits,
  selectActiveSiteVisits,
  fetchPendingSiteVisits,
  fetchAssignedSiteVisits,
} from "../../redux/logistics/features/siteVisit/siteVisitSlice";
import { fetchPendingVehicleRequests } from "../../redux/logistics/features/vehicleRequest/vehicleRequestSlice";
import { fetchNotifications } from "../../redux/logistics/features/notifications/notificationsSlice";
import "./Sidebar.css";
import { Link } from "react-router-dom";

const useHasRole = (roleString) => {
  const accessRole = useSelector((state) => state.user.accessRole) || "";
  const roles = useMemo(() => accessRole.trim().split("#"), [accessRole]);

  return useMemo(() => roles.includes(roleString), [roles, roleString]);
};

const Sidebar = ({ children }) => {
  const dispatch = useDispatch();

  const [canBookSiteVisit, setCanBookSiteVisit] = useState(true);
  const [latestNotification, setLatestNotification] = useState(null);

  // ---- Role checks
  const isMarketer = useHasRole("113");
  const isHOS = useHasRole("hos");
  const isGM = useHasRole("gm");
  const isDriver = useHasRole("driver");
  const isHOL = useHasRole("headOfLogistics");
  const isAnalyst = useHasRole("dataAnalyst");
  const isAdmin = useHasRole("logisticsAdmin");
  const isOperations = useHasRole("operations");

  // ---- Redux store data
  const activeVisits = useSelector(selectActiveSiteVisits);
  const siteVisitStatus = useSelector((state) => state.siteVisit.status);
  const pendingVisits = useSelector((state) => state.siteVisit.pendingVisits);
  const pendingVehicleRequests = useSelector(
    (state) => state.vehicleRequest.pendingVehicleRequests
  );
  const assignedBookings = useSelector((state) => state.siteVisit);

  const numAssignedSiteVisits = Array.isArray(assignedBookings.assignedVisits)
    ? assignedBookings.assignedVisits.length
    : 0;

  // notifications
  const selectNotifications = (state) =>
    state.notifications.notifications.notifications;
  const notifications = useSelector(selectNotifications) || [];
  const unreadNotifications = notifications.filter((n) => n.isRead === 0);
  const unreadNumber = unreadNotifications.length;
  const hasUnreadNotifications = unreadNumber > 0;

  // ---- Combine all initial fetch calls into a single useEffect
  useEffect(() => {
    (async () => {
      try {
        await dispatch(fetchActiveSiteVisits());
        await dispatch(fetchPendingSiteVisits());
        await dispatch(fetchPendingVehicleRequests());
        await dispatch(fetchAssignedSiteVisits());

        const notificationsData = await dispatch(fetchNotifications()).unwrap();
        const newestNotification = notificationsData.notifications[0];
        setLatestNotification(newestNotification);

        // If the newest notification is "approved" but older than 24 hours, disable site visit booking.
        if (
          newestNotification?.type === "approved" &&
          moment().diff(newestNotification.timestamp, "hours") > 24
        ) {
          setCanBookSiteVisit(false);
        }
      } catch (err) {
        console.error("Error fetching initial data: ", err);
      }
    })();
  }, [dispatch]);

  const hasActiveSiteVisit =
    siteVisitStatus === "succeeded" && activeVisits && activeVisits.length > 0;

  const shouldDisableSiteVisitButton = () => {
    // If the last known notification is older than 24hrs, disable
    if (
      latestNotification?.type === "approved" &&
      moment().diff(latestNotification.timestamp, "hours") > 24
    ) {
      return true;
    }

    // Also disable if user has certain active visits
    if (hasActiveSiteVisit && activeVisits.length) {
      for (const visit of activeVisits) {
        // This is the correct way to check multiple statuses:
        if (["in_progress", "complete", "reviewed"].includes(visit.status)) {
          return true;
        }
      }
    }
    return false;
  };

  const numPendingSiteVisits = Array.isArray(pendingVisits)
    ? pendingVisits.length
    : 0;
  const hasPendingSiteVisits = numPendingSiteVisits > 0;

  const numPendingVehicleRequests = Array.isArray(pendingVehicleRequests)
    ? pendingVehicleRequests.length
    : 0;
  const hasPendingVehicleRequests = numPendingVehicleRequests > 0;

  return (
    <div className="drawer">
      <input id="my-drawer" type="checkbox" className="drawer-toggle" />

      {/* Main content */}
      <div className="drawer-content overflow-visible">{children}</div>

      {/* Sidebar menu */}
      <div className="drawer-side">
        <label htmlFor="my-drawer" className="drawer-overlay"></label>

        <ul className="menu p-4 w-80 bg-base-100 text-base-content">
          {/* Home */}
          <li>
            <Link to="/logistics-home" className="font-bold my-1">
              Home
            </Link>
          </li>

          {/* Dashboard */}
          {(isHOS || isGM || isAdmin || isHOL || isAnalyst || isOperations) && (
            <li>
              <Link to="/dashboard" className="font-bold my-1">
                Dashboard
              </Link>
            </li>
          )}

          {/* Notifications */}
          <li>
            <Link to="/notifications" className="font-bold my-1">
              Notifications
              <span
                className={`badge badge-secondary badge-sm text-white font-bold ${
                  hasUnreadNotifications ? "" : "hidden"
                }`}
              >
                {hasUnreadNotifications && unreadNumber}
              </span>
            </Link>
          </li>

          {/* Reports */}
          {(isHOS || isGM || isAdmin || isHOL || isAnalyst || isOperations) && (
            <div className="collapse collapse-arrow border border-base-300 bg-base-100 rounded-box my-1">
              <input type="checkbox" className="peer" />
              <div className="collapse-title font-bold">Reports</div>
              <div className="collapse-content -mt-3 flex flex-col menu bg-base-100">
                <Link
                  to="/approved-site-visit-reports"
                  className="mt-1 hover:bg-base-200 rounded p-2"
                >
                  Approved Site Visits
                </Link>
                <Link
                  to="/site-visits-summary-reports"
                  className="mt-1 hover:bg-base-200 rounded p-2"
                >
                  Site Visits Summary
                </Link>
                <Link
                  to="/most-booked-sites-reports"
                  className="mt-1 hover:bg-base-200 rounded p-2"
                >
                  Most Booked Sites
                </Link>
                <Link
                  to="/marketers-feedback-reports"
                  className="mt-1 hover:bg-base-200 rounded p-2"
                >
                  Marketers Feedback
                </Link>
                <Link
                  to="/driver-itinerary"
                  className="mt-1 hover:bg-base-200 rounded p-2"
                >
                  Chauffeur Itinerary
                </Link>
              </div>
            </div>
          )}

          {/* Site Visits */}
          {(isMarketer || isHOS || isGM || isAdmin || isDriver || isHOL) && (
            <div className="collapse collapse-arrow border border-base-300 bg-base-100 rounded-box my-1">
              <input type="checkbox" className="peer" />
              <div className="collapse-title font-bold">
                Site Visits{" "}
                {(isAdmin || isHOL) && (
                  <span
                    className={`badge badge-warning badge-sm text-white font-bold ${
                      hasPendingSiteVisits ? "" : "hidden"
                    }`}
                  >
                    {numPendingSiteVisits}
                  </span>
                )}
                {(isDriver || isAdmin) && (
                  <span
                    className={`badge badge-primary badge-sm text-white font-bold ${
                      numAssignedSiteVisits > 0 ? "" : "hidden"
                    }`}
                  >
                    {numAssignedSiteVisits}
                  </span>
                )}
                {(isMarketer || isAdmin) && (
                  <span
                    className={`ml-1 badge badge-primary badge-sm text-white font-bold ${
                      hasActiveSiteVisit ? "" : "hidden"
                    }`}
                  >
                    {activeVisits.length}
                  </span>
                )}
              </div>
              <div className="collapse-content -mt-3 flex flex-col menu bg-base-100">
                {(isMarketer || isAdmin) && (
                  <Link
                    to={
                      !hasActiveSiteVisit && !shouldDisableSiteVisitButton()
                        ? "/book-site-visit"
                        : "#"
                    }
                    className={`mt-1 hover:bg-base-200 rounded p-2 ${
                      shouldDisableSiteVisitButton()
                        ? "opacity-50 cursor-not-allowed"
                        : ""
                    }`}
                  >
                    Book a Site Visit
                  </Link>
                )}
                {(isMarketer || isAdmin) && (
                  <Link
                    className="mt-1 hover:bg-base-200 rounded p-2"
                    to="/my-site-visits"
                  >
                    My Site Visits
                    <span
                      className={`ml-1 badge badge-primary badge-sm text-white font-bold ${
                        hasActiveSiteVisit ? "" : "hidden"
                      }`}
                    >
                      {activeVisits.length}
                    </span>
                  </Link>
                )}
                {(isDriver || isAdmin) && (
                  <Link
                    className="mt-1 hover:bg-base-200 rounded p-2"
                    to="/assigned-site-visits"
                  >
                    Assigned Site Visits{" "}
                    {(isDriver || isAdmin) && (
                      <span
                        className={`badge badge-sm badge-primary text-white font-bold ${
                          Array.isArray(assignedBookings.assignedVisits) &&
                          assignedBookings.assignedVisits.length > 0
                            ? ""
                            : "hidden"
                        }`}
                      >
                        {Array.isArray(assignedBookings.assignedVisits)
                          ? assignedBookings.assignedVisits.length
                          : 0}
                      </span>
                    )}
                  </Link>
                )}
                {(isHOS || isGM || isAdmin || isOperations || isHOL) && (
                  <Link
                    className="mt-1 hover:bg-base-200 rounded p-2"
                    to="/approved-site-visits"
                  >
                    Approved Site Visits
                  </Link>
                )}
                {(isAdmin || isHOL) && (
                  <Link
                    className="mt-1 hover:bg-base-200 rounded p-2"
                    to="/site-visit-requests"
                  >
                    Site Visit Requests{" "}
                    <span
                      className={`badge badge-sm badge-warning text-white font-bold ${
                        hasPendingSiteVisits ? "" : "hidden"
                      }`}
                    >
                      {numPendingSiteVisits}
                    </span>
                  </Link>
                )}
                {(isHOS || isGM || isAdmin || isOperations || isHOL) && (
                  <Link
                    className="mt-1 hover:bg-base-200 rounded p-2"
                    to="/all-site-visits"
                  >
                    All Site Visit Bookings
                  </Link>
                )}
              </div>
            </div>
          )}

          {/* Sites */}
          {(isAdmin || isOperations || isHOL || isAnalyst) && (
            <div className="collapse collapse-arrow border border-base-300 bg-base-100 rounded-box my-1">
              <input type="checkbox" className="peer" />
              <div className="collapse-title font-bold">Sites</div>
              <div className="collapse-content -mt-3 flex flex-col menu bg-base-100">
                <Link
                  className="mt-1 hover:bg-base-200 rounded p-2"
                  to="/view-sites"
                >
                  View Sites
                </Link>
              </div>
            </div>
          )}

          {/* Vehicles */}
          <div className="collapse collapse-arrow border border-base-300 bg-base-100 rounded-box my-1">
            <input type="checkbox" className="peer" />
            <div className="collapse-title font-bold">
              Vehicles{" "}
              {(isAdmin || isHOL) && (
                <span
                  className={`badge badge-warning badge-sm text-white font-bold ${
                    hasPendingVehicleRequests ? "" : "hidden"
                  }`}
                >
                  {numPendingVehicleRequests}
                </span>
              )}
            </div>
            <div className="collapse-content -mt-3 flex flex-col menu bg-base-100">
              <Link
                to="/request-vehicle"
                className="mt-1 hover:bg-base-200 rounded p-2"
              >
                Request For A Vehicle
              </Link>
              {(isAdmin || isHOL) && (
                <Link
                  to="/vehicle-requests"
                  className="mt-1 hover:bg-base-200 rounded p-2"
                >
                  Vehicle Requests{" "}
                  <span
                    className={`badge badge-sm badge-warning text-white font-bold ${
                      hasPendingVehicleRequests ? "" : "hidden"
                    }`}
                  >
                    {numPendingVehicleRequests}
                  </span>
                </Link>
              )}
              <Link
                to="/past-vehicle-requests"
                className="mt-1 hover:bg-base-200 rounded p-2"
              >
                My Past Vehicle Requests
              </Link>
              {(isAdmin || isOperations || isHOL) && (
                <>
                  <Link
                    className="mt-1 hover:bg-base-200 rounded p-2"
                    to="/create-vehicle"
                  >
                    Add Vehicle
                  </Link>
                  <Link
                    className="mt-1 hover:bg-base-200 rounded p-2"
                    to="/vehicles"
                  >
                    View Vehicles
                  </Link>
                </>
              )}
              {(isAdmin || isDriver) && (
                <Link
                  className="mt-1 hover:bg-base-200 rounded p-2"
                  to="/assigned-vehicle-requests"
                >
                  Assigned Vehicle Requests
                </Link>
              )}
            </div>
          </div>

          {/* Special Assignments */}
          {(isAdmin || isHOL || isDriver) && (
            <div className="collapse collapse-arrow border border-base-300 bg-base-100 rounded-box my-1">
              <input type="checkbox" className="peer" />
              <div className="collapse-title font-bold">
                Special Assignments
              </div>
              <div className="collapse-content -mt-3 flex flex-col menu bg-base-100">
                {(isHOL || isAdmin) && (
                  <Link
                    className="mt-1 hover:bg-base-200 rounded p-2"
                    to="/create-special-assignment"
                  >
                    Create Special Assignment
                  </Link>
                )}
                {(isDriver || isAdmin) && (
                  <Link
                    className="mt-1 hover:bg-base-200 rounded p-2"
                    to="/assigned-special-assignments"
                  >
                    Assigned Special Assignments
                  </Link>
                )}
              </div>
            </div>
          )}

          {/* Clients */}
          {(isMarketer ||
            isHOS ||
            isGM ||
            isAdmin ||
            isHOL ||
            isOperations ||
            isAnalyst) && (
            <div className="collapse collapse-arrow border border-base-300 bg-base-100 rounded-box my-1">
              <input type="checkbox" className="peer" />
              <div className="collapse-title font-bold">Clients</div>
              <div className="collapse-content -mt-3 flex flex-col menu bg-base-100">
                {(isMarketer || isAdmin) && (
                  <Link
                    className="mt-1 hover:bg-base-200 rounded p-2"
                    to="/my-clients-contacts"
                  >
                    My Clients' Contacts
                  </Link>
                )}
                {(isHOS ||
                  isGM ||
                  isAdmin ||
                  isHOL ||
                  isOperations ||
                  isAnalyst) && (
                  <Link
                    className="mt-1 hover:bg-base-200 rounded p-2"
                    to="/all-clients-contacts"
                  >
                    All Clients' Contacts
                  </Link>
                )}
              </div>
            </div>
          )}

          {/* Drivers */}
          {(isAdmin || isHOL || isOperations) && (
            <div className="collapse collapse-arrow border border-base-300 bg-base-100 rounded-box my-1">
              <input type="checkbox" className="peer" />
              <div className="collapse-title font-bold">Drivers</div>
              <div className="collapse-content -mt-3 flex flex-col menu bg-base-100">
                <Link
                  className="mt-1 hover:bg-base-200 rounded p-2"
                  to="/drivers"
                >
                  View Drivers
                </Link>
              </div>
            </div>
          )}

          {/* Users */}
          {(isAdmin || isHOL) && (
            <li>
              <Link to="/users" className="font-bold my-1">
                Users
              </Link>
            </li>
          )}

          {/* Feedback */}
          <li>
            <Link to="/feedback" className="font-bold my-1">
              System Feedback
            </Link>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default Sidebar;
