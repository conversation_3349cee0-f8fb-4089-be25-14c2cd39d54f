const express = require("express");
const multer = require("multer");
const authenticateJWT = require("../../middleware/authenticateJWT");
const router = express.Router();

// Set up storage for uploaded files
const upload = multer({ dest: "uploads/" });

module.exports = (pool) => {
  // GET all workplan activities for the authenticated user
  router.get("/", authenticateJWT, (req, res) => {
    const { user_id } = req.query;
    pool.query(
      `SELECT 
        w_a.*,
        w.marketer_id AS marketer_id,
        u.fullnames AS name
      FROM workplan_activities w_a
      INNER JOIN workplans w
      ON w_a.workplan_id = w.id
      INNER JOIN defaultdb.users u
      ON u.user_id = w.marketer_id
      WHERE marketer_id = ? AND w.status = 'approved'
      ORDER BY w_a.date ASC`,
      [user_id],
      (err, results) => {
        if (err) {
          console.error(err);
          res.status(500).json({ message: "Server Error" });
        } else {
          res.json(results);
        }
      }
    );
  });

  // GET all workplan activities for marketers in a certain region
  router.get("/all", authenticateJWT, (req, res) => {
    const { user_id } = req.query;

    // fetch the user's team and region based on their id
    const userInfoQuery =
      "SELECT team, t.region FROM defaultdb.users u JOIN defaultdb.teams t ON u.team = t.id JOIN defaultdb.region r ON t.region = r.id WHERE u.user_id = ?";

    pool.query(userInfoQuery, [user_id], (err, userInfoResults) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        const userTeam = userInfoResults[0].team;
        const userRegion = userInfoResults[0].region;

        // query to select workplan activities for marketers in a certain region
        const query = `
          SELECT wa.*, w.marketer_id AS marketer_id, u.fullnames AS name
          FROM workplan_activities wa
          INNER JOIN workplans w ON wa.workplan_id = w.id
          INNER JOIN defaultdb.users u ON u.user_id = w.marketer_id
          INNER JOIN defaultdb.teams t ON u.team = t.id
          INNER JOIN defaultdb.region r ON t.region = r.id
          WHERE t.id = ? AND r.id = ?
          ORDER BY wa.date ASC`;

        pool.query(query, [userTeam, userRegion], (err, results) => {
          if (err) {
            console.error(err);
            res.status(500).json({ message: "Server Error" });
          } else {
            res.json(results);
          }
        });
      }
    });
  });

  // GET workplan activities in the Sales Manager's team for updating remarks
  router.get("/team-activities", (req, res) => {
    const { user_id } = req.query;

    // Fetch the user's team based on their user_id
    const teamQuery = "SELECT team FROM defaultdb.users WHERE user_id = ?";
    pool.query(teamQuery, [user_id], (err, teamResults) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        const userTeam = teamResults[0].team;

        // Query to select workplan activities in the Sales Manager's team
        const query = `
        SELECT w.*, wpa.*, u.fullnames AS marketer_name
          FROM workplans w
        INNER JOIN defaultdb.users u ON w.marketer_id = u.user_id
        INNER JOIN workplan_activities wpa ON w.id = wpa.workplan_id
        WHERE u.team = ? AND wpa.measurable_achievement IS NOT NULL 
        AND wpa.variance IS NOT NULL 
        AND wpa.comments IS NOT NULL`;

        pool.query(query, [userTeam], (err, results) => {
          if (err) {
            console.error(err);
            res.status(500).json({ message: "Server Error" });
          } else {
            res.json(results);
          }
        });
      }
    });
  });

  // GET the most prolific activities done within a week with filled values
  router.get("/most-prolific-activities", (req, res) => {
    const { limit } = req.query;
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7); // Calculate the date one week ago

    const query = `
    SELECT
      wa.title,
      COUNT(wa.id) AS activity_count
    FROM workplan_activities wa
    WHERE wa.date >= ?
      AND wa.measurable_achievement IS NOT NULL
      AND wa.variance IS NOT NULL
      AND wa.comments IS NOT NULL
    GROUP BY wa.title
    ORDER BY activity_count DESC
    LIMIT ?`;

    pool.query(query, [oneWeekAgo, parseInt(limit)], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        res.json(results);
      }
    });
  });

  // GET all marketers for the past one week, including those with nil activity count
  router.get("/most-active-marketers", (req, res) => {
    const { limit } = req.query; // Specify how many top marketers to retrieve
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7); // Calculate the date one week ago

    const query = `
    SELECT
      u.fullnames AS marketer_name,
      u.user_id AS marketer_id,
      CAST(SUM(CASE WHEN wa.date >= ? THEN 1 ELSE 0 END) AS UNSIGNED) AS activity_count
    FROM workplan_activities wa
    RIGHT JOIN workplans w ON wa.workplan_id = w.id
    RIGHT JOIN defaultdb.users u ON u.user_id = w.marketer_id
    GROUP BY u.fullnames, u.user_id
    ORDER BY activity_count DESC
    LIMIT ?`;

    pool.query(query, [oneWeekAgo, parseInt(limit)], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        res.json(results);
      }
    });
  });

  // GET a specific workplan activity
  router.get("/:id", (req, res) => {
    // extract id, to be used to find the specific activity
    const { id } = req.params;
    // query to extract relevant WPA details
    const query = `SELECT 
        wa.*,
        w.marketer_id AS marketer_id,
        u.fullnames AS name
      FROM workplan_activities wa
      INNER JOIN workplans w
      ON wa.workplan_id = w.id
      INNER JOIN defaultdb.users u
      ON u.user_id = w.marketer_id 
      WHERE wa.id = ?`;
    // execute query
    pool.query(query, [id], (err, activity) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else if (activity.length > 0) {
        res.json(activity[0]);
      } else {
        res.status(404).json({ message: "Workplan activity not found" });
      }
    });
  });

  // CREATE a new workplan activity
  router.post("/", authenticateJWT, (req, res) => {
    // extract activities from the body
    const activities = req.body;
    const query =
      "INSERT INTO workplan_activities (workplan_id, date, time, title, expected_output) VALUES (?, ?, ?, ?, ?)";

    // array to store the results of each INSERT query
    const results = [];

    // iterate over the activities and execute the INSERT query for each activity
    activities.forEach((activity) => {
      const { workplan_id, date, time, title, expected_output } = activity;

      pool.query(
        query,
        [workplan_id, date, time, title, expected_output],
        (err, result) => {
          if (err) {
            console.error(err);
            results.push({
              error: true,
              message: "Error creating workplan activity",
            });
          } else {
            results.push({
              error: false,
              message: "Workplan activity created successfully",
            });
          }

          // check if all queries have completed
          if (results.length === activities.length) {
            // check if any errors occurred during the queries
            const hasErrors = results.some((result) => result.error);
            if (hasErrors) {
              res.status(500).json({ message: "Server Error" });
            } else {
              res.json({ message: "Workplan activities created successfully" });
            }
          }
        }
      );
    });
  });

  // UPDATE an existing workplan activity
  router.patch("/:id", upload.single("supporting_document"), (req, res) => {
    try {
      // Workplan Activity ID
      const { id } = req.params;
      const {
        workplan_id,
        date,
        time,
        title,
        expected_output,
        measurable_achievement,
        variance,
        comments,
        sm_remarks,
        rm_remarks,
      } = req.body;

      const supportingDocument = req.file ? req.file.filename : null;

      // Check the existence of the associated workplan activity
      pool.query(
        "SELECT * FROM workplan_activities WHERE id = ?",
        [id],
        (err, result) => {
          if (err) {
            console.error(err);
            res.status(500).json({ message: "Server Error" });
          } else {
            let query = `
          UPDATE workplan_activities 
          SET workplan_id = ?, date = ?, time = ?, title = ?, 
          expected_output = ?, measurable_achievement = ?, 
          variance = ?, comments = ?, sm_remarks = ?, 
          rm_remarks = ?, supporting_document = ? WHERE id = ?`;

            pool.query(
              query,
              [
                workplan_id,
                date,
                time,
                title,
                expected_output,
                measurable_achievement,
                variance,
                comments,
                sm_remarks,
                rm_remarks,
                supportingDocument,
                id,
              ],
              (err, result) => {
                if (err) {
                  console.error(err);
                  res.status(500).json({ message: "Server Error" });
                } else if (result.affectedRows > 0) {
                  res.json({
                    message: "Workplan activity updated successfully",
                  });
                } else {
                  res
                    .status(404)
                    .json({ message: "Workplan activity not found" });
                }
              }
            );
          }
        }
      );
    } catch (err) {
      console.error(err);
      res.status(500).json({ message: "Server Error" });
    }
  });

  // DELETE a workplan activity
  router.delete("/:id", authenticateJWT, (req, res) => {
    // select id of the WPA
    const { id } = req.params;
    // query to delete THAT WPA
    const query = "DELETE FROM workplan_activities WHERE id = ?";
    // execute deletion
    pool.query(query, [id], (err, result) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else if (result.affectedRows > 0) {
        res.json({ message: "Workplan activity deleted successfully" });
      } else {
        res.status(404).json({ message: "Workplan activity not found" });
      }
    });
  });

  return router;
};
