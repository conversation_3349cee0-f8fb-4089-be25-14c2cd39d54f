import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { Tail<PERSON>pin } from "react-loader-spinner";

import Sidebar from "../../components/Sidebar";
import formatTime from "../../../utils/formatTime";
import { Ban, FilePenLine } from "lucide-react";

const MySiteVisits = () => {
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [siteVisits, setSiteVisits] = useState([]);
  const [loading, setLoading] = useState(false); // NEW: track loading state

  const token = useSelector((state) => state.user.token);
  const userId = useSelector((state) => state.user.user.user_id);

  const navigate = useNavigate();

  useEffect(() => {
    const fetchSiteVisits = async () => {
      try {
        setLoading(true); // Start loader
        const response = await fetch(
          "https://workspace.optiven.co.ke/api/site-visit-requests",
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const data = await response.json();
        setSiteVisits(data);
      } catch (error) {
        console.error("Error fetching site visits:", error);
      } finally {
        setLoading(false); // Stop loader
      }
    };

    fetchSiteVisits();
  }, [token]);

  const cancelSiteVisit = async (siteVisitId) => {
    try {
      const response = await fetch(
        `https://workspace.optiven.co.ke/api/site-visit-requests/cancel-site-visit/${siteVisitId}`,
        {
          method: "PATCH",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const data = await response.json();

      if (response.ok) {
        // Update local state
        setSiteVisits((prev) =>
          prev.map((siteVisit) =>
            siteVisit.id === siteVisitId
              ? { ...siteVisit, status: "cancelled" }
              : siteVisit
          )
        );
        toast.success(data.message, {
          position: "top-center",
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
      } else {
        toast.error(data.message || "Error cancelling site visit.", {
          position: "top-center",
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
      }
    } catch (error) {
      console.error("Error cancelling site visit:", error);
      toast.error(error.message || "Error cancelling site visit.", {
        position: "top-center",
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
    }
  };

  const userSiteVisits = siteVisits.filter(
    (siteVisit) => siteVisit.marketer_id === userId
  );

  const handleStartDateChange = (e) => setStartDate(e.target.value);
  const handleEndDateChange = (e) => setEndDate(e.target.value);

  const filteredSiteVisits = userSiteVisits.filter((item) => {
    const itemDate = new Date(item.pickup_date);
    const startDateObj = startDate && new Date(startDate);
    const endDateObj = endDate && new Date(endDate);

    if (startDateObj && endDateObj) {
      return itemDate >= startDateObj && itemDate <= endDateObj;
    } else if (startDateObj) {
      return itemDate >= startDateObj;
    } else if (endDateObj) {
      return itemDate <= endDateObj;
    } else {
      return true;
    }
  });

  const startTrip = async (id) => {
    try {
      const response = await fetch(
        `https://workspace.optiven.co.ke/api/site-visits/start-trip/${id}`,
        {
          method: "PATCH",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        setSiteVisits((prev) =>
          prev.map((sv) =>
            sv.id === id ? { ...sv, status: "in_progress" } : sv
          )
        );
        toast.success("Trip set to in progress.", {
          position: "top-center",
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
      } else {
        const data = await response.json();
        toast.error("An error occurred while attempting to start trip.", {
          position: "top-center",
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        console.error("Error starting trip:", data.message);
      }
    } catch (error) {
      toast.error("An error occurred while attempting to start trip.", {
        position: "top-center",
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      console.error("Error starting trip:", error);
    }
  };

  const endTrip = async (id) => {
    try {
      const response = await fetch(
        `https://workspace.optiven.co.ke/api/site-visits/end-trip/${id}`,
        {
          method: "PATCH",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        setSiteVisits((prev) =>
          prev.map((sv) => (sv.id === id ? { ...sv, status: "complete" } : sv))
        );
        toast.success("Trip set to complete.", {
          position: "top-center",
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
      } else {
        const data = await response.json();
        toast.error("An error occurred while attempting to end trip.", {
          position: "top-center",
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        console.error("Error ending trip:", data.message);
      }
    } catch (error) {
      toast.error("An error occurred while attempting to end trip.", {
        position: "top-center",
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      console.error("Error ending trip:", error);
    }
  };

  // If data is still loading, show a spinner (like in Home.js)
  if (loading) {
    return (
      <Sidebar>
        <div className="flex flex-col items-center justify-center min-h-[70vh]">
          <TailSpin height="40" width="40" color="#999999" />
          <p className="mt-4 italic text-gray-700 text-base">Loading...</p>
        </div>
      </Sidebar>
    );
  }

  return (
    <Sidebar>
      <div className="container px-4 pb-6 mx-auto">
        {/* DATE FILTERS */}
        <div className="flex justify-center items-center my-4">
          <div className="flex space-x-2 items-center">
            <input
              type="date"
              className="input input-bordered max-w-xs"
              value={startDate}
              onChange={handleStartDateChange}
            />
            <span className="text-lg font-bold">to</span>
            <input
              type="date"
              className="input input-bordered max-w-xs"
              value={endDate}
              onChange={handleEndDateChange}
            />
          </div>
        </div>

        {/* TABLE WRAPPER WITH BETTER RESPONSIVE STYLE */}
        <div className="bg-white shadow-md rounded-lg p-4">
          <div className="overflow-x-auto">
            <table className="min-w-full table-auto">
              <thead>
                <tr className="bg-gray-200">
                  <th className="px-4 py-2 text-left">Index</th>
                  <th className="px-4 py-2 text-left">Site Name</th>
                  <th className="px-4 py-2 text-left">Clients</th>
                  <th className="px-4 py-2 text-left">Pickup Location</th>
                  <th className="px-4 py-2 text-left">Date</th>
                  <th className="px-4 py-2 text-left">Time</th>
                  <th className="px-4 py-2 text-left">Status</th>
                  <th className="px-4 py-2 text-left">Action</th>
                </tr>
              </thead>
              <tbody>
                {filteredSiteVisits
                  .sort((a, b) => b.id - a.id)
                  .map((siteVisit, index) => {
                    const isComplete = siteVisit.status === "complete"; // "Pending Survey"
                    const isInProgress = siteVisit.status === "in_progress";
                    const isPendingOrApproved =
                      siteVisit.status === "pending" ||
                      siteVisit.status === "approved";

                    return (
                      <tr key={siteVisit.id} className="hover:bg-gray-100">
                        <td className="border px-4 py-2">{index + 1}</td>
                        <td className="border px-4 py-2">
                          {siteVisit.site_name}
                        </td>
                        <td className="border px-4 py-2">
                          {siteVisit.num_clients}
                        </td>
                        <td className="border px-4 py-2">
                          {siteVisit.pickup_location}
                        </td>
                        <td className="border px-4 py-2">
                          {new Date(siteVisit.pickup_date).toLocaleDateString(
                            "en-GB"
                          )}
                        </td>
                        <td className="border px-4 py-2">
                          {formatTime(siteVisit.pickup_time)}
                        </td>
                        <td
                          className="border px-4 py-2"
                          style={{
                            textTransform: "uppercase",
                            fontWeight: "bold",
                            color:
                              siteVisit.status === "rejected"
                                ? "red"
                                : siteVisit.status === "complete"
                                ? "purple"
                                : siteVisit.status === "reviewed"
                                ? "green"
                                : "black",
                          }}
                        >
                          {isInProgress
                            ? "In Progress"
                            : isComplete
                            ? "Pending Survey"
                            : siteVisit.status === "reviewed"
                            ? "Complete"
                            : siteVisit.status}
                        </td>
                        <td className="border px-4 py-2">
                          {/* Edit if pending */}
                          {siteVisit.status === "pending" && (
                            <button
                              className="btn btn-sm text-white btn-warning mr-1"
                              onClick={() =>
                                navigate(`/edit-site-visit/${siteVisit.id}`)
                              }
                            >
                              Edit <FilePenLine size={"16"} className="ml-1" />
                            </button>
                          )}

                          {/* Cancel if pending/approved */}
                          {isPendingOrApproved && (
                            <button
                              className="btn btn-sm btn-gray-500 mr-1"
                              onClick={() => cancelSiteVisit(siteVisit.id)}
                            >
                              Cancel <Ban size={"16"} className="ml-1" />
                            </button>
                          )}

                          {/* Self-drive logic => Start / End Trip */}
                          {(siteVisit.status === "approved" || isInProgress) &&
                            // FIXED: Check if driver name is one of the three
                            [
                              "own means",
                              "reliever",
                              "to be discussed",
                            ].includes(
                              siteVisit.driver_name.trim().toLowerCase()
                            ) && (
                              <button
                                className={`btn btn-sm ${
                                  isInProgress ? "btn-error" : "btn-primary"
                                } text-white mr-1`}
                                onClick={() =>
                                  isInProgress
                                    ? endTrip(siteVisit.id)
                                    : startTrip(siteVisit.id)
                                }
                              >
                                {isInProgress ? "End Trip" : `Start Trip`}
                              </button>
                            )}

                          {/* If complete => user can fill survey */}
                          {isComplete && (
                            <button
                              className="btn btn-sm btn-secondary text-white"
                              onClick={() =>
                                navigate(`/survey/${siteVisit.id}`)
                              }
                            >
                              Complete Survey
                            </button>
                          )}
                        </td>
                      </tr>
                    );
                  })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </Sidebar>
  );
};

export default MySiteVisits;
