import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import axios from "axios";
import Sidebar from "../components/Sidebar";
import {
  ClipboardList,
  CheckCircle,
  <PERSON><PERSON>,
  Grid,
  DollarSign,
} from "lucide-react";

export default function ProjectDashboard() {
  const [projects, setProjects] = useState([]);
  const [filteredProjects, setFilteredProjects] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");

  // Sample metrics (could be fetched dynamically)
  const [completedSalesAwaiting, setCompletedSalesAwaiting] = useState(25);
  const [completedSalesDischarged, setCompletedSalesDischarged] = useState(40);
  const [leadFilesCompletion, setLeadFilesCompletion] = useState(70); // percentage
  const [plotsAvailable, setPlotsAvailable] = useState(120);
  const [stockValue, setStockValue] = useState(450000); // total stock value

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const department = useSelector((state) => state.user.user.department);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const response = await axios.get(
          "https://workspace.optiven.co.ke/api/mib/recon/specific"
        );
        if (response.data && Array.isArray(response.data.projects)) {
          setProjects(response.data.projects);
          setFilteredProjects(response.data.projects);
        } else {
          setError("Unexpected data format received from server.");
        }
      } catch (err) {
        setError("Failed to load projects. Please try again later.");
      } finally {
        setLoading(false);
      }
    };
    fetchProjects();
  }, []);

  useEffect(() => {
    if (!searchQuery) {
      setFilteredProjects(projects);
    } else {
      const q = searchQuery.toLowerCase();
      setFilteredProjects(
        projects.filter((p) =>
          p.Project_Name.toLowerCase().includes(q)
        )
      );
    }
  }, [searchQuery, projects]);

  if (loading) {
    return (
      <section className="min-h-screen flex items-center justify-center">
        <p>Loading projects...</p>
      </section>
    );
  }

  if (error) {
    return (
      <section className="min-h-screen flex items-center justify-center">
        <p className="text-red-500">{error}</p>
      </section>
    );
  }

  const metrics = [
    {
      title: "Completed Sales Awaiting",
      value: completedSalesAwaiting,
      icon: ClipboardList,
      bg: "bg-orange-100",
      color: "text-orange-500",
    },
    {
      title: "Completed Sales Discharged",
      value: completedSalesDischarged,
      icon: CheckCircle,
      bg: "bg-green-100",
      color: "text-green-500",
    },
    {
      title: "Lead Files Completion",
      value: `${leadFilesCompletion}%`,
      icon: PieChart,
      bg: "bg-blue-100",
      color: "text-blue-500",
      isProgress: true,
    },
    {
      title: "Plots Available",
      value: plotsAvailable,
      icon: Grid,
      bg: "bg-purple-100",
      color: "text-purple-500",
    },
    {
      title: "Stock Value",
      value: `Ksh. ${stockValue.toLocaleString()}`,
      icon: DollarSign,
      bg: "bg-teal-100",
      color: "text-teal-500",
    },
  ];

  return (
    <Sidebar>
      <section className="min-h-screen bg-gray-100 p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-3xl font-bold text-gray-800">
              Project Dashboard
            </h2>
            <p className="text-gray-600">Overview of your projects 
              {/* and metrics */}
              </p>
          </div>
          {/* Could add avatar or filters here */}
        </div>

        {/* Summary Cards */}
        {/* <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 mb-8">
          {metrics.map((m) => (
            <div
              key={m.title}
              className="flex items-center p-4 bg-white rounded-lg shadow hover:shadow-lg transition-shadow"
            >
              <div className={`${m.bg} p-3 rounded-full mr-4`}>
                <m.icon className={`${m.color} w-6 h-6`} />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-500">{m.title}</p>
                <p className="text-xl font-semibold text-gray-800">{m.value}</p>
                {m.isProgress && (
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2 overflow-hidden">
                    <div
                      className="h-full bg-blue-500"
                      style={{ width: `${leadFilesCompletion}%` }}
                    />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div> */}

        {/* Projects Table */}
        <div className="bg-white shadow rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-700">
              All Projects ({filteredProjects.length})
            </h3>
            <input
              type="text"
              placeholder="Search Projects..."
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-64"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full table-auto">
              <thead>
                <tr className="bg-blue-600 text-white uppercase text-sm">
                  <th className="py-3 px-6 text-left">Project Name</th>
                  <th className="py-3 px-6 text-center">Actions</th>
                </tr>
              </thead>
              <tbody className="text-gray-700 text-sm">
                {filteredProjects.length > 0 ? (
                  filteredProjects.map((project) => (
                    <tr
                      key={project.Project_Name}
                      className="border-b border-gray-200 hover:bg-gray-50 transition-colors"
                    >
                      <td className="py-3 px-6 whitespace-nowrap">
                        {project.Project_Name}
                      </td>
                      <td className="py-3 px-6 text-center">
                        <div className="flex justify-center space-x-2">
                          <Link
                            to={`/projects/${encodeURIComponent(
                              project.Project_Name
                            )}/plots`}
                            className="bg-blue-500 hover:bg-blue-600 text-white py-1 px-4 rounded text-sm transition-colors"
                          >
                            View Plots
                          </Link>
                          <Link
                            to={`/projects/${encodeURIComponent(
                              project.Project_Name
                            )}/transactions`}
                            className="bg-green-500 hover:bg-green-600 text-white py-1 px-4 rounded text-sm transition-colors"
                          >
                            Transactions
                          </Link>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan="2"
                      className="py-4 px-6 text-center text-gray-500"
                    >
                      No projects found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </section>
    </Sidebar>
  );
}
