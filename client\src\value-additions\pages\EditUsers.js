import axios from "axios";
import React, { useEffect, useState } from "react";
import Sidebar from "../components/Sidebar";
import { toast } from "react-toastify";
import { useLocation, useNavigate } from "react-router-dom";
// import { useSelector } from "react-redux";

const EditUsers = () => {
  // finding the userId
  const loc = useLocation();

  const url = loc.pathname;

  // Use a safer method to extract the supplierId from the URL
  const userId = url.split("/")[2];

  const navigate = useNavigate();

  const [name, setName] = useState("");
  const [contact, setContact] = useState("");
  const [role, setRole] = useState("");
  const [loading, setLoading] = useState(false);

  const roles = [
    { value: "supervisor", label: "supervisor" },
    { value: "caretaker", label: "caretaker" }
  ];


  useEffect(() => {
    // Fetch the labour data from the backend API
    if (userId) {
      axios
        .get(`https://workspace.optiven.co.ke/api/value_addition_users/${userId}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },

        }
        )
        .then((res) => {
          console.log(res.data);

          setName(res.data.name);
          setContact(res.data.contact);
          setRole(res.data.role);
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }, [userId]);

  const handleSubmit = (event) => {
    event.preventDefault();
    setLoading(true);
    const labour = {
      name,
      contact,
      role,
    };
    console.log(labour);

    // Send a PATCH request to update the casual labourer data in the backend API
    axios
      .patch(`https://workspace.optiven.co.ke/api/value_addition_users/${userId}`, labour,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },

      }
      )

      .then((res) => {
        setLoading(false);
        toast.success("user updated!", {
          position: "top-center",
          autoClose: 2000,
          hideProgressBar: true,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        console.log(res.data);
        navigate("/view-users");
      })
      .catch((err) => {
        setLoading(false);
        toast.error("Something went wrong. Please try again.", {
          position: "top-center",
          autoClose: 2000,
          hideProgressBar: true,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        console.log(err);
      });
  };

  return (
    <Sidebar>
      <div className="hero min-h-screen">
        <form onSubmit={handleSubmit} className="form-control w-full max-w-xs">
          
          <label htmlFor="name" className="label">
            <span className="label-text font-bold">name</span>
          </label>
          <input
            type="name"
            id="name"
            value={name} 
            onChange={(event) => setName(event.target.value)}
            className="input input-bordered w-full max-w-xs"
          />
            

          <label htmlFor="contact" className="label">
            <span className="label-text font-bold">Role</span>
          </label>
          <select
            type="role"
            id="role"
            value={role}
            onChange={(event) => setRole(event.target.value)}
            className="input input-bordered w-full max-w-xs"
          >
            <option value=""> Select role</option>
            {roles.map((options) => (
              <option key={options.value} value={options.value}>
                {options.label}
              </option>
            ))}
          </select>

          <label htmlFor="contact" className="label">
            <span className="label-text font-bold">Contact</span>
          </label>
          <input
            type="tel"
            id="contact"
            value={contact}
            onChange={(event) => setContact(event.target.value)}
            className="input input-bordered w-full max-w-xs"
          />

          <button
            type="submit"
            disabled={loading}
            id="submit"
            className="btn btn-primary w-full max-w-xs mt-4 text-white"
          >
            {loading ? "Saving..." : "Update User"}
          </button>
        </form>
      </div>
    </Sidebar>
  );
};

export default EditUsers;
