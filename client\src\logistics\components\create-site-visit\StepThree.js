import { ChevronLeft } from "lucide-react";
import React from "react";
import formatTime from "../../../utils/formatTime";

function StepThree({
  siteName,
  pickupLocation,
  pickupDate,
  pickupTime,
  clients,
  agree,
  setAgree,
  onPrevious,
  onSubmit,
}) {
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Confirm Details</h2>

      {/* Step 1 Summary */}
      <div className="border border-base-200 rounded p-4">
        <p>
          <strong>Site Name:</strong> {siteName || "Not Provided"}
        </p>
        <p>
          <strong>Pickup Location:</strong> {pickupLocation || "Not Provided"}
        </p>
        <p>
          <strong>Pickup Date:</strong>{" "}
          {pickupDate
            ? new Date(pickupDate).toLocaleDateString("en-GB")
            : "Not Provided"}
        </p>
        <p>
          <strong>Pickup Time:</strong> {pickupTime ? formatTime(pickupTime) : "Not Provided"}
        </p>
      </div>

      {/* Step 2 Summary */}
      <div className="border border-base-200 rounded p-4">
        <h3 className="font-medium mb-2">Clients</h3>
        {clients.map((c, i) => (
          <div key={i} className="mb-2">
            <p>
              <strong>Name:</strong> {c.firstName} {c.lastName}
            </p>
            <p>
              <strong>Phone:</strong> {c.phone}
            </p>
            <p>
              <strong>Email:</strong> {c.email || "No Email Provided"}
            </p>
            {i < clients.length - 1 && <hr className="my-2" />}
          </div>
        ))}
      </div>

      {/* Confirmation Checkbox */}
      <div className="form-control flex items-start">
        <label className="cursor-pointer label">
          <input
            type="checkbox"
            className="checkbox mr-2"
            checked={agree}
            onChange={(e) => setAgree(e.target.checked)}
          />
          <span className="label-text font-bold">
            I confirm the details above are correct
          </span>
        </label>
      </div>

      {/* Prev / Submit */}
      <div className="flex justify-between">
        <button
          type="button"
          onClick={onPrevious}
          className="btn btn-secondary rounded-full"
        >
          <ChevronLeft />
        </button>
        <button
          type="submit"
          className="btn btn-primary text-white"
          disabled={!agree}
        >
          Submit
        </button>
      </div>
    </div>
  );
}

export default StepThree;
