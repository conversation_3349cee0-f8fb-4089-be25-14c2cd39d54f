// src/pages/DischargeInitiated.js
import React, { useEffect, useState } from "react";
import axios from "axios";
import Sidebar from "../components/Sidebar";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

/**
 * Determines the next overall plot-level discharge status.
 * (For plot-level update after all receipts are approved.)
 */
const getNextStatus = (currentStatus, isApprove) => {
  if (!currentStatus) return "";
  if (currentStatus === "Submitted") {
    return isApprove ? "Approved Level 1" : "Rejected Level 1";
  } else if (currentStatus === "Approved Level 1") {
    return isApprove ? "Approved Level 2" : "Rejected Level 2";
  } else if (currentStatus === "Approved Level 2") {
    return isApprove ? "Submitted to Legal" : "Rejected Level 2";
  } else if (currentStatus === "Submitted to Legal") {
    return isApprove ? "Approved Legal" : "Rejected Legal";
  }
  return "";
};

/**
 * Determines the expected receipt-level approval status for the current level,
 * based on the plot’s discharge status.
 */
const getReceiptApprovalStatus = (plotDischarge, isApprove) => {
  if (plotDischarge === "Submitted") {
    return isApprove ? "Level1Approved" : "Level1Rejected";
  } else if (plotDischarge === "Approved Level 1") {
    return isApprove ? "Level2Approved" : "Level2Rejected";
  } else if (plotDischarge === "Submitted to Legal") {
    return isApprove ? "LegalApproved" : "LegalRejected";
  }
  // Fallback
  return isApprove ? "DischargeApproved" : "DischargeRejected";
};

export default function DischargeInitiated() {
  const [plots, setPlots] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // pagination for plots
  const [plotPage, setPlotPage] = useState(1);
  const plotsPerPage = 10;
  const totalPlotPages = Math.ceil(plots.length / plotsPerPage);
  const displayedPlots = plots.slice(
    (plotPage - 1) * plotsPerPage,
    plotPage * plotsPerPage
  );

  // Modal state
  const [modalOpen, setModalOpen] = useState(false);
  const [modalPlot, setModalPlot] = useState(null);
  const [receipts, setReceipts] = useState([]);
  const [loadingReceipts, setLoadingReceipts] = useState(false);

  // sales agreements state
  const [agreements, setAgreements] = useState([]);
  const [loadingAgreements, setLoadingAgreements] = useState(false);

  // pagination for receipts
  const [receiptsPage, setReceiptsPage] = useState(1);
  const receiptsPerPage = 10;
  const totalReceiptsPages = Math.ceil(receipts.length / receiptsPerPage);
  const displayedReceipts = receipts.slice(
    (receiptsPage - 1) * receiptsPerPage,
    receiptsPage * receiptsPerPage
  );

  useEffect(() => {
    axios
      .get("https://workspace.optiven.co.ke/api/mib/plots/discharge-pending")
      .then(({ data }) => {
        if (Array.isArray(data.plots)) setPlots(data.plots);
        else setError("Unexpected data format.");
      })
      .catch(() => setError("Failed to load pending discharge plots."))
      .finally(() => setLoading(false));
  }, []);

  const openModal = async (plot) => {
    setModalPlot(plot);
    setModalOpen(true);
    setReceipts([]);
    setAgreements([]);
    setReceiptsPage(1);
    setLoadingReceipts(true);
    setLoadingAgreements(true);

    // fetch receipts
    try {
      const { data } = await axios.get(
        `https://workspace.optiven.co.ke/api/mib/plots/${encodeURIComponent(
          plot.plot_number
        )}`
      );
      if (Array.isArray(data.receipts)) {
        setReceipts(data.receipts);
      } else {
        toast.error("No receipts found for this plot.");
      }
    } catch {
      toast.error("Could not load receipts for this plot.");
    } finally {
      setLoadingReceipts(false);
    }

    // fetch agreements
    try {
      const { data } = await axios.get(
        `https://workspace.optiven.co.ke/api/sla/plots/${encodeURIComponent(
          plot.plot_number
        )}/agreements`
      );
      if (Array.isArray(data)) {
        setAgreements(data);
      } else {
        toast.error("No sales agreements found.");
      }
    } catch {
      toast.error("Could not load sales agreements.");
    } finally {
      setLoadingAgreements(false);
    }
  };

  const closeModal = () => {
    setModalOpen(false);
    setModalPlot(null);
    setReceipts([]);
    setAgreements([]);
  };

  const updateReceiptApproval = async (id, isApprove, comments) => {
    const status = getReceiptApprovalStatus(modalPlot.discharge, isApprove);
    try {
      await axios.patch(
        `https://workspace.optiven.co.ke/api/mib/receipts/${id}/approval`,
        { approval_status: status, approval_comments: comments }
      );
      toast.success(`Receipt ${id} → ${status}`);
      setReceipts((r) =>
        r.map((x) =>
          x.id === id
            ? { ...x, approval_status: status, approval_comments: comments }
            : x
        )
      );
    } catch {
      toast.error("Failed to update receipt.");
    }
  };

  const confirmDischarge = async () => {
    const expected = getReceiptApprovalStatus(modalPlot.discharge, true);
    const allOK = receipts.every((r) => r.approval_status === expected);
    if (!allOK) {
      toast.error("All receipts must be approved at this level.");
      return;
    }
    const next = getNextStatus(modalPlot.discharge, true);
    if (!next) {
      toast.error("Cannot determine next status.");
      return;
    }
    try {
      await axios.patch(
        `https://workspace.optiven.co.ke/api/mib/plots/${encodeURIComponent(
          modalPlot.plot_number
        )}/discharge`,
        { discharge: next }
      );
      toast.success(`Plot → ${next}`);
      setPlots((p) =>
        p.map((x) =>
          x.plot_number === modalPlot.plot_number
            ? { ...x, discharge: next }
            : x
        )
      );
      closeModal();
    } catch {
      toast.error("Failed to update plot.");
    }
  };

  if (loading) {
    return (
      <Sidebar>
        <div className="p-8 flex items-center justify-center">
          <div
            className="radial-progress animate-spin"
            style={{
              "--value": 70,
              "--size": "4rem",
              "--thickness": "0.1rem",
            }}
          />
          <p className="ml-4 text-lg">Loading plots…</p>
        </div>
      </Sidebar>
    );
  }
  if (error) {
    return (
      <Sidebar>
        <div className="p-8 text-red-600">{error}</div>
      </Sidebar>
    );
  }

  return (
    <Sidebar>
      <div className="p-8 bg-gray-50 min-h-screen">
        <h1 className="text-3xl font-bold mb-6">Discharge Pending Plots</h1>

        <div className="bg-white shadow-xl rounded-lg overflow-hidden">
          <table className="table table-zebra w-full">
            <thead className="bg-gray-100">
              <tr>
                <th className="px-4 py-2">#</th>
                <th className="px-4 py-2">Plot</th>
                <th className="px-4 py-2">Customer</th>
                <th className="px-4 py-2">Bank</th>
                <th className="px-4 py-2">Price</th>
                <th className="px-4 py-2">Status</th>
                <th className="px-4 py-2">Action</th>
              </tr>
            </thead>
            <tbody>
              {displayedPlots.map((plot, i) => {
                const idx = (plotPage - 1) * plotsPerPage + i + 1;
                const ok = [
                  "Submitted",
                  "Approved Level 1",
                  "Submitted to Legal",
                ].includes(plot.discharge);
                return (
                  <tr key={plot.plot_number}>
                    <td className="px-4 py-2">{idx}</td>
                    <td className="px-4 py-2">{plot.plot_number}</td>
                    <td className="px-4 py-2">{plot.Customer_Name}</td>
                    <td className="px-4 py-2">{plot.Bank_Account}</td>
                    <td className="px-4 py-2">
                      {plot.purchase_price
                        ? `Ksh. ${parseFloat(
                            plot.purchase_price
                          ).toLocaleString()}`
                        : "N/A"}
                    </td>
                    <td className="px-4 py-2">{plot.discharge}</td>
                    <td className="px-4 py-2">
                      {ok ? (
                        <button
                          className="btn btn-sm btn-primary"
                          onClick={() => openModal(plot)}
                        >
                          Process
                        </button>
                      ) : (
                        "N/A"
                      )}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {totalPlotPages > 1 && (
          <div className="flex justify-center items-center space-x-4 mt-4">
            <button
              className="btn btn-outline btn-sm"
              disabled={plotPage === 1}
              onClick={() => setPlotPage((p) => p - 1)}
            >
              ← Prev
            </button>
            <span className="text-sm font-medium">
              Page {plotPage} of {totalPlotPages}
            </span>
            <button
              className="btn btn-outline btn-sm"
              disabled={plotPage === totalPlotPages}
              onClick={() => setPlotPage((p) => p + 1)}
            >
              Next →
            </button>
          </div>
        )}

        {/* RECEIPTS & AGREEMENTS MODAL */}
        {modalOpen && modalPlot && (
          <div
            className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center p-4"
            onClick={closeModal}
          >
            <div
              className="bg-white rounded-2xl shadow-2xl w-full max-w-5xl h-[90vh] overflow-y-auto p-8"
              onClick={(e) => e.stopPropagation()}
            >
              <h2 className="text-2xl font-bold border-b pb-2 mb-4">
                Plot {modalPlot.plot_number}
              </h2>
              <div className="grid grid-cols-2 gap-4 mb-6">
                <p>
                  <strong>Customer:</strong> {modalPlot.Customer_Name}
                </p>
                <p>
                  <strong>Status:</strong> {modalPlot.discharge}
                </p>
              </div>

              {/* Sales Agreements */}
              <div className="mb-6">
                <h3 className="text-xl font-semibold mb-2">
                  Sales Agreements
                </h3>
                {loadingAgreements ? (
                  <p>Loading agreements…</p>
                ) : agreements.length > 0 ? (
                  <ul className="list-disc list-inside mb-4">
                    {agreements.map((a) => (
                      <li key={a.id}>
                        <a
                          href={`https://workspace.optiven.co.ke/api/sla/plots/${modalPlot.plot_number}/agreements/${a.id}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          download
                          className="text-blue-600 hover:underline"
                        >
                          {a.original_name}{" "}
                          <small className="text-gray-500">
                            ({new Date(a.uploaded_at).toLocaleString()})
                          </small>
                        </a>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p>No sales agreements found.</p>
                )}
              </div>

              {/* Receipts */}
              {loadingReceipts ? (
                <p>Loading receipts…</p>
              ) : receipts.length === 0 ? (
                <p>No receipts found.</p>
              ) : (
                <>
                  <table className="table w-full mb-4">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="px-3 py-2">ID</th>
                        <th className="px-3 py-2">Narration</th>
                        <th className="px-3 py-2">Reassign Reason</th>
                        <th className="px-3 py-2">Amount</th>
                        <th className="px-3 py-2">Status</th>
                        <th className="px-3 py-2">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {displayedReceipts.map((r) => (
                        <ReceiptRow
                          key={r.id}
                          receipt={r}
                          onUpdate={updateReceiptApproval}
                        />
                      ))}
                    </tbody>
                  </table>

                  {totalReceiptsPages > 1 && (
                    <div className="flex justify-center items-center space-x-4 mb-6">
                      <button
                        className="btn btn-outline btn-xs"
                        disabled={receiptsPage === 1}
                        onClick={() => setReceiptsPage((p) => p - 1)}
                      >
                        ← Prev
                      </button>
                      <span className="text-xs font-medium">
                        Page {receiptsPage} of {totalReceiptsPages}
                      </span>
                      <button
                        className="btn btn-outline btn-xs"
                        disabled={receiptsPage === totalReceiptsPages}
                        onClick={() => setReceiptsPage((p) => p + 1)}
                      >
                        Next →
                      </button>
                    </div>
                  )}

                  <div className="flex justify-end space-x-4">
                    <button className="btn btn-secondary" onClick={closeModal}>
                      Close
                    </button>
                    <button
                      className="btn btn-primary"
                      onClick={confirmDischarge}
                    >
                      Confirm Discharge
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        )}
      </div>

      <ToastContainer position="top-right" />
    </Sidebar>
  );
}

function ReceiptRow({ receipt, onUpdate }) {
  const [comments, setComments] = useState("");
  const handleApprove = () => onUpdate(receipt.id, true, comments);
  const handleReject = () => {
    if (!comments.trim()) return toast.error("Enter a reject reason");
    onUpdate(receipt.id, false, comments);
  };
  return (
    <tr>
      <td className="px-3 py-2">{receipt.id}</td>
      <td className="px-3 py-2">{receipt.Narration}</td>
      <td className="px-3 py-2">{receipt.reassign_comment || "—"}</td>
      <td className="px-3 py-2">{receipt.Amount_LCY || "N/A"}</td>
      <td className="px-3 py-2">{receipt.approval_status || "Pending"}</td>
      <td className="px-3 py-2 space-y-2">
        <textarea
          className="textarea textarea-bordered textarea-sm w-full"
          placeholder="Comments"
          value={comments}
          onChange={(e) => setComments(e.target.value)}
        />
        <div className="flex space-x-2">
          <button className="btn btn-success btn-xs" onClick={handleApprove}>
            ✔
          </button>
          <button className="btn btn-error btn-xs" onClick={handleReject}>
            ✖
          </button>
        </div>
      </td>
    </tr>
  );
}
