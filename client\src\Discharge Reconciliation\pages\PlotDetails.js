// src/pages/PlotDetails.js
import React, { useEffect, useState, useRef } from "react";
import axios from "axios";
import { useParams, useNavigate, Link } from "react-router-dom";
import Sidebar from "../components/Sidebar";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import {
  DollarSign,
  User,
  CreditCard,
  Briefcase,
  Download,
  Trash2,
  FilePlus,
} from "lucide-react";

export default function PlotDetails() {
  const { plotId } = useParams();
  const navigate = useNavigate();

  // refs
  const fileInputRef = useRef(null);

  // state
  const [receipts, setReceipts] = useState([]);
  const [agreements, setAgreements] = useState([]);
  const [purchasePrice, setPurchasePrice] = useState("N/A");
  const [bankAccount, setBankAccount] = useState("N/A");
  const [customerName, setCustomerName] = useState("N/A");
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState({ id: null, name: "" });

  useEffect(() => {
    const fetchPlotDetails = async () => {
      try {
        // 1) fetch receipts & plot info
        const { data } = await axios.get(
          `https://workspace.optiven.co.ke/api/mib/plots/${encodeURIComponent(
            plotId
          )}`
        );
        const receiptsData = data.receipts
          ? Array.isArray(data.receipts)
            ? data.receipts
            : [data.receipts]
          : [];
        setReceipts(receiptsData);
        if (receiptsData.length > 0) {
          const first = receiptsData[0];
          setPurchasePrice(first.purchase_price ?? "N/A");
          setBankAccount(first.Bank_Account ?? "N/A");
          setCustomerName(first.Customer_Name ?? "N/A");
        }

        // 2) fetch existing agreements
        const agRes = await axios.get(
          `https://workspace.optiven.co.ke/api/sla/plots/${encodeURIComponent(
            plotId
          )}/agreements`
        );
        setAgreements(agRes.data);
      } catch (err) {
        console.error(err);
        if (err.response?.status === 404) {
          toast.info("No payment records or agreements found for this plot.");
        } else {
          setError("Failed to load plot details.");
          toast.error("Failed to load plot details.");
        }
      } finally {
        setLoading(false);
      }
    };

    fetchPlotDetails();
  }, [plotId]);

  const formatDate = (d) => {
    if (!d) return "N/A";
    const t = Date.parse(d);
    if (isNaN(t)) return d;
    return new Date(t).toLocaleDateString(undefined, {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // totals & rules
  const totalPaid = receipts.reduce(
    (sum, r) => sum + (parseFloat(r.Amount_LCY) || 0),
    0
  );
  const purchaseNum = purchasePrice !== "N/A" ? parseFloat(purchasePrice) : 0;
  const isUnder = totalPaid < purchaseNum;
  const hasAgreement = agreements.length > 0;

  // upload new agreement
  const handleUpload = async () => {
    if (!file) return toast.error("Please select a file first.");
    const form = new FormData();
    form.append("agreement", file);
    try {
      await axios.post(
        `https://workspace.optiven.co.ke/api/sla/plots/${encodeURIComponent(
          plotId
        )}/agreements`,
        form,
        { headers: { "Content-Type": "multipart/form-data" } }
      );
      toast.success("Agreement uploaded.");
      // refresh list
      const { data } = await axios.get(
        `https://workspace.optiven.co.ke/api/sla/plots/${encodeURIComponent(
          plotId
        )}/agreements`
      );
      setAgreements(data);
      setFile(null);
      if (fileInputRef.current) fileInputRef.current.value = "";
    } catch (err) {
      console.error(err);
      toast.error("Upload failed.");
    }
  };

  // delete an agreement
  const confirmDelete = (id, name) => {
    setDeleteTarget({ id, name });
    setIsDeleteModalOpen(true);
  };

  const handleDelete = async () => {
    try {
      await axios.delete(
        `https://workspace.optiven.co.ke/api/sla/plots/${encodeURIComponent(
          plotId
        )}/agreements/${deleteTarget.id}`
      );
      setAgreements(agreements.filter((a) => a.id !== deleteTarget.id));
      toast.success("Agreement removed.");
    } catch (err) {
      console.error(err);
      toast.error("Could not delete agreement.");
    } finally {
      setIsDeleteModalOpen(false);
    }
  };

  // discharge logic
  const handleDischarge = async () => {
    if (isUnder) {
      return toast.error(
        "Cannot discharge: total paid is less than purchase price."
      );
    }
    if (!hasAgreement) {
      return toast.error("Cannot discharge: no signed sales agreement.");
    }
    try {
      await axios.patch(
        `https://workspace.optiven.co.ke/api/mib/plots/${encodeURIComponent(
          plotId
        )}/discharge`,
        { discharge: "initiated" }
      );
      toast.success("Discharge process initiated.");
      navigate("/discharge-approvals");
    } catch (err) {
      console.error(err);
      toast.error("Failed to initiate discharge.");
    }
  };

  if (loading) {
    return (
      <Sidebar>
        <div className="p-8 text-center">
          <div
            className="radial-progress animate-spin"
            style={{
              "--value": 70,
              "--size": "4rem",
              "--thickness": "0.1rem",
            }}
          />
          <span className="ml-4 text-lg">Loading plot details…</span>
        </div>
      </Sidebar>
    );
  }

  if (error) {
    return (
      <Sidebar>
        <div className="p-8 text-center text-red-600">{error}</div>
      </Sidebar>
    );
  }

  return (
    <Sidebar>
      <div className="max-w-6xl mx-auto p-6 space-y-8">
        {/* Breadcrumb + Back */}
        <div className="flex justify-between items-center">
          <nav className="text-sm text-gray-500">
            <span className="font-medium text-gray-700">Plots</span>
            <span className="mx-2">/</span>
            <span className="font-medium text-gray-700">{plotId}</span>
          </nav>
          <button onClick={() => navigate(-1)} className="btn btn-ghost btn-sm">
            ← Back
          </button>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white shadow rounded-lg p-4 flex items-center border-l-4 border-blue-500">
            <DollarSign className="w-8 h-8 text-blue-500" />
            <div className="ml-4">
              <h3 className="text-xs text-gray-500 uppercase mb-1">
                Purchase Price
              </h3>
              <p className="text-2xl font-semibold">
                {purchasePrice !== "N/A"
                  ? `Ksh. ${purchaseNum.toLocaleString()}`
                  : "N/A"}
              </p>
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-4 flex items-center border-l-4 border-green-500">
            <Briefcase className="w-8 h-8 text-green-500" />
            <div className="ml-4">
              <h3 className="text-xs text-gray-500 uppercase mb-1">
                Bank Account
              </h3>
              <p className="text-2xl font-semibold">{bankAccount}</p>
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-4 flex items-center border-l-4 border-yellow-500">
            <User className="w-8 h-8 text-yellow-500" />
            <div className="ml-4">
              <h3 className="text-xs text-gray-500 uppercase mb-1">
                Customer Name
              </h3>
              <p className="text-2xl font-semibold">{customerName}</p>
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-4 flex items-center border-l-4 border-purple-500">
            <CreditCard className="w-8 h-8 text-purple-500" />
            <div className="ml-4 flex-1">
              <h3 className="text-xs text-gray-500 uppercase mb-1">
                Total Paid
              </h3>
              <p className="text-2xl font-semibold">
                Ksh. {totalPaid.toLocaleString()}
              </p>
              {totalPaid > purchaseNum && (
                <span className="badge badge-warning mt-2 inline-block">
                  Overpayment: Ksh. {(totalPaid - purchaseNum).toLocaleString()}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Receipts Table */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Payment Records</h2>
          {receipts.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="table table-zebra w-full">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>Method</th>
                    <th>Amount</th>
                    <th>Date</th>
                    <th>Reference</th>
                    <th>Narration</th>
                  </tr>
                </thead>
                <tbody>
                  {receipts.map((r, i) => (
                    <tr key={r.id ?? i}>
                      <td>{i + 1}</td>
                      <td>{r.Pay_mode ?? "N/A"}</td>
                      <td>
                        {r.Amount_LCY
                          ? `Ksh. ${parseFloat(r.Amount_LCY).toLocaleString()}`
                          : "N/A"}
                      </td>
                      <td>{formatDate(r.Payment_date || r.PAYMENT_DATE1)}</td>
                      <td>{r.Receipt_No ?? "N/A"}</td>
                      <td>{r.Narration ?? "N/A"}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12 text-gray-500">
              No payment records to display.
            </div>
          )}
        </div>

        {/* Sales Agreements */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Sales Agreements</h2>
          <div className="flex items-center space-x-4">
            <input
              type="file"
              ref={fileInputRef}
              onChange={(e) => setFile(e.target.files?.[0] || null)}
            />
            <button
              onClick={handleUpload}
              className="btn btn-sm btn-success flex items-center"
            >
              <FilePlus className="mr-2" /> Upload
            </button>
          </div>
          <ul className="mt-4 space-y-2">
            {agreements.map((a) => (
              <li key={a.id} className="flex justify-between items-center">
                <span>
                  {a.original_name}{" "}
                  <small className="text-gray-500">
                    ({new Date(a.uploaded_at).toLocaleString()})
                  </small>
                </span>
                <div className="flex flex-col sm:flex-row sm:space-x-2 space-y-2 sm:space-y-0 flex-wrap w-full">
                  <a
                    href={`https://workspace.optiven.co.ke/api/sla/plots/${plotId}/agreements/${a.id}`}
                    download
                    className="btn btn-xs btn-primary flex items-center justify-center"
                  >
                    <Download className="mr-1" /> Download
                  </a>
                  <button
                    onClick={() => confirmDelete(a.id, a.original_name)}
                    className="btn btn-xs btn-error flex items-center justify-center"
                  >
                    <Trash2 className="mr-1" /> Delete
                  </button>
                </div>
              </li>
            ))}
            {agreements.length === 0 && (
              <li className="text-gray-500">No agreements uploaded.</li>
            )}
          </ul>
        </div>

        {/* Discharge Button */}
        <div className="flex justify-center">
          <button
            onClick={handleDischarge}
            disabled={isUnder || !hasAgreement}
            className="btn btn-primary btn-lg"
          >
            Submit to Discharge
          </button>
        </div>
      </div>

      {/* Confirmation Modal */}
      {isDeleteModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg p-6 w-96">
            <h3 className="text-lg font-semibold mb-4">Confirm Deletion</h3>
            <p className="mb-6">
              Are you sure you want to delete{" "}
              <strong>{deleteTarget.name}</strong>?
            </p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => setIsDeleteModalOpen(false)}
                className="btn btn-outline"
              >
                Cancel
              </button>
              <button onClick={handleDelete} className="btn btn-error">
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      <ToastContainer
        position="top-right"
        autoClose={4000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnHover
        draggable
      />
    </Sidebar>
  );
}
