const express = require("express");
const authenticateJWT = require("../../../middleware/authenticateJWT");
const router = express.Router();

module.exports = (pool, io) => {
  // Function to fetch book details based on the selected book_name
  async function fetchBookDetails(book_name) {
    return new Promise((resolve, reject) => {
      pool.getConnection((err, connection) => {
        if (err) {
          reject(err);
          return;
        }

        connection.query(
          "SELECT `book_code`, `book_price` FROM `book_uploads` WHERE `book_name` = ?",
          [book_name],
          (err, results) => {
            connection.release(); // Release the connection after querying

            if (err) {
              reject(err);
            } else {
              if (results.length > 0) {
                resolve(results[0]);
              } else {
                resolve(null);
              }
            }
          }
        );
      });
    });
  }
  // Route for the Add Book Issue data modal
  router.post("/", async (req, res) => {
    const {
      book_name,
      book_copies,
      book_office_issued,
      book_person_responsible,
      book_event,
    } = req.body;

    try {
      // Fetch book_code based on the selected book_name
      const bookDetails = await fetchBookDetails(book_name);

      if (!bookDetails) {
        return res.status(404).json({
          message: "Book not found.",
        });
      }

      const { book_code, book_price } = bookDetails;

      // Insert into the book_issuance table
      pool.query(
        "INSERT INTO `book_issuance`(`book_name`, `book_code`, `book_price`, `book_copies`, `book_office_issued`, `book_person_responsible`, `book_event`) VALUES (?, ?, ?, ?, ?, ?, ?)",
        [
          book_name,
          book_code,
          book_price,
          book_copies,
          book_office_issued,
          book_person_responsible,
          book_event,
        ],
        (err, result) => {
          if (err) {
            console.error("Database Error:", err);
            return res.status(500).json({
              message: "An error occurred while adding the Book Issue.",
            });
          }
          res.status(201).json({ message: "Book Issue added successfully!" });
        }
      );
    } catch (error) {
      console.error("Error:", error);
      res.status(500).json({
        message: "An error occurred while adding the Book Issue.",
      });
    }
  });

  //   Route to get Book Issue Data
  router.get("/", async (req, res) => {
    try {
      pool.query("SELECT * FROM book_issuance", (err, results) => {
        if (err) throw err;

        res.json(results);
      });
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching the Book Issue",
      });
    }
  });
  router.get("/users", async (req, res) => {
    try {
      pool.query("SELECT * FROM defaultdb.users;", (err, results) => {
        if (err) throw err;

        res.json(results);
      });
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching the Book Issue",
      });
    }
  });
  return router;
};
