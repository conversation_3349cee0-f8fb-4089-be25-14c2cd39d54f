import React, { useEffect, useState } from "react";
import axios from "axios";
import Sidebar from "../../components/Sidebar";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useSelector } from "react-redux";

const TeamReports = () => {
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [selectedTeam, setSelectedTeam] = useState("all");
  const [salesManagerDetails, setSalesManagerDetails] = useState(null);
  const [regionTeams, setRegionTeams] = useState([]);

  const token = useSelector((state) => state.user.token);
  const userRole = useSelector((state) => state.user.user.Accessrole);
  const userId = useSelector((state) => state.user.user.user_id);

  useEffect(() => {
    // Fetch the RM's details
    const fetchSalesManagerDetails = async () => {
      try {
        const response = await axios.get(
          `https://workspace.optiven.co.ke/api/users/${userId}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        setSalesManagerDetails(response.data);
      } catch (error) {
        console.error("Error fetching user's details:", error);
      }
    };

    // Fetch teams in the RM's region
    const fetchTeams = async () => {
      try {
        const response = await axios.get(
          `https://workspace.optiven.co.ke/api/teams`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        setRegionTeams(
          response.data.filter(
            (team) => team.id === (salesManagerDetails?.team || null)
          )
        );
      } catch (error) {
        console.error("Error fetching user's teams:", error);
      }
    };

    // Fetch the user's details and teams when the component mounts
    fetchSalesManagerDetails();
    fetchTeams();
  }, [salesManagerDetails?.team, token, userId]);

  const handleDownload = async () => {
    // Form validation
    if (!startDate || !endDate) {
      toast.error("Both dates must be chosen before submitting the form.", {
        position: "top-center",
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      return;
    }
    if (endDate < startDate) {
      toast.error("End date cannot be before the start date.", {
        position: "top-center",
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      return;
    }

    try {
      let url = "https://workspace.optiven.co.ke/api/workplan-reports/regional";

      // Check if the user is a regional manager and has selected a team
      if (
        userRole.split("#").includes("regionalManager") &&
        selectedTeam !== "all"
      ) {
        // Ensure that the selected team belongs to the user
        const teamIds = regionTeams.map((team) => team.id);
        if (!teamIds.includes(selectedTeam)) {
          toast.error(
            "You are not authorized to download reports for this team.",
            {
              position: "top-center",
              closeOnClick: true,
              pauseOnHover: true,
              draggable: true,
              progress: undefined,
            }
          );
          return;
        }

        url += `/${selectedTeam}`;
      }

      const response = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        params: {
          start_date: startDate,
          end_date: endDate,
          user_id: userId,
        },
        responseType: "blob",
      });

      // Create a blob from the PDF stream
      const file = new Blob([response.data], {
        type: "application/pdf",
      });

      // Create a link and click it to trigger the download
      const fileURL = URL.createObjectURL(file);
      const link = document.createElement("a");
      link.href = fileURL;
      link.download = "regional_reports.pdf";
      link.click();

      toast.success("PDF downloaded successfully.", {
        position: "top-center",
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
    } catch (error) {
      console.error("Error downloading PDF:", error);
      toast.error(
        "An error occurred while downloading the PDF. Please try again.",
        {
          position: "top-center",
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        }
      );
    }
  };

  const renderTeamDropdown = () => {
    if (
      userRole.split("#").includes("regionalManager") ||
      userRole.split("#").includes("workplanAdmin")
    ) {
      // Render team dropdown for regional managers
      return (
        <div>
          <label className="label">
            <span className="label-text font-bold">Select Team</span>
          </label>
          <select
            value={selectedTeam}
            onChange={(e) => setSelectedTeam(e.target.value)}
            className="select select-bordered w-full max-w-xs mb-4"
          >
            <option value="all">All</option>
            {regionTeams.map((team) => (
              <option key={team.id} value={team.id}>
                {team.name}
              </option>
            ))}
          </select>
        </div>
      );
    }
    return null; // Don't render the dropdown for other roles
  };

  return (
    <Sidebar>
      <div className="hero min-h-screen">
        <div className="form-control w-full max-w-xs">
          <div className="flex flex-col justify-center">
            <h1 className="font-bold text-lg">TEAM REPORTS</h1>
            {renderTeamDropdown()}
            <label className="label">
              <span className="label-text font-bold">Start Date</span>
            </label>
            <input
              type="date"
              value={startDate}
              className="input input-bordered w-full max-w-xs mb-4"
              onChange={(e) => setStartDate(e.target.value)}
            />
            <label className="label">
              <span className="label-text font-bold">End Date</span>
            </label>
            <input
              type="date"
              className="input input-bordered w-full max-w-xs mb-4"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
            />
            <button className="btn btn-outline" onClick={handleDownload}>
              Download PDF
            </button>
          </div>
        </div>
      </div>
    </Sidebar>
  );
};

export default TeamReports;
