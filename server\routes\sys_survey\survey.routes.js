const express = require("express");
const router = express.Router();

module.exports = (pool) => {
  router.get("/", async (req, res) => {
    try {
      pool.query("SELECT * FROM users_feedback ORDER BY id DESC", (err, results) => {
        if (err) {
          console.error("Error fetching feedback:", err);
          return res.status(500).json({
            message: "An error occurred while fetching feedback.",
          });
        }
        res.json(results);
      });
    } catch (error) {
      console.error("Unexpected error:", error);
      res.status(500).json({
        message: "An unexpected error occurred.",
      });
    }
  });

  router.post("/", async (req, res) => {
    const {
      program,
      name,
      understanding,
      knowHowToUse,
      usefulness,
      experience,
      userFriendliness,
      easeOfUse,
      frustrations,
      expectations,
      helpfulness,
      importantElements,
      desiredChanges,
      additionalFeatures,
      difficulties,
      usageFrequency,
      mostUsedFeatures,
      navigationEase,
      findability,
      taskChallenges,
      learningSpeed,
      informationClarity,
      screenGoal,
      interfaceRating,
      menuClarity,
      elementPosition,
      designSimplification,
      removals,
      layoutOrganization,
      screenAdditions,
      navigationRating,
      pageLikeability,
      aestheticsRating,
      recommendationLikelihood,
    } = req.body;

    try {
      pool.query(
        "INSERT INTO users_feedback (program, name, understanding, knowHowToUse, usefulness, experience, userFriendliness, easeOfUse, frustrations, expectations, helpfulness, importantElements, desiredChanges, additionalFeatures, difficulties, usageFrequency, mostUsedFeatures, navigationEase, findability, taskChallenges, learningSpeed, informationClarity, screenGoal, interfaceRating, menuClarity, elementPosition, designSimplification, removals, layoutOrganization, screenAdditions, navigationRating, pageLikeability, aestheticsRating, recommendationLikelihood) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        [
          program,
          name,
          understanding,
          knowHowToUse,
          usefulness,
          experience,
          userFriendliness,
          easeOfUse,
          frustrations,
          expectations,
          helpfulness,
          importantElements,
          desiredChanges,
          additionalFeatures,
          difficulties, 
          usageFrequency,
          mostUsedFeatures,
          navigationEase,
          findability,
          taskChallenges,
          learningSpeed,
          informationClarity,
          screenGoal,
          interfaceRating,
          menuClarity,
          elementPosition,
          designSimplification,
          removals,
          layoutOrganization,
          screenAdditions,
          navigationRating,
          pageLikeability,
          aestheticsRating,
          recommendationLikelihood,
        ],
        (err, result) => {
          if (err) {
            console.error("Error inserting feedback:", err);
            return res.status(500).json({
              message: "An error occurred while creating feedback.",
            });
          }

          res.status(201).json({ message: "Feedback created successfully" });
        }
      );
    } catch (error) {
      console.error("Unexpected error:", error);
      res.status(500).json({
        message: "An unexpected error occurred.",
      });
    }
  });

  return router;
};
