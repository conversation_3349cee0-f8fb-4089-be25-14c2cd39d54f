const express = require("express");
const util = require("util");
const router = express.Router();
const pdfMakePrinter = require("pdfmake/src/printer");
const authenticateJWT = require("../../middleware/authenticateJWT");
function formatDate(inputDate) {
  const date = new Date(inputDate);

  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0"); 
  const day = date.getDate().toString().padStart(2, "0");

  return `${year}-${month}-${day}`;
}
const fonts = {
  Roboto: {
    normal: "node_modules/roboto-font/fonts/Roboto/roboto-regular-webfont.ttf",
    bold: "node_modules/roboto-font/fonts/Roboto/roboto-bold-webfont.ttf",
    italic: "node_modules/roboto-font/fonts/Roboto/roboto-italic-webfont.ttf",
    bolditalics:
      "node_modules/roboto-font/fonts/Roboto/roboto-bolditalic-webfont.ttf",
  },
};
const printer = new pdfMakePrinter(fonts);
function dataToPdfRows(data) {
  const pdfRows = [];

  // Iterate over the data and map it to rows
  let totalPayments = 0; // Initialize total budget

  data.forEach((item) => {
    const supplier_payments = parseFloat(item.supplier_payment_amount); // Convert to float if necessary
    totalPayments += supplier_payments; // Add task actual budget to total

    pdfRows.push([
      { text: item.project_name, style: "tableCell" },
      { text: item.supplier_name, style: "tableCell" },
      { text: item.supplier_phone.toString(), style: "tableCell" },
      { text: item.supplier_supplies, style: "tableCell" },
      { text: item.supplier_payment_type, style: "tableCell" },
      {
        text: formatDate(item.supplier_payment_start_date.toString()),
        style: "tableCell",
      },
      {
        text: formatDate(item.supplier_payment_end_date.toString()),
        style: "tableCell",
      },
      
      { text: item.supplier_payment_amount, style: "tableCell" },
    ]);
  });

  pdfRows.push([
    // Add a row for the total budget at the end
    { text: "Total Payments", style: "tableCell", colSpan: 6 },
    {},
    {},
    {},
    {},
    {},
    {}, // Empty cells for formatting
    { text: totalPayments.toString(), style: "tableCell" }, // Display total budget
  ]);

  return pdfRows;
}

module.exports = (pool, Project) => {
  // get supplier payments for maintenance
  router.get("/maintenance-supply-range",authenticateJWT, (req, res) => {
    let vaQuery = `SELECT 
      s.name AS supplier_name,
      s.phoneNumber AS supplier_phone,
      s.id_number AS supplier_id_number,
      s.supplies AS supplier_supplies,
      sp.start_date AS supplier_payment_start_date,
      sp.end_date  AS supplier_payment_end_date,
      sp.amount AS supplier_payment_amount,
      sp.lpo AS supplier_payment_lpo,
      sp.quantity AS supplier_payment_quantity,
      sp.invoice AS supplier_payment_invoice,
      p.project_name AS project_name 
      FROM supplier_payments sp 
      LEFT JOIN supplier s ON sp.supplier_id = s.id
      LEFT JOIN projects p ON s.projectId = p.id`;

    const { start_date, end_date } = req.query;

    if (start_date && end_date) {
      // Filter by start_date and end_date if both are provided
      vaQuery += ` WHERE sp.start_date BETWEEN '${start_date}' AND '${end_date}' AND sp.type = 'maintenance'`;
    }
    

    try {
      // Execute the SQL query
      pool.query(vaQuery, (err, result) => {
        if (err) {
          console.error(err);
          res
            .status(500)
            .json({ error: "An error occurred while fetching data." });
        } else {
          // Return the result if successful
          res.json(result);
        }
      });
    } catch (error) {
      console.error(error);
      res
        .status(500)
        .json({ error: "An error occurred while processing the request." });
    }
});


  // Get supplier payments
  router.get("/supplier-payments",authenticateJWT,  (req, res) => {
    const currentDate = new Date();
    const thirtyDaysInMilliseconds = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds

    pool.query(
      `SELECT sp.*, s.name, s.phoneNumber, s.email, s.id_number, s.projectId, s.supplies, 
        DATEDIFF(CURRENT_DATE(), sp.end_date) AS days_since_due 
        FROM supplier_payments sp 
        JOIN supplier s ON sp.supplier_id = s.id 
        WHERE sp.status ='pending' AND type='valueAddition'`,
      (err, result) => {
        if (err) {
          console.error(err);
          return res.status(500).json({ message: "Internal Server Error" });
        }
        // Update status to 'overdue' for payments that are overdue
        result.forEach((payment) => {
          if (payment.days_since_due > 30) {
            payment.status = "overdue";
          }
        });
        res.status(200).json(result);
      }
    );
  });
  // Get maintenance supplier payments
  router.get("/maintenance-supplier-payments",authenticateJWT,(req, res) => {
    const currentDate = new Date();
    const thirtyDaysInMilliseconds = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds

    pool.query(
      `SELECT sp.*, s.name, s.phoneNumber, s.email, s.id_number, s.projectId, s.supplies, 
        DATEDIFF(CURRENT_DATE(), sp.end_date) AS days_since_due 
        FROM supplier_payments sp 
        JOIN supplier s ON sp.supplier_id = s.id 
        WHERE  type='maintenance'`,
      (err, result) => {
        if (err) {
          console.error(err);
          return res.status(500).json({ message: "Internal Server Error" });
        }
        // Update status to 'overdue' for payments that are overdue
        result.forEach((payment) => {
          if (payment.days_since_due > 30) {
            payment.status = "overdue";
          }
        });
        res.status(200).json(result);
      }
    );
  });
  // maintenance payments
  router.get("/supplier-payments",authenticateJWT,(req, res) => {
    const currentDate = new Date();
    const thirtyDaysInMilliseconds = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds

    pool.query(
      `SELECT sp.*, s.name, s.phoneNumber, s.email, s.id_number, s.projectId, s.supplies, 
      DATEDIFF(CURRENT_DATE(), sp.end_date) AS days_since_due 
      FROM supplier_payments sp 
      JOIN supplier s ON sp.supplier_id = s.id 
      WHERE sp.status ='pending' AND type='maintenance'`,
      (err, result) => {
        if (err) {
          console.error(err);
          return res.status(500).json({ message: "Internal Server Error" });
        }
        // Update status to 'overdue' for payments that are overdue
        result.forEach((payment) => {
          if (payment.days_since_due > 30) {
            payment.status = "overdue";
          }
        });
        res.status(200).json(result);
      }
    );
  });

  // get supplier Payments
  router.get("/view-supplier-payments",authenticateJWT,(req, res) => {
    pool.query(
      "SELECT sp.*, s.name, s.phoneNumber, s.email, s.id_number, s.projectId, s.supplies FROM supplier_payments sp JOIN supplier s ON sp.supplier_id = s.id WHERE sp.status ='PAID'",
      (err, result) => {
        if (err) {
          console.error(err);
          return res.status(500).json({ message: "Internal Server Error" });
        }
        res.status(200).json(result);
      }
    );
  });

  // get reports for payments
  router.get("/report",authenticateJWT,(req, res) => {
    let vaQuery = `SELECT 
      s.name AS supplier_name,
      s.phoneNumber AS supplier_phone,
      s.supplies AS supplier_supplies,
      sp.start_date AS supplier_payment_start_date,
      sp.end_date  AS supplier_payment_end_date,
      sp.amount AS supplier_payment_amount,
      sp.lpo AS supplier_payment_lpo,
      sp.type AS supplier_payment_type,
      sp.quantity AS supplier_payment_quantity,
      sp.invoice AS supplier_payment_invoice,
      p.project_name AS project_name 
      FROM supplier_payments sp 
      LEFT JOIN supplier s ON sp.supplier_id = s.id
      LEFT JOIN projects p ON s.projectId = p.id`;

    const { start_date, end_date } = req.query;

    if ((start_date, end_date)) {
      // Filter by start_date,end_date if provided
      vaQuery += ` WHERE sp.start_date BETWEEN '${start_date}' AND '${end_date}'`;
    }

    try {
      // Execute the SQL query
      pool.query(vaQuery, (err, result) => {
        if (err) {
          console.error(err);
          res
            .status(500)
            .json({ error: "An error occurred while fetching data." });
        } else {
          // Define the document definition for the PDF
          const docDefinition = {
            pageSize: "A4",
            pageOrientation: "landscape",
            content: [
              {
                table: {
                  headerRows: 1,
                  widths: ["*", "*", "*", "*", "*", "*", "*","*"], // Adjust column widths as needed
                  body: [
                    [
                      { text: "Project Name", style: "tableHeader" },
                      { text: "Supplier Name", style: "tableHeader" },
                      { text: "Supplier Contact", style: "tableHeader" },
                      { text: "Supplies", style: "tableHeader" },
                      { text: "Category", style: "tableHeader" },
                      {
                        text: "Supply start Date",
                        style: "tableHeader",
                      },
                      { text: "Supply End Date", style: "tableHeader" },
                    
                      { text: "Total paid", style: "tableHeader" },
                    ],
                    ...dataToPdfRows(result), // Add data rows
                  ],
                },
              },
            ],
            styles: {
              tableHeader: {
                bold: true,
                fontSize: 12,
                fillColor: "#CCCCCC", // Header background color
              },
              tableCell: {
                fontSize: 10,
              },
            },
          };

          // Create the PDF document using pdfmake
          const pdfDoc = printer.createPdfKitDocument(docDefinition);

          // Set the response headers to indicate a PDF file
          res.setHeader("Content-Type", "application/pdf");

          // Stream the PDF document as the response
          pdfDoc.pipe(res);
          pdfDoc.end();
        }
      });
    } catch (error) {
      console.error(error);
      res
        .status(500)
        .json({ error: "An error occurred while processing the request." });
    }
  });

  // Get a specific supplier by ID
  router.get("/:id",authenticateJWT,  async (req, res) => {
    try {
      const [supplier] = await pool
        .promise()
        .query("SELECT * FROM supplier WHERE id = ?", [req.params.id]);
      if (supplier.length === 0) {
        return res.status(404).json({ message: "Supplier not found" });
      }
      res.json(supplier[0]);
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });

  // get all suppliers
  router.get("/",authenticateJWT,  (req, res) => {
    pool.query("SELECT * FROM supplier ORDER BY name  ", (err, results) => {
      if (err) {
        return res.status(500).json({
          message: "An error occurred while fetching supplier.",
          error: err,
        });
      }
      res.json(results);
    });
  });

  

  // Create a new supplier and associate it with a project
  router.post("/", authenticateJWT,async (req, res) => {
    const {
      name,
      location,
      phoneNumber,
      email,
      id_number,
      supplies,
      projectId,
    } = req.body;
    try {
      const result = await pool
        .promise()
        .query(
          "INSERT INTO supplier (name, location, phoneNumber, email, id_number, supplies, projectId) VALUES (?, ?, ?, ?, ?, ?, ?)",
          [name, location, phoneNumber, email, id_number, supplies, projectId]
        );
      const newSupplier = {
        id: result[0].insertId,
        name,
        location,
        phoneNumber,
        email,
        id_number,
        supplies,
        projectId,
      };
      const project = await Project.findById(projectId);
      if (project) {
        project.suppliers.push(result[0].insertId);
        await project.save();
      }
      res.status(201).json(newSupplier);
    } catch (error) {
      res.status(400).send(error);
    }
  });

  // Supplier payment post
  router.post("/payments", authenticateJWT,async (req, res) => {
    const {
      supplier_id,
      start_date,
      end_date,
      invoice,
      lpo,
      amount,
      status,
      quantity,
      type,
    } = req.body; // Added "type" here
    try {
      const result = await pool
        .promise()
        .query(
          "INSERT INTO supplier_payments (supplier_id, start_date, end_date, invoice, lpo, amount, status, quantity, type) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
          [
            supplier_id,
            start_date,
            end_date,
            invoice,
            lpo,
            amount,
            status,
            quantity,
            type,
          ]
        );
      const supplierPayment = {
        id: result[0].insertId,
        supplier_id,
        start_date,
        end_date,
        invoice,
        lpo,
        amount,
        status,
        quantity,
        type,
      };
      res.status(201).json(supplierPayment);
    } catch (error) {
      res.status(400).send(error);
    }
  });

  // Patch request for payments.
  router.patch("/:id/payments",  authenticateJWT,async (req, res) => {
    const {
      start_date,
      end_date,
      amount,
      status,
      invoice,
      lpo,
      quantity,
      type,
    } = req.body;
    try {
      const result = await pool
        .promise()
        .query(
          "UPDATE supplier SET start_date=?, end_date=?, amount=?, status=? ,invoice=?lpo=?,quantity=?,type=? WHERE id=?",
          [
            start_date,
            end_date,
            amount,
            status,
            invoice,
            lpo,
            quantity,
            type,
            req.params.id,
          ]
        );
      if (result[0].affectedRows === 0) {
        return res.status(404).json({ message: "Supplier not found" });
      }
      res.json({ message: "Supplier updated successfully" });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });
  // update status
  router.patch("/:id/updateStatus",  authenticateJWT,async (req, res) => {
    const { status } = req.body;
    try {
      const result = await pool
        .promise()
        .query("UPDATE supplier_payments SET status =? WHERE id=?", [
          status,
          req.params.id,
        ]);
      if (result[0].affectedRows === 0) {
        return res
          .status(404)
          .json({ message: "supplier payments status not found" });
      }
      res.json({ message: "supplier payments status updated successfully" });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });

  // PATCH (update) a supplier
  router.patch("/:id",  authenticateJWT,async (req, res) => {
    const { name, location, phoneNumber, email, id_number, supplies } =
      req.body;
    try {
      const result = await pool
        .promise()
        .query(
          "UPDATE supplier SET name=?, location=?, phoneNumber=?, email=?, id_number=?, supplies=? WHERE id=?",
          [
            name,
            location,
            phoneNumber,
            email,
            id_number,
            supplies,
            req.params.id,
          ]
        );
      if (result[0].affectedRows === 0) {
        return res.status(404).json({ message: "Supplier not found" });
      }
      res.json({ message: "Supplier updated successfully" });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });
  // DELETE Supplier Payment
  router.delete("/supplier-payments/:id",  authenticateJWT,async (req, res) => {
    try {
      // Check if the Supplier exists
      const [checkSupplier] = await pool
        .promise()
        .query("SELECT id FROM supplier_payments WHERE id=?", [req.params.id]);
      if (checkSupplier.length === 0) {
        return res.status(404).json({ message: "Supplier_payments not found" });
      }
      // Now, delete the supplier
      const result = await pool
        .promise()
        .query("DELETE FROM supplier_payments WHERE id=?", [req.params.id]);
      if (result[0].affectedRows === 0) {
        return res.status(404).json({ message: "Supplier_payment not found" });
      }
      res.json({ message: "Supplier_payment deleted successfully" });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });

  // DELETE a supplier
  router.delete("/:id",  authenticateJWT,async (req, res) => {
    try {
      // Check if the Supplier exists
      const [checkSupplier] = await pool
        .promise()
        .query("SELECT id FROM supplier WHERE id=?", [req.params.id]);
      if (checkSupplier.length === 0) {
        return res.status(404).json({ message: "Supplier not found" });
      }
      // Now, delete the supplier
      const result = await pool
        .promise()
        .query("DELETE FROM supplier WHERE id=?", [req.params.id]);
      if (result[0].affectedRows === 0) {
        return res.status(404).json({ message: "Supplier not found" });
      }
      res.json({ message: "Supplier deleted successfully" });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });

  return router;
};
