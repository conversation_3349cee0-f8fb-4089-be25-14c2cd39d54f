const express = require('express');
const router = express.Router();

module.exports = (pool) => {
  // GET all rooms
  router.get('/', (req, res) => {
    pool.query("SELECT * FROM gmc_conference.rooms", (err, results) => {
      if (err) {
        return res.status(500).send({ message: "Error retrieving rooms: " + err });
      }
      res.send(results);
    });
  });

  // GET a single room by ID
  router.get('/:id', (req, res) => {
    const { id } = req.params;
    pool.query("SELECT * FROM gmc_conference.rooms WHERE id = ?", [id], (err, result) => {
      if (err) {
        return res.status(500).send({ message: "Error retrieving room: " + err });
      }
      if (result.length > 0) {
        res.send(result[0]);
      } else {
        res.status(404).send({ message: "Room not found" });
      }
    });
  });

  // POST a new room
  router.post('/', (req, res) => {
    const { name, description, capacity, backgroundColor } = req.body;
    if (!name || !capacity) {
      return res.status(400).send({ message: "Name and capacity are required" });
    }

    pool.query("INSERT INTO gmc_conference.rooms (name, description, capacity, backgroundColor) VALUES (?, ?, ?, ?)",
      [name, description, capacity, backgroundColor], (err, result) => {
        if (err) {
          return res.status(500).send({ message: "Error adding room: " + err });
        }
        res.status(201).send({ message: "Room added successfully", roomId: result.insertId });
      });
  });

  // PUT to update a room by ID
  router.put('/:id', (req, res) => {
    const { id } = req.params;
    const { name, description, capacity, backgroundColor } = req.body;
    pool.query("UPDATE gmc_conference.rooms SET name = ?, description = ?, capacity = ?, backgroundColor = ? WHERE id = ?",
      [name, description, capacity, backgroundColor, id], (err, result) => {
        if (err) {
          return res.status(500).send({ message: "Error updating room: " + err });
        }
        if (result.affectedRows == 0) {
          return res.status(404).send({ message: "Room not found" });
        }
        res.send({ message: "Room updated successfully" });
      });
  });

  // DELETE a room by ID
  router.delete('/:id', (req, res) => {
    const { id } = req.params;
    pool.query("DELETE FROM gmc_conference.rooms WHERE id = ?", [id], (err, result) => {
      if (err) {
        return res.status(500).send({ message: "Error deleting room: " + err });
      }
      if (result.affectedRows == 0) {
        return res.status(404).send({ message: "Room not found" });
      }
      res.send({ message: "Room deleted successfully" });
    });
  });

  return router;
};
