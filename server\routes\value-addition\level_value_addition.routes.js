const express = require('express')
const router = express.Router()
const authenticateJWT = require("../../middleware/authenticateJWT");

module.exports = (pool) => {
router.get('/',authenticateJWT,(req,res) =>{
    try{
        pool.query("SELECT * FROM level_value_addition", (err,result) => { 
            if(err) throw err
            res.status(200).json(result)
        })

    }catch(error){
        console.error(error)
        res.status(500).json({message:"Error in fetching data"})
    }
})

    return router

}
