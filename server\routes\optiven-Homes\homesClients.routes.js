const express = require("express");
const { underline } = require("pdfkit");
const router = express.Router();
const pdfMakePrinter = require("pdfmake/src/printer");
const nodemailer = require("nodemailer");
require("dotenv").config();
const fs = require("fs");
const path = require("path");

// Function to convert image to base64
const imageToBase64 = (filePath) => {
  const bitmap = fs.readFileSync(filePath);
  return new Buffer.from(bitmap).toString("base64");
};

// Nodemailer helper function to send email
async function sendEmail(userEmail, subject, text) {
  // create reusable transporter object using the default SMTP transport
  let transporter = nodemailer.createTransport({
    host: "smtp.zoho.com",
    port: 465,
    secure: true,
    auth: {
      user: process.env.HOMES_EMAIL,
      pass: process.env.HOMES_PASSWORD,
    },
  });

  const additionalText = `
  
  This is a system-generated email. Please do not reply.
  
  Incase of any comments or queries <NAME_EMAIL>
  
  Kind Regards`;

  // send mail with defined transport object
  let info = await transporter.sendMail({
    from: '"Optiven Construction 👷🏾‍♀️👷🏾‍♂️🚧" <<EMAIL>>', // sender address
    to: userEmail, // list of receivers
    subject: subject, // Subject line
    text: text + additionalText, // plain text body
  });
}

const fonts = {
  Roboto: {
    normal: "node_modules/roboto-font/fonts/Roboto/roboto-regular-webfont.ttf",
    bold: "node_modules/roboto-font/fonts/Roboto/roboto-bold-webfont.ttf",
    italic: "node_modules/roboto-font/fonts/Roboto/roboto-italic-webfont.ttf",
    bolditalics:
      "node_modules/roboto-font/fonts/Roboto/roboto-bolditalic-webfont.ttf",
  },
};

module.exports = (pool) => {
  // get single client
  router.get("/:id", (req, res) => {
    const { id } = req.params;
    try {
      pool.query(
        "SELECT * FROM homes_clients WHERE id=?",
        [id],
        (error, result) => {
          if (error) throw error;
          res.status(200).json(result);
        }
      );
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Error fetching client" });
    }
  });
  // get all clients
  router.get("/", (req, res) => {
    try {
      pool.query(
        "SELECT * FROM homes_clients ORDER BY date DESC",
        (error, result) => {
          if (error) throw error;
          res.status(200).json(result);
        }
      );
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Error fetching clients" });
    }
  });
  // Get client email by client id
  router.get("/:id/email", (req, res) => {
    const { id } = req.params;
    try {
      pool.query(
        "SELECT email FROM homes_clients WHERE id = ?",
        [id],
        (error, result) => {
          if (error) throw error;
          if (result.length > 0) {
            res.status(200).json(result[0]);
          } else {
            res.status(404).json({ message: "Client not found" });
          }
        }
      );
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Error fetching client email" });
    }
  });
  // Send notification
  router.post("/notify", async (req, res) => {
    const { client_id, email, subject, body } = req.body;
    try {
      await sendEmail(email, subject, body);
      res.status(200).json({ message: "Notification sent successfully!" });
    } catch (error) {
      console.error("Error sending email:", error);
      res.status(500).json({ message: "Error sending notification" });
    }
  });

  // get current month clients
  router.get("/clients/current-month", (req, res) => {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = currentDate.getFullYear();

    pool.query(
      "SELECT * FROM homes_clients WHERE MONTH(date) = ? AND YEAR(date) = ?",
      [currentMonth, currentYear],
      (error, result) => {
        if (error) {
          console.error(error);
          return res
            .status(500)
            .json({ message: "Error fetching current month clients" });
        }
        res.status(200).json(result);
      }
    );
  });

  // get previous month clients
  router.get("/clients/previous-month", (req, res) => {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    const prevMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const prevYear = currentMonth === 0 ? currentYear - 1 : currentYear;

    pool.query(
      "SELECT * FROM homes_clients WHERE MONTH(date) = ? AND YEAR(date) = ?",
      [prevMonth + 1, prevYear],
      (error, result) => {
        if (error) {
          console.error(error);
          return res
            .status(500)
            .json({ message: "Error fetching previous month clients" });
        }
        res.status(200).json(result);
      }
    );
  });

  // Post clients
router.post("/", (req, res) => {
  try {
      const {
          client_name,
          id_no,
          LR_no,
          plot_size,
          type,
          cost,
          email,
          actual_cost,
      } = req.body;
      // Add 'status' column with default value 'design'
      const status = "design";
      pool.query(
          "INSERT INTO homes_clients(client_name, id_no, LR_no, plot_size, type, cost, email, status,actual_cost) VALUES(?,?,?,?,?,?,?,?,?)",
          [
              client_name,
              id_no,
              LR_no,
              plot_size,
              type,
              cost,
              email,
              status,
              actual_cost,
          ],
          (error, result) => {
              if (error) {
                  console.error("Error inserting client:", error);
                  return res.status(500).json({ message: "Error posting client" });
              }

              const clientId = result.insertId;
              const phases = [
                  { phase_id: 1, is_current_phase: true },
                  { phase_id: 2, is_current_phase: false },
                  { phase_id: 3, is_current_phase: false },
                  { phase_id: 4, is_current_phase: false },
                  { phase_id: 5, is_current_phase: false }
              ];

              const clientPhases = phases.map((phase) => [clientId, phase.phase_id, phase.is_current_phase]);

              pool.query(
                  "INSERT INTO client_phases (client_id, phase_id, is_current_phase) VALUES ?",
                  [clientPhases],
                  (error) => {
                      if (error) {
                          console.error("Error inserting client phases:", error);
                          return res.status(500).json({ message: "Error posting client phases" });
                      }
                      res.status(200).json({ message: "Client and phases inserted successfully" });
                  }
              );
          }
      );
  } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Internal server error" });
  }
});

  // Get phase details
  router.get("/phases", (req, res) => {
    try {
        pool.query(
            "SELECT p.id, p.label, p.percentage, t.id as task_id, t.name, t.completed FROM optiven-homes.phases p LEFT JOIN tasks t ON p.id = t.phase_id",
            (error, results) => {
                if (error) throw error;

                const phases = results.reduce((acc, row) => {
                    const { id, label, percentage, task_id, name, completed } = row;
                    if (!acc[id]) {
                        acc[id] = {
                            id,
                            label,
                            percentage,
                            tasks: []
                        };
                    }
                    acc[id].tasks.push({ id: task_id, name, completed });
                    return acc;
                }, {});

                res.status(200).json(Object.values(phases));
            }
        );
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: "Error fetching phase details" });
    }
});

// Update task completion status
router.patch("/tasks/:id", (req, res) => {
    const { id } = req.params;
    const { completed } = req.body;

    try {
        pool.query(
            "UPDATE tasks SET completed=? WHERE id=?",
            [completed, id],
            (error, result) => {
                if (error) throw error;
                res.status(200).json({ message: "Task status updated successfully" });
            }
        );
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: "Error updating task status" });
    }
});

 // Get phase details for a specific client
router.get("/clients/:clientId/phases", (req, res) => {
  const { clientId } = req.params;

  pool.query(
      `SELECT p.id, p.label, p.percentage, t.id as task_id, t.name, ct.completed, cp.is_current_phase
       FROM phases p 
       LEFT JOIN tasks t ON p.id = t.phase_id
       LEFT JOIN client_tasks ct ON t.id = ct.task_id AND ct.client_id = ?
       LEFT JOIN client_phases cp ON p.id = cp.phase_id AND cp.client_id = ?
       ORDER BY p.id`,
      [clientId, clientId],
      (error, results) => {
          if (error) {
              console.error("Error fetching phases:", error);
              return res.status(500).json({ message: "Error fetching phases" });
          }

          const formattedPhases = results.reduce((acc, row) => {
              const { id, label, percentage, task_id, name, completed, is_current_phase } = row;
              if (!acc[id]) {
                  acc[id] = {
                      id,
                      label,
                      percentage,
                      is_current_phase: !!is_current_phase,
                      tasks: []
                  };
              }
              if (task_id) {
                  acc[id].tasks.push({ id: task_id, name, completed });
              }
              return acc;
          }, {});

          res.status(200).json(Object.values(formattedPhases));
      }
  );
});

// Get task costs for a specific client
router.get("/clients/:clientId/tasks/costs", (req, res) => {
  const { clientId } = req.params;

  pool.query(
    `SELECT t.id as task_id, t.name, ct.cost, ct.completed, p.id as phase_id, p.label, p.percentage
     FROM client_tasks ct
     JOIN tasks t ON ct.task_id = t.id
     JOIN phases p ON t.phase_id = p.id
     WHERE ct.client_id = ?`,
    [clientId],
    (error, results) => {
      if (error) {
        console.error("Error fetching task costs:", error);
        return res.status(500).json({ message: "Error fetching task costs" });
      }

      const phases = results.reduce((acc, row) => {
        const { phase_id, label, percentage, task_id, name, cost, completed } = row;
        if (!acc[phase_id]) {
          acc[phase_id] = {
            id: phase_id,
            label,
            percentage,
            tasks: []
          };
        }
        acc[phase_id].tasks.push({ id: task_id, name, cost, completed });
        return acc;
      }, {});

      res.status(200).json(Object.values(phases));
    }
  );
});



// Update task completion status for a specific client
router.patch("/clients/:clientId/tasks/:taskId", (req, res) => {
  const { clientId, taskId } = req.params;
  const { completed } = req.body;

  try {
      pool.query(
          "INSERT INTO client_tasks (client_id, task_id, completed) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE completed=?",
          [clientId, taskId, completed, completed],
          (error, result) => {
              if (error) throw error;
              res.status(200).json({ message: "Task status updated successfully" });
          }
      );
  } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Error updating task status" });
  }
});

// Update task cost for a specific client
router.patch("/clients/:clientId/tasks/:taskId/cost", (req, res) => {
  const { clientId, taskId } = req.params;
  const { cost } = req.body;

  try {
      pool.query(
          "UPDATE client_tasks SET cost=? WHERE client_id=? AND task_id=?",
          [cost, clientId, taskId],
          (error, result) => {
              if (error) throw error;
              res.status(200).json({ message: "Task cost updated successfully" });
          }
      );
  } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Error updating task cost" });
  }
});

// Update current phase for a specific client
router.patch("/clients/:clientId/current-phase", (req, res) => {
  const { clientId } = req.params;
  const { phaseId } = req.body;

  pool.query(
      "UPDATE client_phases SET is_current_phase = CASE WHEN phase_id = ? THEN TRUE ELSE FALSE END WHERE client_id = ?",
      [phaseId, clientId],
      (error, result) => {
          if (error) {
              console.error("Error updating current phase:", error);
              return res.status(500).json({ message: "Error updating current phase" });
          }
          res.status(200).json({ message: "Current phase updated successfully" });
      }
  );
});


// Create new client phases and tasks when a client is added
router.post("/clients/:clientId/phases", async (req, res) => {
  const { clientId } = req.params;
  const { phases } = req.body;

  const clientPhases = phases.map((phase, index) => [clientId, phase.id, index === 0]);
  const clientTasks = phases.flatMap((phase) =>
      phase.tasks.map((task) => [clientId, task.id, false])
  );

  try {
      await pool.query("INSERT INTO client_phases (client_id, phase_id, is_current_phase) VALUES ?", [clientPhases]);
      await pool.query("INSERT INTO client_tasks (client_id, task_id, completed) VALUES ?", [clientTasks]);
      res.status(201).json({ message: "Client phases and tasks added successfully" });
  } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Error adding client phases and tasks" });
  }
});

  // Route to update actual cost
  router.patch("/:id/actual_cost", (req, res) => {
    try {
      const { id } = req.params;
      // Extract actual_cost from the request body
      const { actual_cost } = req.body;
      // Update actual cost
      pool.query(
        "UPDATE homes_clients SET actual_cost=? WHERE id=?",
        [actual_cost, id],
        (err, result) => {
          if (err) {
            console.error(err);
            res.status(500).json({ message: "Server Error" });
          } else if (result.affectedRows > 0) {
            res.json({
              message: "Actual Cost updated successfully",
            });
          } else {
            res.status(404).json({ message: "Actual Cost not Updated" });
          }
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while Updating Actual Cost.",
      });
    }
  });

  // Route to update construction cost and proposed dates
  router.patch("/:id/construction_details", (req, res) => {
    try {
      const { id } = req.params;
      // Extract const_cost, prop_start_date, and prop_end_date from the request body
      const { const_cost, prop_start_date, prop_end_date } = req.body;
      // Update construction cost and proposed dates
      pool.query(
        "UPDATE homes_clients SET const_cost=?, prop_start_date=?, prop_end_date=? WHERE id=?",
        [const_cost, prop_start_date, prop_end_date, id],
        (err, result) => {
          if (err) {
            console.error(err);
            res.status(500).json({ message: "Server Error" });
          } else if (result.affectedRows > 0) {
            res.json({
              message: "Construction details updated successfully",
            });
          } else {
            res
              .status(404)
              .json({ message: "Construction details not updated" });
          }
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while updating construction details.",
      });
    }
  });

  // Route to update status to 'construction'
  router.patch("/:id/change-status/construction", (req, res) => {
    try {
      const { id } = req.params;
      // Update status to 'construction'
      const newStatus = "construction";
      pool.query(
        "UPDATE homes_clients SET status=? WHERE id=?",
        [newStatus, id],
        (error, result) => {
          if (error) throw error;
          res.status(200).json(result);
        }
      );
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Route to update status to 'terminated'
  router.patch("/:id/change-status/terminated", (req, res) => {
    try {
      const { id } = req.params;
      // Update status to 'terminated'
      const newStatus = "terminated";
      pool.query(
        "UPDATE homes_clients SET status=? WHERE id=?",
        [newStatus, id],
        (error, result) => {
          if (error) throw error;
          res.status(200).json(result);
        }
      );
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Internal server error" });
    }
  });
  //   patch request
  router.patch("/:id", (req, res) => {
    try {
      const { id } = req.params;
      const { client_name, id_no, LR_no, plot_size, type, cost, email } =
        req.body;
      pool.query(
        "UPDATE homes_clients SET client_name=?,id_no=?,LR_no=?,plot_size=?,type=?,cost=?,email=? WHERE id=? ",
        [client_name, id_no, LR_no, plot_size, type, cost, email, id],
        (error, result) => {
          if (error) throw error;
          res.status(200).json(result);
        }
      );
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Internal server error" });
    }
  });
  // handle contract download
  router.get("/:id/contract", async (req, res) => {
    const { id } = req.params;

    try {
      // Fetch client details from the database
      pool.query(
        `SELECT client_name, id_no, LR_no, plot_size, cost, type FROM homes_clients WHERE id = ?`,
        [id],
        async (err, result) => {
          if (err) {
            console.error("Error fetching client details:", err);
            return res.status(500).json({
              message: "An error occurred while fetching the client's info.",
            });
          }

          if (result.length === 0) {
            return res.status(404).json({ message: "Client not found" });
          }

          const clientInfo = result[0];

          // Prepare contract content based on client type
          let articlesOfAgreement = [];
          if (clientInfo.type.toLowerCase() === "bungalow") {
            articlesOfAgreement = [
              {
                text: "2.0 ARTICLES OF AGREEMENT",
                bold: true,
                fontSize: 14,
                alignment: "center",
                decoration: "underline",
                margin: [0, 0, 0, 20],
              },
              {
                text: "IT IS HEREBY AGREED AS FOLLOWS;",
                fontSize: 14,
                margin: [0, 0, 0, 20],
                decoration: "underline",
                bold: true,
              },
              {
                text: "1. The Contractor undertakes that they have all the necessary skills, technical knowledge and resources to undertake the tasks listed below.",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                text: "2. The client is hereby willing to engage the Contractor to undertake the following services selected below. Tick the check box(s) below to indicate the service(s) you wish to procure below.",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                text: "Tick the check box(s) below to indicate the service(s) you wish to procure below.",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },

              {
                text: "[   ]Site Visit and Due Diligence (Mandatory) - Ksh. 50,000.00",
                fontSize: 14,
                margin: [0, 0, 0, 20],
                bold: true,
              },
              {
                text: "This involves:",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                ul: [
                  {
                    text: [
                      { text: "Due diligence", bold: true },
                      {
                        text: " will entail in-depth research and analysis of details regarding a property to ensure that all relevant factors have been put into consideration before development activities are undertaken. The following aspects will be involved:",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Status of a property", bold: true },
                      {
                        text: ": The process will involve confirmation of ownership status by ascertaining the land tenure through land search, title, survey and registry maps, and confirmation of beacons.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Site Analysis", bold: true },
                      {
                        text: ": This is a review of the land's physical attributes such as topography, neighborhood characteristics, existing developments and trends.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Review of Land Use Zoning", bold: true },
                      {
                        text: ": This review shall consider existing land use zoning laws and land use regulations for compliance with existing developments.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Infrastructure Assessment", bold: true },
                      {
                        text: ": This will consider existing infrastructure, such as roads, utilities, and other auxiliary services that aid service provision to the proposed site.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Environmental pre-assessment", bold: true },
                      {
                        text: ": This process will identify likely environmental issues, contamination, flooding risks, and whether any remediation/mitigation measures should be implemented.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                ],
              },

              {
                text: "[   ]Preparation of Architectural Design Drawings – Ksh. 100,000.00",
                fontSize: 14,
                margin: [0, 0, 0, 20],
                bold: true,
              },
              {
                text: "This involves:",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                ul: [
                  {
                    text: [
                      { text: "Site Investigations", bold: true },
                      { text: " – To determine site layout." },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Inception", bold: true },
                      {
                        text: " -Obtaining the initial statement of requirements from the client.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Advising the client", bold: true },
                      { text: " on the feasibility of the project." },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Scheme design", bold: true },
                      {
                        text: " - presentation of scheme designs; floor plans, sections, elevations for approval purposes.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Detailed Design", bold: true },
                      {
                        text: "- Small-scale construction drawings for site use.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                ],
              },
              {
                text: "[   ]Preparation of Structural Design Drawings – Ksh. 60,000.00",
                fontSize: 14,
                margin: [0, 0, 0, 20],
                bold: true,
              },
              {
                text: "This involves:",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                ul: [
                  {
                    text: [
                      { text: "Site Investigations", bold: true },
                      { text: " – To determine appropriate foundations." },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Inception", bold: true },
                      {
                        text: "-Obtaining the initial statement of requirements from the client.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Advising the client", bold: true },
                      { text: " on the possible project designs." },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Detailed Designs", bold: true },
                      { text: "-Construction drawings for site use." },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                ],
              },
              {
                text: "[   ]Preparation of Interior Design Concepts and Service Details -Ksh. 80,000.00",
                fontSize: 14,
                margin: [0, 0, 0, 20],
                bold: true,
              },
              {
                text: "This involves:",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                ul: [
                  {
                    text: [
                      { text: "Space Planning", bold: true },
                      {
                        text: " - Arranging furniture and optimizing traffic flow for better functionality.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Concept Development", bold: true },
                      {
                        text: " - Creating a design concept that matches client preferences, including color schemes and style.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Furniture and Fixture Selection", bold: true },
                      {
                        text: " - Helping choose furniture, lighting, and decorative pieces that fit the design and are practical.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Material Selection", bold: true },
                      {
                        text: " - Recommending durable and visually appealing materials for floors, walls, and countertops.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Color Consultation", bold: true },
                      {
                        text: " - Advising on color palettes to enhance the mood and lighting of a room.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Custom Design Elements", bold: true },
                      {
                        text: " - Designing unique built-in features like cabinets and shelves to fit specific needs.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Styling and Accessories", bold: true },
                      {
                        text: " - Selecting and arranging accessories and artwork to add personality to the space.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Consultation and Advice", bold: true },
                      {
                        text: " - Providing professional guidance on space use, colors, and furniture arrangement.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                ],
              },
              {
                text: "[   ]Preparation of Construction Cost Estimates – Ksh. 90,000.00",
                fontSize: 14,
                margin: [0, 0, 0, 20],
                bold: true,
              },
              {
                text: "This involves:",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                ul: [
                  {
                    text: [
                      { text: "Site Adaptation", bold: true },
                      {
                        text: " – To understand the location of the project for accuracy in cost estimation.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Client Brief", bold: true },
                      {
                        text: " – Understanding the client’s needs and budget for the project.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Preliminary Cost Estimates", bold: true },
                      {
                        text: " – Preparation of cost estimates based on early scheme designs and briefs.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Detailed Cost Estimates", bold: true },
                      {
                        text: " – Prepare detailed bills of quantities based on final architectural, structural and interior design drawings and concepts.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                ],
              },

              
            ];
          } else if (clientInfo.type.toLowerCase() === "custom") {
            articlesOfAgreement = [
              {
                text: "2.0 ARTICLES OF AGREEMENT",
                bold: true,
                fontSize: 14,
                alignment: "center",
                decoration: "underline",
                margin: [0, 0, 0, 20],
              },
              {
                text: "IT IS HEREBY AGREED AS FOLLOWS;",
                fontSize: 14,
                margin: [0, 0, 0, 20],
                decoration: "underline",
                bold: true,
              },
              {
                text: "1. The Contractor undertakes that they have all the necessary skills, technical knowledge and resources to undertake the tasks listed below.",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                text: "2. The client is hereby willing to engage the Contractor to undertake the following services selected below. Tick the check box(s) below to indicate the service(s) you wish to procure below.",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                text: "Tick the check box(s) below to indicate the service(s) you wish to procure below.",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                text: "[   ]Site Visit and Due Diligence (Mandatory) - Ksh. 50,000.00",
                fontSize: 14,
                margin: [0, 0, 0, 20],
                bold: true,
              },
              {
                text: "This involves:",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                ul: [
                  {
                    text: [
                      { text: "Due diligence", bold: true },
                      {
                        text: " will entail in-depth research and analysis of details regarding a property to ensure that all relevant factors have been put into consideration before development activities are undertaken. The following aspects will be involved:",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Status of a property", bold: true },
                      {
                        text: ": The process will involve confirmation of ownership status by ascertaining the land tenure through land search, title, survey and registry maps, and confirmation of beacons.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Site Analysis", bold: true },
                      {
                        text: ": This is a review of the land's physical attributes such as topography, neighborhood characteristics, existing developments and trends.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Review of Land Use Zoning", bold: true },
                      {
                        text: ": This review shall consider existing land use zoning laws and land use regulations for compliance with existing developments.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Infrastructure Assessment", bold: true },
                      {
                        text: ": This will consider existing infrastructure, such as roads, utilities, and other auxiliary services that aid service provision to the proposed site.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Environmental pre-assessment", bold: true },
                      {
                        text: ": This process will identify likely environmental issues, contamination, flooding risks, and whether any remediation/mitigation measures should be implemented.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                ],
              },
              {
                text: "[   ]Preparation of Architectural Design Drawings – Ksh. 300,000.00",
                fontSize: 14,
                margin: [0, 0, 0, 20],
                bold: true,
              },
              {
                text: "This involves:",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                ul: [
                  {
                    text: [
                      { text: "Site Investigations", bold: true },
                      { text: " – To determine site layout." },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Inception", bold: true },
                      {
                        text: " -Obtaining the initial statement of requirements from the client.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Advising the client", bold: true },
                      { text: " on the feasibility of the project." },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Scheme design", bold: true },
                      {
                        text: " - presentation of scheme designs; floor plans, sections, elevations for approval purposes.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Detailed Design", bold: true },
                      {
                        text: "- Small-scale construction drawings for site use.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                ],
              },
              {
                text: "[   ]Preparation of Structural Design Drawings – Ksh. 280,000.00",
                fontSize: 14,
                margin: [0, 0, 0, 20],
                bold: true,
              },
              {
                text: "This involves:",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                ul: [
                  {
                    text: [
                      { text: "Site Investigations", bold: true },
                      { text: " – To determine appropriate foundations." },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Inception", bold: true },
                      {
                        text: "-Obtaining the initial statement of requirements from the client.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Advising the client", bold: true },
                      { text: " on the possible project designs." },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Detailed Designs", bold: true },
                      { text: "-Construction drawings for site use." },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                ],
              },
              {
                text: "[   ]Preparation of Interior Design Concepts and Details -Ksh. 250,000.00",
                fontSize: 14,
                margin: [0, 0, 0, 20],
                bold: true,
              },
              {
                text: "This involves:",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                ul: [
                  {
                    text: [
                      { text: "Space Planning", bold: true },
                      {
                        text: " - Arranging furniture and optimizing traffic flow for better functionality.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Concept Development", bold: true },
                      {
                        text: " - Creating a design concept that matches client preferences, including color schemes and style.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Furniture and Fixture Selection", bold: true },
                      {
                        text: " - Helping choose furniture, lighting, and decorative pieces that fit the design and are practical.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Material Selection", bold: true },
                      {
                        text: " - Recommending durable and visually appealing materials for floors, walls, and countertops.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Color Consultation", bold: true },
                      {
                        text: " - Advising on color palettes to enhance the mood and lighting of a room.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Custom Design Elements", bold: true },
                      {
                        text: " - Designing unique built-in features like cabinets and shelves to fit specific needs.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Styling and Accessories", bold: true },
                      {
                        text: " - Selecting and arranging accessories and artwork to add personality to the space.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Consultation and Advice", bold: true },
                      {
                        text: " - Providing professional guidance on space use, colors, and furniture arrangement.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                ],
              },
              {
                text: "Preparation of Quantity Surveyor’s Cost Estimates – Ksh. 280,000.00",
                fontSize: 14,
                margin: [0, 0, 0, 20],
                bold: true,
              },
              {
                text: "This involves:",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                ul: [
                  {
                    text: [
                      { text: "Site Adaptation", bold: true },
                      {
                        text: " – To understand the location of the project for better accuracy in cost estimation.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Client Brief", bold: true },
                      {
                        text: " – Understanding the client’s needs and budget for the project.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Preliminary Cost Estimates", bold: true },
                      {
                        text: " – Preparation of cost estimates based on early scheme designs and briefs.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Detailed Cost Estimates", bold: true },
                      {
                        text: " – Prepare detailed bills of quantities based on final architectural, structural and interior design drawings and concepts.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                ],
              },
              
            ];
          } else if (clientInfo.type.toLowerCase() === "mansion") {
            articlesOfAgreement = [
              {
                text: "2.0 ARTICLES OF AGREEMENT",
                bold: true,
                fontSize: 14,
                alignment: "center",
                decoration: "underline",
                margin: [0, 0, 0, 20],
              },
              {
                text: "IT IS HEREBY AGREED AS FOLLOWS;",
                fontSize: 14,
                margin: [0, 0, 0, 20],
                decoration: "underline",
                bold: true,
              },
              {
                text: "1. The Contractor undertakes that they have all the necessary skills, technical knowledge and resources to undertake the tasks listed below.",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                text: "2. The client is hereby willing to engage the Contractor to undertake the following services selected below. Tick the check box(s) below to indicate the service(s) you wish to procure below.",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                text: "Tick the check box(s) below to indicate the service(s) you wish to procure below.",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                text: "[   ]Site Visit and Due Diligence (Mandatory) - Ksh. 50,000.00",
                fontSize: 14,
                margin: [0, 0, 0, 20],
                bold: true,
              },
              {
                text: "This involves:",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                ul: [
                  {
                    text: [
                      { text: "Due diligence", bold: true },
                      {
                        text: " will entail in-depth research and analysis of details regarding a property to ensure that all relevant factors have been put into consideration before development activities are undertaken. The following aspects will be involved:",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Status of a property", bold: true },
                      {
                        text: ": The process will involve confirmation of ownership status by ascertaining the land tenure through land search, title, survey and registry maps, and confirmation of beacons.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Site Analysis", bold: true },
                      {
                        text: ": This is a review of the land's physical attributes such as topography, neighborhood characteristics, existing developments and trends.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Review of Land Use Zoning", bold: true },
                      {
                        text: ": This review shall consider existing land use zoning laws and land use regulations for compliance with existing developments.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Infrastructure Assessment", bold: true },
                      {
                        text: ": This will consider existing infrastructure, such as roads, utilities, and other auxiliary services that aid service provision to the proposed site.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Environmental pre-assessment", bold: true },
                      {
                        text: ": This process will identify likely environmental issues, contamination, flooding risks, and whether any remediation/mitigation measures should be implemented.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                ],
              },
              {
                text: "[   ]Preparation of Architectural Design Drawings – Ksh. 150,000.00",
                fontSize: 14,
                margin: [0, 0, 0, 20],
                bold: true,
              },
              {
                text: "This involves:",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                ul: [
                  {
                    text: [
                      { text: "Site Investigations", bold: true },
                      { text: " – To determine site layout." },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Inception", bold: true },
                      {
                        text: " -Obtaining the initial statement of requirements from the client.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Advising the client", bold: true },
                      { text: " on the feasibility of the project." },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Scheme design", bold: true },
                      {
                        text: " - presentation of scheme designs; floor plans, sections, elevations for approval purposes.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Detailed Design", bold: true },
                      {
                        text: "- Small-scale construction drawings for site use.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                ],
              },
              {
                text: "[   ]Preparation of Structural Design Drawings – Ksh. 130,000.00",
                fontSize: 14,
                margin: [0, 0, 0, 20],
                bold: true,
              },
              {
                text: "This involves:",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                ul: [
                  {
                    text: [
                      { text: "Site Investigations", bold: true },
                      { text: " – To determine appropriate foundations." },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Inception", bold: true },
                      {
                        text: "-Obtaining the initial statement of requirements from the client.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Advising the client", bold: true },
                      { text: " on the possible project designs." },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Detailed Designs", bold: true },
                      { text: "-Construction drawings for site use." },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                ],
              },
              {
                text: "[   ]Preparation of Interior Design Concept Details -Ksh. 100,000.00",
                fontSize: 14,
                margin: [0, 0, 0, 20],
                bold: true,
              },
              {
                text: "This involves:",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                ul: [
                  {
                    text: [
                      { text: "Space Planning", bold: true },
                      {
                        text: " - Arranging furniture and optimizing traffic flow for better functionality.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Concept Development", bold: true },
                      {
                        text: " - Creating a design concept that matches client preferences, including color schemes and style.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Furniture and Fixture Selection", bold: true },
                      {
                        text: " - Helping choose furniture, lighting, and decorative pieces that fit the design and are practical.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Material Selection", bold: true },
                      {
                        text: " - Recommending durable and visually appealing materials for floors, walls, and countertops.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Color Consultation", bold: true },
                      {
                        text: " - Advising on color palettes to enhance the mood and lighting of a room.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Custom Design Elements", bold: true },
                      {
                        text: " - Designing unique built-in features like cabinets and shelves to fit specific needs.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Styling and Accessories", bold: true },
                      {
                        text: " - Selecting and arranging accessories and artwork to add personality to the space.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Consultation and Advice", bold: true },
                      {
                        text: " - Providing professional guidance on space use, colors, and furniture arrangement.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                ],
              },
              {
                text: "Preparation of Quantity Surveyor’s Cost Estimates – Ksh. 130,000.00",
                fontSize: 14,
                margin: [0, 0, 0, 20],
                bold: true,
              },
              {
                text: "This involves:",
                fontSize: 14,
                margin: [0, 0, 0, 20],
              },
              {
                ul: [
                  {
                    text: [
                      { text: "Site Adaptation", bold: true },
                      {
                        text: " – To understand the location of the project for better accuracy in cost estimation.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Client Brief", bold: true },
                      {
                        text: " – Understanding the client’s needs and budget for the project.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Preliminary Cost Estimates", bold: true },
                      {
                        text: " – Preparation of cost estimates based on early scheme designs and briefs.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                  {
                    text: [
                      { text: "Detailed Cost Estimates", bold: true },
                      {
                        text: " – Prepare detailed bills of quantities based on final architectural, structural and interior design drawings and concepts.",
                      },
                    ],
                    fontSize: 14,
                    margin: [0, 0, 0, 20],
                  },
                ],
              },
              
            ];
          }

          // Prepare contract content using client details
          const contractContent = [
            {
              alignment: "center",
              stack: [
                {
                  text: "AGREEMENT AND CONDITIONS OF CONTRACT FOR DESIGN WORKS",
                  bold: true,
                  fontSize: 17,
                  margin: [0, 0, 0, 20],
                  decoration: "underline",
                },
              ],
            },
            {
              text: [
                { text: "THIS AGREEMENT", bold: true, fontSize: 14 },
                {
                  text: " is made on the ...................... day of ………………… 2024.",
                  bold: false,
                },
              ],
              fontSize: 14,
            },

            {
              text: [
                { text: "BETWEEN ", fontSize: 14, bold: false },
                { text: "OPTIVEN CONSTRUCTION LIMITED", bold: true },
                {
                  text: " of P.O Box 623 – 00600, Nairobi in the Republic of Kenya hereinafter referred to as ",
                  bold: false,
                },
                { text: "“The Contractor” ", bold: true },
                {
                  text: "which expression shall where the context so admits include its successors and assigns of the one part; and ",
                  bold: false,
                },
                { text: clientInfo.client_name, bold: true },
                { text: " of ID/Passport Number ", bold: false },
                { text: clientInfo.id_no, bold: true },
                {
                  text: " and of P.O. Box .................................... in the Republic of Kenya hereinafter referred to as ",
                  bold: false,
                },
                { text: "“The Client” ", bold: true },
                {
                  text: "which expression shall where the context so admits include his respective personal representatives and assigns of the other part.",
                  bold: false,
                },
              ],
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },

            {
              text: "WHEREAS",
              bold: false,
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: [
                {
                  text: "A. The Client is the registered owner of ALL THAT piece or parcel of land known as ",
                  bold: false,
                },
                {
                  text: "LAND REFERENCE NUMBER " + clientInfo.LR_no,
                  bold: true,
                },
                { text: " containing by acreage ", bold: false },
                { text: clientInfo.plot_size, bold: true },
                {
                  text: " Ha or thereabouts hereinafter referred to as ",
                  bold: false,
                },
                { text: "“The Property”", bold: true },
              ],
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "B. The Client has agreed to engage The Contractor to provide the services described herein.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              alignment: "center",
              text: "1.0 DEFINITIONS",
              bold: true,
              fontSize: 14,
              margin: [0, 0, 0, 20],
              decoration: "underline",
            },
            { text: "1.1. “Agreement”", bold: true, fontSize: 14 },
            {
              text: "  Means this contract, including all its annexes, attachments, and any modifications agreed upon in writing by both parties.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "1.2. “Contractor” ", bold: true, fontSize: 14 },
            {
              text: " Refers to Optiven Construction Limited, the company duly registered with the National Construction Authority (NCA) to provide building and construction services specified in this Agreement with the Client.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "1.3. “Contract Documents” ", bold: true, fontSize: 14 },
            {
              text: " Shall include but will not be limited to Drawings, Bills of Quantities, Specifications, and invoices.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "1.4. “Client” ", bold: true, fontSize: 14 },
            {
              text: " Refers to the individual or entity entering into this agreement with the Contractor for the provision of services outlined herein.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "1.5. “Architect” ", bold: true, fontSize: 14 },
            {
              text: "Refers to a person skilled and professionally trained to design, plan and provide advice - both aesthetic and technical - on built objects or adapt an existing property.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "1.6. “Engineer” ", bold: true, fontSize: 14 },
            {
              text: "Refers to a person skilled and professionally trained to design, assess and inspect structures to ensure they are efficient and stable.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "1.7. “Quantity Surveyor”  ", bold: true, fontSize: 14 },
            {
              text: " Refers to a person/firm skilled and professionally trained to provide expert advice on construction costs, compare different options, track variations and cost control.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "1.8. “Interior Designer” ", bold: true, fontSize: 14 },
            {
              text: " Refers to a person skilled and professionally trained to design, create 3D visual impressions and details of the Works.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "1.9. “Approval” ", bold: true, fontSize: 14 },
            {
              text: " Means the process of obtaining formal authorization granted by the relevant governing body or regulatory authority recognized by the Government of Kenya.",
              fontSize: 14,
              margin: [0, 0, 0, 30],
            },
            { text: "1.10. “Drawings” ", bold: true, fontSize: 14 },
            {
              text: " Means all other drawings or details for the carrying out of the Works issued by the Architect or any other person with the delegated authority of the Architect that are not contract drawings. Any other drawings or details approved by the Structural Engineer for use in the Works shall have the same meaning.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "1.11. “Bills of Quantities” ", bold: true, fontSize: 14 },
            {
              text: " Means the document drawn up by the Quantity Surveyor in accordance with the specified Standard Method of Measurement, describing the conditions under which the scope, quality and quantity of Works including all contractual requirements necessary for the full performance of the Contract.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "1.12. “Works” ", bold: true, fontSize: 14 },
            {
              text: " Means the permanent works forming part of the Contract designed for the Client by the Architect, or other agents, to be carried out by the Contractor and as described in the Contract documents.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "1.13. “Site” ", bold: true, fontSize: 14 },
            {
              text: " Means the place or places where the permanent Works are to be carried out.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "1.14. “Standard Method of Measurement” ",
              bold: true,
              fontSize: 14,
            },
            {
              text: " Means the Standard Method of Measurement used in the preparation of the Bills of Quantities.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "1.15. “Specifications” ", bold: true, fontSize: 14 },
            {
              text: " Means a document that describes and defines the method, quality and standards to be attained in the carrying out and completion of the Works.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "1.16. “Confidential Information” ",
              bold: true,
              fontSize: 14,
            },
            {
              text: " Means any proprietary or sensitive information disclosed by one Party to the other in connection with this Agreement, including but not limited to plans, structural and architectural designs, trade secrets, financial data, business strategies, or client lists, whether disclosed orally, visually, or in writing, and marked as confidential or disclosed under circumstances indicating its confidentiality.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "1.17. “Due Diligence” ", bold: true, fontSize: 14 },
            {
              text: " Means the process of authenticating the documents provided by the client including and not limited to land title verification, obtaining land search, payment validation, land rates confirmation, and acquisition of maps where necessary.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "1.18. “Force Majeure” ", bold: true, fontSize: 14 },
            {
              text: " Means an event, occurrence or circumstance which;",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              ul: [
                {
                  text: "1.18.1 Is beyond a party’s control,",
                  margin: [20, 0, 0, 10],
                },
                {
                  text: "1.18.2 Such party could not reasonably have foreseen before entering into contract.",
                  margin: [20, 0, 0, 10],
                },
                {
                  text: "1.18.3 Having arisen, such party could not reasonably have avoided or overcome.",
                  margin: [20, 0, 0, 10],
                },
              ],
              fontSize: 14,
            },
            { text: "1.19. “Effective Date”  ", bold: true, fontSize: 14 },

            {
              text: " Refers to the date on which this Agreement becomes legally binding upon the Parties.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "1.20. “Intellectual Property Rights” ",
              bold: true,
              fontSize: 14,
            },
            {
              text: " Includes all patents, copyrights, trademarks, trade secrets, and any other intellectual property rights recognized by law.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "1.21. “Indemnify”  ", bold: true, fontSize: 14 },
            {
              text: " Refers to compensation for any losses or damages incurred as a result of specified circumstances.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "1.22. “Services” ", bold: true, fontSize: 14 },
            {
              text: " Means the specific services to be provided by the Contractor to the Client.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "1.23. “Terminate”", bold: true, fontSize: 14 },
            {
              text: " Refers to the conclusion or cessation of this Agreement, either through completion of the Services, expiration of the term, or by the action of one Party terminating the Agreement as provided for herein.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "", pageBreak: "after" },
            ...articlesOfAgreement,
            {
              text: "3. The Client is hereby bound by to pay the Contractor the sum of Kenya Shillings ………………………………………………………………………………to undertake the tasks selected in section (2) above.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: [
                "4. The Client shall remit the amount indicated in section (3) to the following account details:\n",
                { text: "Name: OPTIVEN CONSTRUCTION LIMITED\n", bold: true },
                { text: "Account number: *************\n", bold: true },
                { text: "Bank: EQUITY BANK LIMITED\n", bold: true },
                { text: "Branch: KENYATTA AVENUE\n", bold: true },
                { text: "Swift Code: EQBLKENA", bold: true },
              ],
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "5. In the event the Client terminates this Agreement after preparation of the above documents has commenced, the amounts paid by the Client shall be non-refundable.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "6. The Client hereby undertakes to offer the requisite assistance in the presentation of the drawings provided by the Contractor to the relevant statutory bodies for approval and clearance. The Contractor shall not be held liable for any omission by the Client in undertaking requisite action in the procurement of the necessary approvals of the drawings and documents including but not limited to undertaking due diligence where necessary ahead of undertaking construction of the same designs.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "7. The Contractor shall not be liable for any delays in preparing the above documents occasioned by any factors beyond its control also known as force majeure.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "8. The Contractor may terminate this agreement if the Client contravenes this agreement and the Client undertakes to indemnify the Contractor for any loss resulting from the same.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "9. Any variation or alteration to this agreement shall be in writing and shall be notified to the adverse party within 14 days thereof.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "10. Time will be of the essence in the execution and completion of this Agreement.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "11. This Agreement constitutes the entire agreement between the Parties and supersedes any representations, warranties or statements.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "12. Either party hereby agree and confirm that they have entered this Contract with the intention to bind themselves to its contents hereof.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "13. This Agreement shall be construed and interpreted in accordance with the laws of Kenya.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "14. Any notice required by this Agreement to be given by either Party to the other by electronic means and receipt of the electronic means will be deemed to have been effective within 48 hours of sending out the notice.",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: [
                {
                  text: "15. Confidentiality Clause\n",
                  bold: true,
                  decoration: "underline",
                },
                "The Client agrees to accept and hold confidential information supplied by the Contractor in complete confidence, and further agrees not to divulge such to any third party or use the same for their own benefit. The Client agrees to return all originals and copies of such data and all ancillary information derived therefrom to the Contractor promptly upon request, or termination of this Contract, whichever occurs first.",
              ],
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: [
                {
                  text: "16. Contractor’s Indemnity\n",
                  bold: true,
                  decoration: "underline",
                },
                "The Client undertakes to procure the services of competent professionals in the execution of the building designs. Consequently, the Client releases the Contractor from any liability that may arise from the misapplication of the designs.",
              ],
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: [
                {
                  text: "17. Professional Indemnity\n",
                  bold: true,
                  decoration: "underline",
                },
                "The Client is hereby informed that the designs both Architectural and Structural, Bills of Quantities and Design Visuals supplied by this Contract are not stamped copies and are therefore not to be executed as such in Construction Works. The Contractor and the professionals engaged are not liable for any works that arise from these documents beyond its express knowledge and acceptance in writing.",
              ],
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: [
                {
                  text: "18. Approvals\n",
                  bold: true,
                  decoration: "underline",
                },
                "The Client is informed that this contracts and their respective agreements do not cover the costs associated with Planning (change of use), Environmental Impact Assessment (EIA) and Building (County and NCA) approvals. However, we are willing to pursue the same for our client at an extra charge based on the project complexity and needs.",
              ],
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "", pageBreak: "after" },
            {
              text: [
                {
                  text: "IN WITNESS WHEREOF ",
                  bold: true,
                  decoration: "underline",
                },
                {
                  text: " the Parties have duly executed this Agreement the day and year first hereinbefore written.",
                  bold: false,
                },
              ],
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: [
                { text: "SIGNED", bold: true, decoration: "underline" },
                { text: " by the Contractor", bold: false },
              ],
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "OPTIVEN CONSTRUCTION LIMITED",
              bold: true,
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "In the presence of:",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "DIRECTOR …………………………… ",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "Common seal", fontSize: 14, margin: [0, 0, 0, 20] },
            {
              text: "DIRECTOR/SECRETARY …..………………………. ",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "In presence of:", fontSize: 14, margin: [0, 0, 0, 10] },
            {
              text: "ADVOCATE",
              bold: true,
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: [
                { text: "I CERTIFY ", bold: true },
                { text: " that the above- named ", bold: false },
                {
                  text: "Director and Director/Secretary of the Contractor ",
                  bold: true,
                },
                {
                  text: " appeared before me on the…………day of …...…………………. 2024 and being known to acknowledged the above signatures or marks to be theirs and that they had freely and voluntarily executed this Agreement and understood its contents.",
                  bold: false,
                },
              ],
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "------------------------------------------",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "Advocate",
              bold: true,
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },

            {
              text: [
                { text: "Signed", bold: true },
                { text: " by the Client", bold: false },
              ],
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: clientInfo.client_name + " ………………………………",
              bold: true,
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "In presence of:", fontSize: 14, margin: [0, 0, 0, 20] },
            {
              text: "ADVOCATE",
              bold: true,
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            { text: "", pageBreak: "after" },
            {
              text: [
                { text: "I CERTIFY", bold: true },
                { text: " that the above- named ", bold: false },
                { text: clientInfo.client_name, bold: true },
                {
                  text: " appeared before me on the…………….day of …...…………………. 2024 and being known to me/identified by ID Card/Passport Number …………….. acknowledged the above signature or marks to be theirs and that he had freely and voluntarily executed this Agreement and understood its contents.",
                  bold: false,
                },
              ],
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "------------------------------------------",
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "Advocate",
              bold: true,
              fontSize: 14,
              margin: [0, 0, 0, 20],
            },
            {
              text: "DRAWN BY:",
              bold: true,
              fontSize: 14,
              margin: [0, 0, 0, 10],
              decoration: "underline",
            },
            {
              text: "E.M KIPOTO ADVOCATE",
              fontSize: 14,
              margin: [0, 0, 0, 10],
            },
            { text: "ABSA TOWERS", fontSize: 14, margin: [0, 0, 0, 10] },
            { text: "14TH FLOOR", fontSize: 14, margin: [0, 0, 0, 10] },
            { text: "P.O. BOX 623-00600", fontSize: 14, margin: [0, 0, 0, 10] },
            {
              text: "NAIROBI",
              fontSize: 14,
              margin: [0, 0, 0, 10],
              decoration: "underline",
              bold: true,
            },
          ];

          try {
            const headerImagePath = path.join(
              __dirname,
              "../../../client/src/assets/optiven_homes_logo.png"
            );
            const footerImagePath = path.join(
              __dirname,
              "../../../client/src/assets/optiven_homes_footer.png"
            );
            const watermarkImagePath = path.join(
              __dirname,
              "../../../client/public/logo192.png"
            );

            const headerImageBase64 = imageToBase64(headerImagePath);
            const footerImageBase64 = imageToBase64(footerImagePath);
            const watermarkImageBase64 = imageToBase64(watermarkImagePath);

            const printer = new pdfMakePrinter(fonts);

            // Create a new PDF document
            const docDefinition = {
              pageSize: "A4",
              pageOrientation: "portrait",
              pageMargins: [40, 150, 40, 100], // Increase top margin and bottom margin
              header: (currentPage, pageCount, pageSize) => {
                return {
                  image: "headerImage",
                  width: pageSize.width / 1, // Reduce image width
                  alignment: "center", // Center the header
                  margin: [0, 0, 0, 0], // Adjust margins to place header correctly
                };
              },
              footer: (currentPage, pageCount, pageSize) => {
                return {
                  image: "footerImage",
                  width: pageSize.width / 1.5, // Reduce image width
                  alignment: "center", // Center the footer
                  margin: [0, 0, 0, 10], // Adjust margins to place footer lower
                };
              },
              background: (currentPage, pageSize) => {
                return {
                  image: "watermarkImage",
                  width: pageSize.width / 2,
                  opacity: 0.5,
                  absolutePosition: {
                    x: pageSize.width / 4,
                    y: pageSize.height / 3,
                  },
                };
              },
              content: contractContent,
              images: {
                headerImage: "data:image/png;base64," + headerImageBase64,
                footerImage: "data:image/png;base64," + footerImageBase64,
                watermarkImage: "data:image/png;base64," + watermarkImageBase64,
              },
            };

            const pdfDoc = printer.createPdfKitDocument(docDefinition);
            res.setHeader("Content-Type", "application/pdf");
            res.setHeader(
              "Content-Disposition",
              `attachment; filename="contract_${id}.pdf"`
            );
            pdfDoc.pipe(res);
            pdfDoc.end();
          } catch (error) {
            console.error("Error generating PDF:", error);
            res.status(500).json({ message: "Error generating PDF" });
          }
        }
      );
    } catch (error) {
      console.error("Error handling contract download:", error);
      res.status(500).json({ message: "Error handling contract download" });
    }
  });

  // handle the constuction contract download

  router.get("/:id/const-contract", async (req, res) => {
    const { id } = req.params;

    try {
      // Fetch client details from the database
      pool.query(
        `SELECT client_name, id_no, LR_no, plot_size, cost, type FROM homes_clients WHERE id = ?`,
        [id],
        async (err, result) => {
          if (err) {
            console.error("Error fetching client details:", err);
            return res.status(500).json({
              message: "An error occurred while fetching the client's info.",
            });
          }

          if (result.length === 0) {
            return res.status(404).json({ message: "Client not found" });
          }

          const clientInfo = result[0];

          // Prepare contract content using client details
          const contractContent = `
DATED THE 			DAY OF 				2024


OPTIVEN HOMES LIMITED
(as the “CONTRACTOR”)

and

${clientInfo.client_name} 
(as the “CLIENT”)

_____________________________________________________________________________
AGREEMENT FOR BUILDING WORKS TO BE CONDUCTED ON 
LAND TITLE NUMBER ${clientInfo.LR_no}




DRAWN BY:
J.K MBUVI ADVOCATE
ABSA TOWERS
2ND FLOOR
P.O. BOX 623-00600
NAIROBI







THIS AGREEMENT is made the		day of 			2024 

BETWEEN

1.	OPTIVEN HOMES LIMITED a Company duly registered in Kenya under The Companies Act, 2015 under registration number PVT-ZQULZDJ9 and of  Post Office Box Number 623-00600 ABSA Towers, Loita Street 2nd  floor Wing A in the Republic of Kenya (hereinafter called the “Contractor” and which expression shall where the context so admits include its successors in title and assigns) of one part;  and

2.	${clientInfo.client_name} of Kenyan National Identity Card Number ${clientInfo.id_no} and of Post Office Box Number ………………, Nairobi (hereinafter together called the “Client” which expression where the context so admits shall include the Client’s personal representatives and assigns) of the other part.  
WHEREAS:

A.	The Client is the registered Proprietor of all that land known as Land Reference No. ${clientInfo.LR_no} measuring approximately ${clientInfo.plot_size} Ha (herein after the “Property”).

B.	The Client is desirous of erecting a residential bungalow on the Property.

C.	The Client has reviewed and agreed to the correctness of the bill of quantities by the Quantity Surveyor and the drawings by the Architect. The Client disclaims that she has entered into this Agreement in reliance upon any express or implied representation made by or on behalf of the Contractor.

D.	For the consideration stated herein, the Contractor shall carry out and complete the building works as per the architectural drawings and bill of quantities.


NOW IT IS HEREBY AGREED AS FOLLOWS: -

1.	DEFINITIONS

1.1.	“Building Works” means the works which are to be carried out in connection with the construction of the residential bungalow.

1.2.	“Completion Date” means a period of seven (7) months after execution of this agreement and receipt of the payments as prescribed under Clause 3.1.1.

1.3.	“Defect” means any defect in the Building Works which was not carried out in accordance with the Architectural Drawings and this Agreement. The Client shall notify the Contractor of the same before the expiry of the Defects Liability Period. The defects shall be subjected to a physical inspection by both parties.

1.4.	“Defects Liability Period” means a period of six (6) months effective from the Completion Date.

2.	THE CONTRACTOR'S DUTIES 

2.1	The Contractor will carry out and complete the works as stipulated in the architectural and structural drawings and bill of quantities within the time prescribed in this Agreement.
2.2	The Contractor shall have the right (in its reasonable discretion) to make such revisions, variations or modifications to the Architectural Drawings in so far as the same:
i.	will not affect the external facade of the construction to a material extent; or
ii.	to obtain any necessary licenses or permissions or complying with any requirement made by any local or other authority.
2.3	The Contractor will only make good the defects raised within the defects liability period. 
2.4	Any Fittings and Fitted Equipment (including any lights, sanitary fittings, bathroom and washroom fittings and equipment) that may be installed in the Residential House have been chosen based on the pricing for the works and on the reputation of the manufacturers and suppliers of the said fittings and fitted equipment. Consequently, the Contractor gives no warranties whatsoever as to the quality, worthiness, durability, suitability and usage of any of the said fittings and fitted equipment that may be installed in the Residential House and the Client hereby expressly accepts this condition.
2.5	The Contractor shall engage construction workers and site supervisors who are accredited by the National Construction Authority (NCA) and ensure the highest possible standard of workmanship.
2.6	The Contractor warrants to use materials and equipment for the project that meet the approved standards to ensure structural stability.
2.7	The Contractor shall apply and procure all requisite approvals from the relevant authorities.
2.8	The Contractor agrees to apply part of the contingency budget to cover minor changes if any as proposed by the client during the defect liability period.
2.9	The Contractor shall arrange for handover of the bungalow on the site in the presence of the Client or his/her authorized representative. The inspection of the completed works shall be by use of a handover checklist.

3.	THE CLIENT’S DUTIES 
3.1.	The Client shall pay to the Contractor a total sum of Kenya Shillings Ten Million Seven Hundred and Nine Thousand and Twenty One and Fifty Cents Only (Kshs. 10,709,021.50/-) (hereinafter referred to as the “Contract Price”) for the Building Works rendered.
3.1.1	The Contract price shall be paid as follows:
Phase I - First payment
50% upon commencement amounting to Kshs. 5,354,510/=
During this phase the following shall take place:
A.	Sub structure works;
B.	Super structure walling; and
C.	Roof slab formwork.
Phase II - Second payment
20% upon completion of Phase I amounting to Ksh. 2,141,804.30/=
During this phase the following shall take place:
A.	Roof slab steel work;
B.	Concreting; and
C.	Doors and windows fabrications
Phase III - Third payment
15% upon completion of Phase II amounting to Kshs. 1,606,353.22/=
During this phase the following shall take place:
A.	Electrical and plumbing piping;
B.	Plaster work; 
C.	Gypsum work; 
D.	Ceiling/wall skimming; and
E.	Tiling.
Phase IV - Fourth payment
10% upon completion of Phase III amounting to Kshs. 1,070,902.15/= During this phase the following shall take place:
A.	Joinery works;
B.	Fitting works; and
C.	Painting and drawing works.
Fifth payment - Phase V
5% after the 6 months defect liability period. The retention fee amounts to Kshs. 535,451.83/=

That in the event that the Client does not strictly adhere to the said mode of Payment of the Contract Price, the Contractor shall have the discretion to treat the same as non-payment which is a fundamental breach of the Client’s obligations under this Agreement. Consequently, the provisions of Clause 4.2 shall apply. 

3.2.	The Contract Price shall be paid to the Contractor by means of Real Time Gross Settlement (RTGS) to the following account: 

NAME	Optiven Construction Limited
ACCOUNT NUMBERS	*************
BANK	EQUITY BANK LIMITED
BRANCH	KENYATTA AVENUE
SWIFT CODE	EQBLKENA 

3.3	The Client hereby confirms that the funds in respect of the payment of the Contract Price are not and were not at any time from the proceeds of crime as defined in the Proceeds of Crime and Anti-Money Laundering Act of Kenya (No. 9 of 2009 as amended). The Client warrants to the Contractor that the Client has not been involved in or convicted for corrupt practices or economic crimes as defined in Section 2 (1) of the Anti-Corruption and Economic Crimes Act No. 3 of 2003 and that the funds used by the Client in the construction of the Residential Bungalow are not an unexplained asset as defined in the said Act.
4.	FAILURE TO COMPLETE
4.1	If the Client fails to pay the contract price on the due date, the Contractor may give the Client a seven (7) day written notice requiring the Client to remedy the same before expiry of such notice failure to which the Contractor shall rescind this Agreement.
4.2	If the Contractor rescinds this Agreement under or pursuant to Clause 4.1 above, it is hereby agreed that:
4.2.1	The Client shall immediately pay to the Contractor 10% of the Contract Price as agreed liquidated damages.
4.2.2	The Contractor shall be entitled to also deduct any other payments including such costs as shall have been expended by the Contractor and any such other sums which may be lawfully due from the Client to the Contractor.
4.3	In the event the Building Works are not completed on or before the Completion Date, the Contractor shall in writing inform the Client of the reason for such failure and give an alternative Completion Date, which date shall not exceed twelve (12) months from the initial completion date. If completion of the Building Works exceeds the alternative Completion Date, then the Client shall be entitled to either extend the time for completion or terminate this Agreement upon giving the Contractor a twenty-one (21) days’ notice.
4.4	If this Agreement is terminated according to Clause 4.3 above, the Contractor shall forthwith refund to the Client unutilized amounts or such part thereof as shall have been paid to the Contractor by the date of such termination within six (6) months thereof.
5.	TIME IS OF THE ESSENCE
Time will be of the essence in the execution and completion of this contract.

6.	RISK
Risk shall pass to the Client upon handover and the Client shall be liable to insure the bungalow against risks such as fire, earthquake, accident, lightning and floods.

7.	FORCE MAJEURE
7.1.	The Contractor shall not be liable for any delays to perform its obligations caused by any of the following:
a.	any act of God;
b.	any war or hostilities (whether the war is declared or not
c.	any act of terror;
d.	any sabotage, riots or other act of civil disobedience, civil commotion, rebellion, act of a public enemy, invasions, strikes and lockouts;
e.	any suits, administrative actions, judicial actions, lockouts, industrial disputes or actions of any such nature;
f.	any actions or proceedings of or by any government or other competent authority or any agency;
g.	any non-performance by any third party contracted with by the Contractor in connection with its obligations under this Agreement;
h.	any storms, floods or other inclement weather, earthquakes, subsidence, epidemics or other natural physical disasters;
i.	fire, accident, explosion, or shortage or delay or non-availability of labour, power or water or products and materials required in connection with building works or currency exchange rates which are material in the opinion of the Contractor;
j.	epidemics or pandemics; and
k.	other causes or unforeseen circumstances beyond the reasonable control of the Contractor;

AND if from any of the causes or events above whatsoever completion shall be delayed beyond the Completion Date the Client shall not be entitled to compensation or damages thereon.

8.	GENERAL PROVISIONS
8.1	No failure or delay to exercise any power, right or remedy shall operate as a waiver of that right, power or remedy and no singular or partial exercise of any right, power or remedy shall preclude its further exercise or the exercise of any other right, power or remedy.
8.2	The rights and remedies provided in this Agreement are cumulative and not exclusive of any rights or remedies provided by law.
8.3	If any term or condition of this Agreement shall to any extent be found or held to be invalid or unenforceable, the parties shall negotiate in good faith to amend such term or condition so as to be valid and enforceable. If any term or condition of this Agreement shall to any extent be invalid or unenforceable, the remainder of this Agreement shall not be affected and each other term and condition shall be valid and enforceable to the fullest extent permitted by law.
8.4	This Agreement constitutes the entire agreement between the parties and supersede any representations, warranties, or statements.
8.5	Each of the parties hereby agree and confirm that it/she has executed this Agreement with the intention to bind itself or herself to the contents hereof.
8.6	This Agreement shall be construed and interpreted in accordance with the laws of Kenya.
8.7	Any notice required by this Agreement to be given by either Party to the other by electronic means and receipt of the electronic means will be deemed to have been effective within 48 hours of sending out the notice.

IN WITNESS WHEREOF the Parties have duly executed this Agreement the day and year first hereinbefore written.

SIGNED by the Contractor: -						)
OPTIVEN HOMES LIMITED 						)			
In the presence of : 
					
DIRECTOR                                       …………………………… 	)	Common seal		

DIRECTOR/SECRETARY               …..……………………….	)		

In presence of:											
ADVOCATE	


I CERTIFY that the above- named Director and Director/Secretary of the Contractor appeared before me on the…………day of …...…………………. 2024 and being known to acknowledged the above signatures or marks to be theirs and that they had freely and voluntarily executed this Agreement and understood its contents.

------------------------------------------
Advocate







Signed by the Client
${clientInfo.client_name} 	) ………………………………		




In presence of:						
ADVOCATE

I CERTIFY that the above- named ${clientInfo.client_name} appeared before me on the…………….day of …...…………………. 2024 and being known to me/identified by ID Card/Passport Number …………….. acknowledged the above signature or marks to be theirs and that she had freely and voluntarily executed this Agreement and understood its contents.

------------------------------------------
Advocate



DRAWN BY:
J.K MBUVI ADVOCATE
ABSA TOWERS
2ND FLOOR
P.O. BOX 623-00600
NAIROBI
`;

          try {
            const printer = new pdfMakePrinter(fonts);
            // Create a new PDF document
            const docDefinition = {
              pageSize: "A4",
              pageOrientation: "portrait",
              content: contractContent,
            };

            const pdfDoc = printer.createPdfKitDocument(docDefinition);
            res.setHeader("Content-Type", "application/pdf");
            res.setHeader(
              "Content-Disposition",
              `attachment; filename="contract_${id}.pdf"`
            );
            pdfDoc.pipe(res);
            pdfDoc.end();
          } catch (error) {
            console.error("Error generating PDF:", error);
            res.status(500).json({ message: "Error generating PDF" });
          }
        }
      );
    } catch (error) {
      console.error("Error handling contract download:", error);
      res.status(500).json({ message: "Error handling contract download" });
    }
  });

  // Delete client
  router.delete("/:id", (req, res) => {
    try {
      pool.query("DELETE * FROM homes_client WHERE id =? ", (error, result) => {
        if (error) {
          return res.status(500).json({ message: "Error deleting client" });
        }
        res.status(200).json(result);
      });
    } catch (error) {
      console.error(error);
    }
  });
  return router;
};
