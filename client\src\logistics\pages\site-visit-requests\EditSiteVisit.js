import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Sidebar from "../../components/Sidebar";
import { Trash2, PlusCircle } from "lucide-react"; // Icons for better UI

// Utility functions
import formatDate from "../../../utils/formatDate";
import { TailSpin } from "react-loader-spinner";

const EditSiteVisit = () => {
  const [sites, setSites] = useState([]);
  const [siteVisit, setSiteVisit] = useState({
    project_id: "",
    pickup_location: "",
    pickup_time: "",
    pickup_date: "",
    clients: [],
    marketer_id: "",
  });
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);

  const token = useSelector((state) => state.user.token);
  const navigate = useNavigate();
  const { id } = useParams();

  // Fetch Sites List
  useEffect(() => {
    const fetchSites = async () => {
      try {
        const response = await fetch(
          "https://workspace.optiven.co.ke/api/sites",
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (!response.ok) {
          throw new Error("Failed to fetch sites.");
        }
        const data = await response.json();
        setSites(data);
      } catch (error) {
        console.error("Error fetching sites:", error);
        setError("Unable to load sites. Please try again later.");
      }
    };

    fetchSites();
  }, [token]);

  // Fetch Site Visit Details
  useEffect(() => {
    const fetchSiteVisit = async () => {
      try {
        const response = await fetch(
          `https://workspace.optiven.co.ke/api/site-visit-requests/${id}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (!response.ok) {
          throw new Error("Failed to fetch site visit details.");
        }
        const siteVisitData = await response.json();

        const simplifiedSiteVisit = {
          project_id: siteVisitData.project_id || "",
          pickup_location: siteVisitData.pickup_location || "",
          pickup_time: siteVisitData.pickup_time || "",
          pickup_date: formatDate(siteVisitData.pickup_date) || "",
          clients: siteVisitData.clients.map((client) => ({
            name: client.client_name || "",
            email: client.client_email || "",
            phone_number: client.client_phone || "",
          })),
          marketer_id: siteVisitData.marketer_id || "",
        };

        setSiteVisit(simplifiedSiteVisit);
      } catch (error) {
        console.error("Error fetching site visit:", error);
        setError("Unable to load site visit details. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchSiteVisit();
  }, [id, token]);

  // Handle Input Changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setSiteVisit((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle Client Changes
  const handleClientChange = (index, e) => {
    const { name, value } = e.target;
    const updatedClients = [...siteVisit.clients];
    updatedClients[index] = {
      ...updatedClients[index],
      [name]: value,
    };
    setSiteVisit((prev) => ({
      ...prev,
      clients: updatedClients,
    }));
  };

  // Add New Client
  const handleAddClient = () => {
    setSiteVisit((prev) => ({
      ...prev,
      clients: [...prev.clients, { name: "", email: "", phone_number: "" }],
    }));
  };

  // Remove Client
  const handleRemoveClient = (index) => {
    const updatedClients = [...siteVisit.clients];
    updatedClients.splice(index, 1);
    setSiteVisit((prev) => ({
      ...prev,
      clients: updatedClients,
    }));
  };

  // Handle Form Submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);

    const requestObject = {
      project_id: siteVisit.project_id,
      pickup_location: siteVisit.pickup_location,
      pickup_time: siteVisit.pickup_time,
      pickup_date: formatDate(siteVisit.pickup_date),
      clients: siteVisit.clients.map((client) => ({
        name: client.name,
        email: client.email,
        phone_number: client.phone_number,
      })),
      marketer_id: siteVisit.marketer_id,
    };

    try {
      const response = await fetch(
        `https://workspace.optiven.co.ke/api/site-visits/${id}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(requestObject),
        }
      );

      if (response.ok) {
        toast.success("Site visit successfully updated!", {
          position: "top-center",
          autoClose: 3000,
        });
        navigate("/my-site-visits");
      } else {
        const data = await response.json();
        throw new Error(data.message || "Error updating the site visit.");
      }
    } catch (error) {
      console.error("Error updating site visit:", error);
      toast.error(error.message || "Error updating the site visit.", {
        position: "top-center",
        autoClose: 3000,
      });
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Sidebar>
        <div className="flex flex-col items-center justify-center min-h-screen">
          <TailSpin
            height="80"
            width="80"
            color="#4fa94d"
            ariaLabel="Loading"
          />
          <p className="mt-4 text-gray-700">Loading site visit details...</p>
        </div>
      </Sidebar>
    );
  }

  if (error) {
    return (
      <Sidebar>
        <div className="flex flex-col items-center justify-center min-h-screen">
          <p className="text-red-500 text-lg">{error}</p>
        </div>
      </Sidebar>
    );
  }

  return (
    <Sidebar>
      <div className="flex flex-col items-center p-6 bg-gray-100 min-h-screen">
        {/* Form Container */}
        <div className="w-full max-w-4xl bg-white rounded-lg shadow-lg p-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
            Edit Site Visit
          </h2>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Site Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label
                  htmlFor="project_id"
                  className="block text-gray-700 font-medium mb-2"
                >
                  Site Name
                </label>
                <select
                  id="project_id"
                  name="project_id"
                  value={siteVisit.project_id}
                  onChange={handleChange}
                  className="select select-bordered w-full"
                  required
                >
                  <option value="">Select a site</option>
                  {sites.map((site) => (
                    <option key={site.project_id} value={site.project_id}>
                      {site.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label
                  htmlFor="pickup_location"
                  className="block text-gray-700 font-medium mb-2"
                >
                  Pickup Location
                </label>
                <input
                  type="text"
                  id="pickup_location"
                  name="pickup_location"
                  value={siteVisit.pickup_location}
                  onChange={handleChange}
                  className="input input-bordered w-full"
                  required
                />
              </div>
            </div>

            {/* Pickup Location and Time */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label
                  htmlFor="pickup_date"
                  className="block text-gray-700 font-medium mb-2"
                >
                  Pickup Date
                </label>
                <input
                  type="date"
                  id="pickup_date"
                  name="pickup_date"
                  value={siteVisit.pickup_date}
                  onChange={handleChange}
                  className="input input-bordered w-full"
                  required
                />
              </div>

              <div>
                <label
                  htmlFor="pickup_time"
                  className="block text-gray-700 font-medium mb-2"
                >
                  Pickup Time
                </label>
                <input
                  type="time"
                  id="pickup_time"
                  name="pickup_time"
                  value={siteVisit.pickup_time}
                  onChange={handleChange}
                  className="input input-bordered w-full"
                  required
                />
              </div>
            </div>

            {/* Clients Section */}
            <div>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-semibold text-gray-700">Clients</h3>
                <button
                  type="button"
                  onClick={handleAddClient}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition"
                >
                  <PlusCircle className="w-5 h-5" />
                  Add Client
                </button>
              </div>
              {siteVisit.clients.length > 0 ? (
                siteVisit.clients.map((client, index) => (
                  <div
                    key={index}
                    className="border rounded-lg p-6 mb-4 bg-gray-50 relative"
                  >
                    <button
                      type="button"
                      onClick={() => handleRemoveClient(index)}
                      className="absolute btn-error top-4 right-4 text-white rounded-full p-1"
                    >
                      <Trash2 className="w-5 h-5" />
                    </button>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div>
                        <label
                          htmlFor={`clients[${index}].name`}
                          className="block text-gray-700 font-medium mb-2"
                        >
                          Client Name
                        </label>
                        <input
                          type="text"
                          id={`clients[${index}].name`}
                          name="name"
                          value={client.name}
                          onChange={(e) => handleClientChange(index, e)}
                          className="input input-bordered w-full"
                          required
                        />
                      </div>
                      <div>
                        <label
                          htmlFor={`clients[${index}].email`}
                          className="block text-gray-700 font-medium mb-2"
                        >
                          Client Email
                        </label>
                        <input
                          type="email"
                          id={`clients[${index}].email`}
                          name="email"
                          value={client.email}
                          onChange={(e) => handleClientChange(index, e)}
                          className="input input-bordered w-full"
                        />
                      </div>
                      <div>
                        <label
                          htmlFor={`clients[${index}].phone_number`}
                          className="block text-gray-700 font-medium mb-2"
                        >
                          Client Phone Number
                        </label>
                        <input
                          type="tel"
                          id={`clients[${index}].phone_number`}
                          name="phone_number"
                          value={client.phone_number}
                          onChange={(e) => handleClientChange(index, e)}
                          className="input input-bordered w-full"
                          required
                        />
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500">No clients added yet.</p>
              )}
            </div>

            {/* Submit Button */}
            <div className="flex justify-center">
              <button
                type="submit"
                disabled={submitting || siteVisit.clients.length === 0}
                className={`px-6 py-3 rounded-full text-white font-semibold ${
                  submitting
                    ? "bg-gray-400 cursor-not-allowed"
                    : "bg-green-500 hover:bg-green-600"
                } transition`}
              >
                {submitting ? "Updating..." : "Update Site Visit"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </Sidebar>
  );
};

export default EditSiteVisit;
