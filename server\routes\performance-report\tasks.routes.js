const express = require("express");
const router = express.Router();
const nodemailer = require("nodemailer");

module.exports = (pool) => {
  // GET all tasks with staff names
  router.get("/", (req, res) => {
    const query = `
      SELECT 
        task_info.*, 
        users.fullnames AS staff_name 
      FROM task_info 
      INNER JOIN defaultdb.users AS users ON task_info.staff_id = users.user_id`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        const tasksWithStaffNames = results.map((task) => ({
          ...task,
          staff_name: task.staff_name,
        }));

        res.json(tasksWithStaffNames);
      }
    });
  });

  // GET a specific task by ID
  router.get("/:id", (req, res) => {
    const { id } = req.params;
    const query = `
      SELECT 
        task_info.*, 
        users.fullnames AS staff_name 
      FROM task_info 
      INNER JOIN defaultdb.users AS users ON task_info.staff_id = users.user_id 
      WHERE task_info.id = ?`;

    pool.query(query, [id], (err, result) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else if (result.length > 0) {
        res.json(result[0]);
      } else {
        res.status(404).json({ message: "Task not found" });
      }
    });
  });

  // GET tasks assigned to a specific user by user ID
  router.get("/assigned/:userId", (req, res) => {
    const { userId } = req.params;
    const query = `
      SELECT 
        task_info.*, 
        users.fullnames AS staff_name 
      FROM task_info 
      INNER JOIN defaultdb.users AS users ON task_info.staff_id = users.user_id 
      WHERE task_info.staff_id = ?`;

    pool.query(query, [userId], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        const tasksAssignedToUser = results.map((task) => ({
          ...task,
          staff_name: task.staff_name,
        }));

        res.json(tasksAssignedToUser);
      }
    });
  });

  // CREATE a new task
  router.post("/", (req, res) => {
    const tasks = Array.isArray(req.body) ? req.body : [req.body];

    // Check if tasks is an array and it's not empty
    if (!tasks.length) {
      return res.status(400).json({ message: "Invalid task data" });
    }

    const query =
      "INSERT INTO task_info (name, expected_output, figure, staff_id, start_date, end_date, actual_output) VALUES (?, ?, ?, ?, ?, ?, ?)";

    const results = [];

    tasks.forEach((task) => {
      const {
        name,
        expected_output,
        figure,
        staff_id,
        start_date,
        end_date,
        actual_output,
      } = task;
      const formattedFigure = figure ? figure.replace(/,/g, "") : null;

      pool.query(
        query,
        [
          name,
          expected_output,
          formattedFigure,
          staff_id,
          start_date,
          end_date,
          actual_output,
        ],
        async (err, result) => {
          if (err) {
            console.error(err);
            results.push({
              error: true,
              message: "Error creating task",
            });
          } else {
            results.push({
              error: false,
              message: "Task created successfully",
            });

            // Fetch assigned user's email
            const fetchUserEmailQuery =
              "SELECT email FROM defaultdb.users WHERE user_id = ?";
            pool.query(fetchUserEmailQuery, [staff_id], (err, userResult) => {
              if (err) {
                console.error(err);
                return;
              }

              const userEmail = userResult[0]?.email;

              if (userEmail) {
                // Send email to the assigned user
                const subject = "New Task Assigned";
                const text = `Dear User,

A new task has been assigned to you. 
Please check your tasks on the Optiven Senior Leadership Reporting System for details.

Best regards.`;

                sendEmail(userEmail, subject, text);
              }
            });
          }

          if (results.length === tasks.length) {
            const hasErrors = results.some((result) => result.error);
            if (hasErrors) {
              res.status(500).json({ message: "Server Error" });
            } else {
              res.json({ message: "Tasks created successfully" });
            }
          }
        }
      );
    });
  });

  // Nodemailer helper function to send email
  async function sendEmail(userEmail, subject, text) {
    // create reusable transporter object using the default SMTP transport
    let transporter = nodemailer.createTransport({
      host: "smtp.zoho.com",
      port: 465,
      secure: true,
      auth: {
        user: process.env.DOMAIN_EMAIL, // your domain email account from .env
      pass: process.env.DOMAIN_PASSWORD,
      },
    });

    // send mail with defined transport object
    let info = await transporter.sendMail({
      from: '"Optiven Notifications" <<EMAIL>>',
      to: userEmail,
      subject: subject,
      text: text,
    });

    console.log("Email sent:", info.messageId);
  }

  // Separate PATCH request for updating actual_output
  router.patch("/:id", (req, res) => {
    const { id } = req.params;
    const { actual_output } = req.body;
    const query = "UPDATE task_info SET actual_output = ? WHERE id = ?";
    pool.query(query, [actual_output, id], (err, result) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else if (result.affectedRows > 0) {
        res.json({ message: "Actual output updated successfully" });
      } else {
        res.status(404).json({ message: "Task not found" });
      }
    });
  });

  // DELETE a task by ID
  router.delete("/:id", (req, res) => {
    const { id } = req.params;
    pool.query("DELETE FROM task_info WHERE id = ?", [id], (err, result) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else if (result.affectedRows > 0) {
        res.json({ message: "Task deleted successfully" });
      } else {
        res.status(404).json({ message: "Task not found" });
      }
    });
  });

  // GET tasks for the current month with request parameters
  router.get("/current-month/:start/:end", (req, res) => {
    const startOfMonth = new Date(req.params.start);
    const endOfMonth = new Date(req.params.end);

    // Check if the date parsing is successful
    if (isNaN(startOfMonth.getTime()) || isNaN(endOfMonth.getTime())) {
      return res.status(400).json({ message: "Invalid date parameters" });
    }

    // Set the time to midnight for accurate date comparison
    startOfMonth.setUTCHours(0, 0, 0, 0);
    endOfMonth.setUTCHours(23, 59, 59, 999);

    // console.log("Start Date:", startOfMonth.toISOString());
    // console.log("End Date:", endOfMonth.toISOString());

    const query = `
    SELECT 
      task_info.*, 
      users.fullnames AS staff_name 
    FROM task_info 
    INNER JOIN defaultdb.users AS users ON task_info.staff_id = users.user_id 
    WHERE task_info.start_date >= DATE(?) AND task_info.end_date <= DATE(?)`;

    pool.query(query, [startOfMonth, endOfMonth], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        const tasksForCurrentMonth = results.map((task) => ({
          ...task,
          staff_name: task.staff_name,
        }));

        res.json(tasksForCurrentMonth);
      }
    });
  });

  // Check this route
  // bonge la issues

  // GET tasks assigned to a specific user within a custom date range
router.get("/assigned/:userId/:start/:end", (req, res) => {
  const { userId, start, end } = req.params;

  // Parse start and end dates
  const startDate = new Date(start);
  const endDate = new Date(end);

  // Check if the date parsing is successful
  if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    return res.status(400).json({ message: "Invalid date parameters" });
  }

  // Set the time to midnight for accurate date comparison
  startDate.setUTCHours(0, 0, 0, 0);
  endDate.setUTCHours(23, 59, 59, 999);

  const query = `
    SELECT 
      task_info.*, 
      users.fullnames AS staff_name 
    FROM task_info 
    INNER JOIN defaultdb.users AS users ON task_info.staff_id = users.user_id 
    WHERE task_info.staff_id = ? AND task_info.start_date >= ? AND task_info.end_date <= ?`;

  pool.query(query, [userId, startDate, endDate], (err, results) => {
    if (err) {
      console.error(err);
      res.status(500).json({ message: "Server Error" });
    } else {
      // Check if any tasks are assigned to the user within the specified date range
      if (results.length === 0) {
        return res.status(404).json({ message: "No tasks found for the user within the specified date range" });
      }

      const tasksAssignedToUser = results.map((task) => ({
        ...task,
        staff_name: task.staff_name,
      }));

      res.json(tasksAssignedToUser);
    }
  });
});

  return router;
};
