import React, { useEffect, useMemo, useState } from "react";
import Sidebar from "../components/Sidebar";
import axios from "axios";
import _404cat from "../assets/svgs/404-cat.svg";
import { toast } from "react-toastify";

function formatDate(inputDate) {
  const date = new Date(inputDate);

  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0"); // Months are zero-based
  const day = date.getDate().toString().padStart(2, "0");

  return `${year}-${month}-${day}`;
}

const UpdatePayments = () => {
  const [payment, setPayment] = useState([]);
  const [projects, setProjects] = useState([]);
  // SEARCHBAR: Initialize the search query to an empty string
  const [query, setQuery] = useState("");

  useEffect(() => {
    const fetchPayments = async () => {
      try {
        const response = await axios.get(
          "https://workspace.optiven.co.ke/api/value_addition_payments/casual-labourers-payments",
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem("token")}`,
            },
          }
        );
        setPayment(response.data);
      } catch (error) {
        console.error(error);
      }
    };
    const fetchProjects = async () => {
      try {
        const response = await axios.get(
          "https://workspace.optiven.co.ke/api/projects/all",
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem("token")}`,
            },
          }
        );
        setProjects(response.data);
      } catch (error) {
        console.error(error);
      }
    };

    fetchPayments();
    fetchProjects();
  }, []);

  // SEARCHBAR: First filter the array of items(payment) to return an array that does not include the value of the query
  // SEARCHBAR: Memoize the filtered items, to only re-render when the value of the input or payment changes
  const filteredCasualLabourer = useMemo(
    () =>
      // eslint-disable-next-line array-callback-return
      payment.filter((member) => {
        if (query === "") {
          return member;
        } else if (member.name.toLowerCase().includes(query.toLowerCase())) {
          return member;
        }
      }),
    [query, payment]
  );

  const handleDelete = async (id) => {
    try {
      await axios.delete(`https://workspace.optiven.co.ke/api/value_addition_payments/${id}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      });
      setPayment(payment.filter((member) => member.id !== id));
      toast.success("Payment deleted successfully!", {
        position: "top-center",
        autoClose: 2000,
        hideProgressBar: true,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
    } catch (error) {
      console.error(error);
      toast.error("Payment has not been deleted", error.response.data, {
        position: "top-center",
        autoClose: 2000,
        hideProgressBar: true,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
    }
  };
  //   handle update
  const handleUpdate = async (id, work_days, total_amount) => {
    try {
      await axios.patch(
        `https://workspace.optiven.co.ke/api/value_addition_payments/${id}/updatePay`,
        {
          work_days: work_days,
          total_amount: total_amount,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        },
        
      );
      setPayment(payment.filter((member) => member.id !== id));
      // Assuming you want to update the local state after successfully updating on the server
      // You can add logic here to handle the updated data if necessary
      toast.success("Payment updated successfully!", {
        position: "top-center",
        autoClose: 2000,
        hideProgressBar: true,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
    } catch (error) {
      console.error(error);
      toast.error("Failed to update payment ", {
        position: "top-center",
        autoClose: 2000,
        hideProgressBar: true,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
    }
  };

  const handleCheckboxChange = (id) => {
    // Find the casual labourer object in the array by ID
    const updatedCasualLabourer = payment.map((member) =>
      member.id === id
        ? {
            ...member,
            work_days: member.work_days + 1,
          }
        : member
    );

    // Update the local state with the modified casual labourer array
    setPayment(updatedCasualLabourer);
  };
  const handleReduceCheckboxChange = (id) => {
    // Find the casual labourer object in the array by ID
    const updatedCasualLabourer = payment.map((member) =>
      member.id === id
        ? {
            ...member,
            work_days: member.work_days - 1,
          }
        : member
    );

    // Update the local state with the modified casual labourer array
    setPayment(updatedCasualLabourer);
  };

  const getProjectName = (projectData) => {
    const project = projects.find(
      (proj) => proj.id === parseInt(projectData, 10)
    );
    return project ? project.project_name : "Unknown Project";
  };

  return (
    <>
      <Sidebar>
        <div className="flex justify-center items-center m-4 flex-col">
          <input
            placeholder="Search Casual Payments by Name"
            className="input input-bordered input-success w-full max-w-xs"
            type="text"
            // SEARCHBAR: Create an onChange event which sets the query to whatever the value of the input is when it changes
            onChange={(e) => setQuery(e.target.value)}
          />
        </div>
        <div className="overflow-x-auto px-10">
          {filteredCasualLabourer.length === 0 ? (
            <div className="flex flex-col justify-center items-center w-full">
              <img
                src={_404cat}
                alt="not-found"
                className="max-w-lg lg:w-4/5"
              />
              <h1 className="text-black font-bold text-center">
                Wow, such empty! Add some payment Payments to get
                started
              </h1>
            </div>
          ) : (
            <table className="table table-zebra w-full">
              {/* head */}
              <thead>
                <tr className="text-xl font-serif">
                  <th></th>
                  <th>Add</th>
                  <th>Reduce</th>
                  <th>Project </th>
                  <th>Name</th>
                  <th>Phone</th>
                  <th>ID No</th>
                  <th>S. Date</th>
                  <th>E. Date </th>
                  <th>D.DIFF</th>
                  <th>W.Description</th>
                  <th>Q(work_days)</th>
                  <th>R.P.D</th>
                  <th>Amount</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                {filteredCasualLabourer.map((member, i) => {
                  // Find the job group document for this member
                  const total_amount = member.rate_per_day * member.work_days;
                  console.log(member.id);
                  const startDate = new Date(member.start_date);
                  const endDate = new Date(member.end_date);

                  const dateDifference = endDate.getTime() - startDate.getTime();
                  const daysDifference = Math.floor(dateDifference / (1000 * 60 * 60 * 24))
                  const daysDifferenceWithOneDayAdded = daysDifference + 1

                  return (
                    <tr key={member.id} className="font-[Poppin]">
                      <th>{i + 1}</th>
                      <td>
                        <input
                          type="checkbox"
                          checked={member.checked}
                          onChange={(e) =>
                            handleCheckboxChange(member.id, e.target.checked)
                          }
                        />
                      </td>
                      <td>
                        <input
                          type="checkbox"
                          
                          checked={member.checked}
                          onChange={(e) =>
                            handleReduceCheckboxChange(member.id, e.target.checked)
                          }
                        />
                      </td>

                      <td>{getProjectName(member.project_data)}</td>
                      <td>{member.name}</td>
                      <td>{member.phone}</td>
                      <td>{member.id_number}</td>
                      <td>{formatDate(member.start_date)}</td>
                      <td>{formatDate(member.end_date)}</td>
                      <td>{daysDifferenceWithOneDayAdded}</td>
                      <td>{member.work_description}</td>
                      <td>{member.work_days}</td>
                      <td>{member.rate_per_day}</td>
                      <td>{total_amount}</td>

                      <td>
                        <button
                          className="btn btn-warning btn-outline btn-sm mr-2"
                          onClick={() =>
                            handleUpdate(
                              member.id,
                              member.work_days,
                              total_amount
                            )
                          }
                        >
                          Update
                        </button>
                        <button
                          className="btn btn-accent btn-sm"
                          onClick={() => handleDelete(member.id)}
                        >
                          Delete
                        </button>{" "}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          )}
        </div>
      </Sidebar>
    </>
  );
};

export default UpdatePayments;
