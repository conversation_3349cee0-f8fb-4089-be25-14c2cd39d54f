const express = require("express");
const router = express.Router();

module.exports = (pool) => {
  // GET all Focus of the Week entries with staff names
router.get("/", (req, res) => {
  const query = `
    SELECT 
      fow.*, 
      u.fullnames AS staff_name,
      u.user_id
    FROM 
      focus_of_week AS fow
    INNER JOIN 
      defaultdb.users AS u ON fow.staff_id = u.user_id`;

  pool.query(query, (err, results) => {
    if (err) {
      console.error(err);
      res.status(500).json({ message: "Server Error" });
    } else {
      res.json(results);
    }
  });
});

// GET a specific Focus of the Week entry by ID with staff name
router.get("/:id", (req, res) => {
  const { id } = req.params;
  const query = `
    SELECT 
      fow.*, 
      u.fullnames AS staff_name,
      u.user_id
    FROM 
      focus_of_week AS fow
    INNER JOIN 
      defaultdb.users AS u ON fow.staff_id = u.user_id
    WHERE 
      fow.id = ?`;

  pool.query(query, [id], (err, result) => {
    if (err) {
      console.error(err);
      res.status(500).json({ message: "Server Error" });
    } else if (result.length > 0) {
      res.json(result[0]);
    } else {
      res.status(404).json({ message: "Focus of the Week not found" });
    }
  });
});

// GET all completed Focus of the Week entries with staff names
router.get("/test/completed", (req, res) => {
  const query = `
    SELECT 
      fow.*, 
      u.fullnames AS staff_name,
      u.user_id
    FROM 
      focus_of_week AS fow
    INNER JOIN 
      defaultdb.users AS u ON fow.staff_id = u.user_id
    WHERE 
      fow.status = 'completed'`;

  pool.query(query, (err, results) => {
    if (err) {
      console.error(err);
      res.status(500).json({ message: "Server Error" });
    } else {
      res.json(results);
    }
  });
});


  // CREATE a new Focus of the Week entry
  router.post("/", (req, res) => {
    const {
      start_date,
      end_date,
      title,
      description,
      expected_mib,
      expected_sales,
      custom_activity,
      actual_mib,
      actual_sales,
      variance,
      comments,
      recommendations,
      staff_id, // Include staff_id in the destructured assignment
    } = req.body;

    const status = "pending"; // Default status

    // Update the SQL INSERT statement to include staff_id
    const insertQuery = `
    INSERT INTO focus_of_week (
      start_date, end_date, title, description, 
      expected_mib, expected_sales, custom_activity, 
      variance, actual_mib, actual_sales, comments, recommendations, 
      status, staff_id) 
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

    // Include staff_id in the values array
    pool.query(
      insertQuery,
      [
        start_date,
        end_date,
        title,
        description,
        expected_mib,
        expected_sales,
        custom_activity,
        actual_mib,
        actual_sales,
        variance,
        comments,
        recommendations,
        status,
        staff_id,
      ],
      (err) => {
        if (err) {
          console.error(err);
          res.status(500).json({ message: "Server Error" });
        } else {
          res.json({ message: "Focus of the Week created successfully" });
        }
      }
    );
  });

  router.put("/:id", (req, res) => {
    const { id } = req.params;
    const { actual_mib, actual_sales, variance, comments, recommendations } =
      req.body;

    const status = "completed";

    const updateQuery =
      "UPDATE focus_of_week SET actual_mib = ?,actual_sales = ?,variance = ?, comments = ?, recommendations = ?, status = ? WHERE id = ?";
    pool.query(
      updateQuery,
      [
        actual_mib,
        actual_sales,
        variance,
        comments,
        recommendations,
        status,
        id,
      ],
      (err, result) => {
        if (err) {
          console.error(err);
          res.status(500).json({ message: "Server Error" });
        } else if (result.affectedRows > 0) {
          res.json({
            message:
              "Focus of the Week updated and marked as completed successfully",
          });
        } else {
          res.status(404).json({ message: "Focus of the Week not found" });
        }
      }
    );
  });

  router.patch("/approve/:id", (req, res) => {
    const { id } = req.params;
    const { approved_by } = req.body; // Name of the user who is approving

    if (!approved_by) {
      return res.status(400).json({ message: "Approver's name is required" });
    }

    const query =
      "UPDATE focus_of_week SET approved_by = CONCAT(IFNULL(approved_by, ''), IF(LENGTH(approved_by) > 0, ',', ''), ?) WHERE id = ?";
    pool.query(query, [approved_by, id], (err, result) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else if (result.affectedRows > 0) {
        res.json({ message: "Focus of the Week approved successfully" });
      } else {
        res.status(404).json({ message: "Focus of the Week not found" });
      }
    });
  });

  // DELETE a Focus of the Week entry by ID
  router.delete("/:id", (req, res) => {
    const { id } = req.params;
    const deleteQuery = "DELETE FROM focus_of_week WHERE id = ?";
    pool.query(deleteQuery, [id], (err, result) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else if (result.affectedRows > 0) {
        res.json({ message: "Focus of the Week deleted successfully" });
      } else {
        res.status(404).json({ message: "Focus of the Week not found" });
      }
    });
  });

  return router;
};
