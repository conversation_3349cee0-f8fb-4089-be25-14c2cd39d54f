const express = require("express");
const authenticateJWT = require("../../middleware/authenticateJWT");
const router = express.Router();

module.exports = (pool) => {
  // Get all regions
  router.get("/", async (req, res) => {
    try {
      // Query the database to get all regions
      const regions = await pool.query("SELECT * FROM region");
      res.json(regions);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: "Unable to fetch regions" });
    }
  });

  // Get one region by ID
  router.get("/:id", async (req, res) => {
    const regionId = req.params.id;

    try {
      // Query the database to get one region by ID
      const region = await pool.query("SELECT * FROM region WHERE id = ?", [
        regionId,
      ]);

      if (region.length === 0) {
        res.status(404).json({ error: "Region not found" });
      } else {
        res.json(region[0]);
      }
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: "Unable to fetch region" });
    }
  });

  // Create a new region (POST)
  router.post("/", async (req, res) => {
    const { name, regional_manager } = req.body;

    try {
      // Insert a new region into the database
      await pool.query(
        "INSERT INTO region (name, regional_manager) VALUES (?, ?)",
        [name, regional_manager]
      );
      res.status(201).json({ message: "Region created successfully" });
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: "Unable to create a region" });
    }
  });

  // Update an existing region by ID (PUT)
  router.put("/:id", async (req, res) => {
    const regionId = req.params.id;
    const { name, regional_manager } = req.body;

    try {
      // Update the region in the database
      await pool.query(
        "UPDATE region SET name = ?, regional_manager = ? WHERE id = ?",
        [name, regional_manager, regionId]
      );
      res.json({ message: "Region updated successfully" });
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: "Unable to update the region" });
    }
  });

  // Delete a region by ID (DELETE)
  router.delete("/:id", async (req, res) => {
    const regionId = req.params.id;

    try {
      // Delete the region from the database
      await pool.query("DELETE FROM region WHERE id = ?", [regionId]);
      res.json({ message: "Region deleted successfully" });
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: "Unable to delete the region" });
    }
  });

  return router;
};
