const express = require("express");
const router = express.Router();

module.exports = (pool) => {
  // GET all regions from the category table
  router.get("/", (req, res) => {
    const query = `
      SELECT * 
      FROM perf_reports.category`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        res.json(results);
      }
    });
  });

  return router;
};
