const express = require("express");
const authenticateJWT = require("../../middleware/authenticateJWT");
const router = express.Router();

module.exports = (pool) => {
  // Get all unique customers of type 'Individual' along with total_paid
  router.get("/", async (req, res) => {
    try {
      const query = `
        SELECT 
          c.customer_no, 
          c.primary_email, 
          c.phone, 
          c.customer_name,
          c.national_id, 
          c.passport_no, 
          CASE 
            WHEN c.gender IS NULL OR c.gender = 'Company' THEN 'N/A'
            ELSE c.gender
          END AS gender,
          UPPER(c.country_of_residence) AS country_of_residence,
          IFNULL(SUM(lf.total_paid), 0) AS total_paid
        FROM CRM.customer c
        LEFT JOIN CRM.lead_files lf 
          ON c.customer_no = lf.customer_id 
        WHERE 
          c.customer_type = 'Individual' 
          AND c.customer_name IS NOT NULL 
          AND (c.gender IS NOT NULL AND c.gender != 'Company') 
          AND (c.country_of_residence IS NOT NULL)
        GROUP BY 
          c.customer_no, 
          c.primary_email, 
          c.phone, 
          c.customer_name,
          c.national_id, 
          c.passport_no, 
          c.gender,
          c.country_of_residence
        ORDER BY 
          c.customer_name ASC
      `;

      pool.query(query, (err, results) => {
        if (err) {
          console.error("Error executing query:", err);
          return res.status(500).json({
            message: "An error occurred while fetching customers.",
          });
        }

        // Ensure total_paid is a number
        const formattedResults = results.map((customer) => ({
          ...customer,
          total_paid: Number(customer.total_paid) || 0,
        }));

        res.json({
          customers: formattedResults,
        });
      });
    } catch (error) {
      console.error("Error in GET / route:", error);
      res.status(500).json({
        message: "An error occurred while fetching customers.",
      });
    }
  });

  // Existing route to get a single customer by customer_id with total_paid
  router.get("/:customer_id", async (req, res) => {
    const { customer_id } = req.params;

    try {
      const query = `
        SELECT 
          c.customer_no, 
          c.primary_email, 
          c.phone, 
          c.customer_name,
          c.national_id, 
          c.passport_no, 
          CASE 
            WHEN c.gender IS NULL OR c.gender = 'Company' THEN 'N/A'
            ELSE c.gender
          END AS gender,
          UPPER(c.country_of_residence) AS country_of_residence,
          IFNULL(SUM(lf.total_paid), 0) AS total_paid
        FROM CRM.customer c
        LEFT JOIN CRM.lead_files lf 
          ON c.customer_no = lf.customer_id 
        WHERE 
          c.customer_no = ? 
          AND c.customer_type = 'Individual'
        GROUP BY 
          c.customer_no, 
          c.primary_email, 
          c.phone, 
          c.customer_name,
          c.national_id, 
          c.passport_no, 
          c.gender,
          c.country_of_residence
      `;

      pool.query(query, [customer_id], (err, results) => {
        if (err) {
          console.error("Error executing query:", err);
          return res.status(500).json({
            message: "An error occurred while fetching the customer.",
          });
        }

        if (results.length === 0) {
          return res.status(404).json({ message: "Customer not found" });
        }

        const formattedCustomer = {
          ...results[0],
          total_paid: Number(results[0].total_paid) || 0,
        };

        res.json({
          customer: formattedCustomer,
        });
      });
    } catch (error) {
      console.error("Error in GET /:customer_id route:", error);
      res.status(500).json({
        message: "An error occurred while fetching the customer.",
      });
    }
  });

  return router;
};
