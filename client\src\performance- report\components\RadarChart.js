import React, { useEffect, useState } from "react";
import {
  Radar,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Legend,
  Tooltip,
} from "recharts";

const RadarChartExample = () => {
  const [chartData, setChartData] = useState([]);

  useEffect(() => {
    // Fetch data from your backend API for multiple teams
    fetch("https://workspace.optiven.co.ke/api/chart")
      .then((response) => response.json())
      .then((data) => {
        const transformedData = Object.keys(data[0]) // Assuming data for at least one team is available
          .filter((key) => key !== "team") // Exclude 'team' key from team data
          .map((key) => ({
            metric: mapMetricName(key),
            ...data.reduce((acc, team) => {
              acc[team.team] = team[key];
              return acc;
            }, {}),
          }));
        setChartData(transformedData);
      })
      .catch((error) => {
        console.error("Error fetching data:", error);
        // Handle error state
      });
  }, []);

  const mapMetricName = (key) => {
    switch (key) {
      case "high_value_plots":
        return "High Value Plots";
      case "affordable_plots":
        return "Affordable Plots";
      case "total_sales":
        return "Total Sales";
      case "total_referrals":
        return "Total Referrals";
      case "sales_achieved":
        return "Sales Achieved";
      case "site_visits":
        return "Site Visits";
      default:
        return key;
    }
  };

  // Predefined color array to avoid clashing
  const colors = [
    "#1f77b4",
    "#ff7f0e",
    "#2ca02c",
    "#d62728",
    "#9467bd",
    "#8c564b",
    "#e377c2",
    "#7f7f7f",
    "#bcbd22",
    "#17becf",
  ];

  return (
    <div>
      {chartData.length > 0 ? (
        <RadarChart
          cx={300}
          cy={250}
          outerRadius={150}
          width={600}
          height={500}
          data={chartData}
        >
          <PolarGrid />
          <PolarAngleAxis dataKey="metric" />
          <PolarRadiusAxis angle={30} domain={[0, 100]} />

          {Object.keys(chartData[0]) // Render Radar components for each team
            .filter((key) => key !== "metric") // Exclude 'metric' key
            .map((teamKey, index) => (
              <Radar
                key={index}
                name={teamKey}
                dataKey={teamKey}
                stroke={colors[index % colors.length]}
                fill={colors[index % colors.length]}
                fillOpacity={0.6}
              />
            ))}

          <Legend />
          <Tooltip />
        </RadarChart>
      ) : (
        <p>Loading...</p>
      )}
    </div>
  );
};

export default RadarChartExample;
