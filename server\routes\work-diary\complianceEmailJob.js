// jobs/complianceEmailJob.js

const schedule = require('node-schedule');
const sendEmail = require('./emailService'); // Adjust the path as necessary
const logger = require('./logger'); // Import the logger

/**
 * Initializes the scheduled compliance email job.
 * @param {object} pool - The database connection pool.
 */
function initializeComplianceEmailJob(pool) {
  // Schedule the job to run every Friday at 6 PM
  const job = schedule.scheduleJob({ dayOfWeek: 5, hour: 18, minute: 0 }, () => {
    logger.info('Running compliance email alert job...');
    sendComplianceAlerts(pool);
  });

  logger.info('Compliance email alert job scheduled for every Friday at 6 PM.');
}

/**
 * Sends compliance alerts to non-compliant users.
 * @param {object} pool - The database connection pool.
 */
async function sendComplianceAlerts(pool) {
  try {
    // Step 1: Fetch all users
    const usersQuery = "SELECT user_id, email, fullnames FROM defaultdb.users";
    pool.query(usersQuery, async (err, users) => {
      if (err) {
        logger.error('Error fetching users:', err);
        return;
      }

      if (users.length === 0) {
        logger.info('No users found for compliance checks.');
        return;
      }

      // Step 2: Iterate through each user to calculate compliance
      for (const user of users) {
        const { user_id, email, fullnames } = user;

        // Step 3: Fetch compliance data for the current week
        const complianceQuery = `
          SELECT 
            DATE(date) AS activityDate,
            HOUR(start_time) AS activityHour
          FROM activities
          WHERE 
            employee_id = ?
            AND WEEK(date, 1) = WEEK(CURDATE(), 1)
            AND DAYOFWEEK(date) BETWEEN 2 AND 6 -- Monday=2 to Friday=6
            AND HOUR(start_time) BETWEEN 9 AND 16 -- 9 AM to 4 PM
        `;

        pool.query(complianceQuery, [user_id], async (err, results) => {
          if (err) {
            logger.error(`Error fetching compliance data for user ${user_id}:`, err);
            return;
          }

          // Define expected days and hours
          const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
          const hours = Array.from({ length: 8 }, (_, i) => 9 + i); // 9 to 16 representing 9 AM to 5 PM

          // Initialize compliance data structure
          const complianceData = daysOfWeek.map(day => ({
            day,
            missingHours: []
          }));

          // Helper function to get day name from date
          const getDayName = (date) => {
            const options = { weekday: 'long' };
            return new Date(date).toLocaleDateString('en-US', options);
          };

          // Populate complianceData with missing hours
          daysOfWeek.forEach((day, dayIndex) => {
            hours.forEach(hour => {
              // Check if activity exists for the day and hour
              const activityExists = results.some(activity => {
                const activityDay = getDayName(activity.activityDate);
                return activityDay === day && activity.activityHour === hour;
              });

              if (!activityExists) {
                complianceData[dayIndex].missingHours.push(`${hour}:00 - ${hour + 1}:00`);
              }
            });
          });

          // Calculate compliance rate
          const totalExpectedHoursPerDay = 8; // 9 AM to 5 PM
          const totalDays = 5; // Monday to Friday
          const totalExpectedHours = totalExpectedHoursPerDay * totalDays; // 40 hours

          const totalMissingHours = complianceData.reduce((acc, day) => acc + day.missingHours.length, 0);
          const completedHours = totalExpectedHours - totalMissingHours;
          const complianceRate = Math.round((completedHours / totalExpectedHours) * 100);

          // Determine if user is non-compliant based on a threshold, e.g., < 90%
          const complianceThreshold = 90;
          if (complianceRate < complianceThreshold) {
            // Send email alert
            const subject = "Compliance Alert: Action Required";
            const text = `
Dear ${fullnames},

Our records indicate that your compliance rate this week is ${complianceRate}%. Please address the missing activities to ensure full compliance.

If you have any questions or need assistance, feel free to reach out to your supervisor.

Best regards,
Optiven Work Plan
            `;

            try {
              await sendEmail(email, subject, text);
              logger.info(`Compliance alert sent to ${email}`);
            } catch (emailErr) {
              logger.error(`Failed to send email to ${email}:`, emailErr);
            }
          }
        });
      }
    });
  } catch (error) {
    logger.error('Error in sendComplianceAlerts:', error);
  }
}

module.exports = initializeComplianceEmailJob;
