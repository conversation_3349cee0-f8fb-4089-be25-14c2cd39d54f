import React from "react";
import "./Sidebar.css";
import { Link } from "react-router-dom";
import { useSelector } from "react-redux";

const Sidebar = ({ children }) => {
  const accessRole = useSelector((state) => state.user.accessRole).trim();

  const department = useSelector((state) => state.user.user.department);

  const accessRoles = accessRole.split("#");

  const isCustomerExp = accessRoles.includes("isCustomerExp");
  const conferenceSuperuser = accessRoles.includes("conferenceSuperuser");
  const conferenceUsers = accessRoles.includes("conferenceUsers");
  const isVisitorsManagementHR = accessRoles.includes("visitorsManagementHR");

  return (
    <>
      <div className="drawer">
        <input id="my-drawer" type="checkbox" className="drawer-toggle" />
        <div className="drawer-content overflow-visible">{children}</div>
        <div className="drawer-side">
          <label htmlFor="my-drawer" className="drawer-overlay"></label>
          <ul className="menu p-4 w-80 bg-base-100 text-base-content">
            {/* Home */}
            <li>
              <Link to="/visitors-management" className="font-bold my-1">
                Home
              </Link>
            </li>

            {/* Visitors */}
            {(isCustomerExp || department === "ICT SYSTEMS") && (
              <div className="collapse collapse-arrow border border-base-300 bg-base-100 rounded-box my-1">
                <input type="checkbox" className="peer" />
                <div className="collapse-title font-bold">Visitors</div>
                <div className="collapse-content -mt-5 flex flex-col menu bg-base-100">
                  <Link
                    className="font-sans mt-1 hover:bg-base-200 rounded p-2"
                    to="/register-visitor"
                  >
                    Register Visitor
                  </Link>
                  <Link
                    className="font-sans mt-1 hover:bg-base-200 rounded p-2"
                    to="/view-visitors"
                  >
                    View Visitors
                  </Link>
                </div>
              </div>
            )}

            {/* Interviews */}
            {(isCustomerExp ||
              isVisitorsManagementHR ||
              department === "ICT SYSTEMS") && (
              <div className="collapse collapse-arrow border border-base-300 bg-base-100 rounded-box my-1">
                <input type="checkbox" className="peer" />
                <div className="collapse-title font-bold">Interviews</div>
                <div className="collapse-content -mt-5 flex flex-col menu bg-base-100">
                  {(isVisitorsManagementHR || department === "ICT SYSTEMS") && (
                    <Link
                      className="font-sans mt-1 hover:bg-base-200 rounded p-2"
                      to="/schedule-interview"
                    >
                      Schedule Interview
                    </Link>
                  )}
                  <Link
                    className="font-sans mt-1 hover:bg-base-200 rounded p-2"
                    to="/view-interviews"
                  >
                    View Interviews
                  </Link>
                </div>
              </div>
            )}

            {/* Reports */}
            {(isCustomerExp ||
              isVisitorsManagementHR ||
              department === "ICT SYSTEMS") && (
              <div className="collapse collapse-arrow border border-base-300 bg-base-100 rounded-box my-1">
                <input type="checkbox" className="peer" />
                <div className="collapse-title font-bold">Reports</div>
                <div className="collapse-content -mt-5 flex flex-col menu bg-base-100">
                  <Link
                    className="font-sans mt-1 hover:bg-base-200 rounded p-2"
                    to="/visitors-reports"
                  >
                    Visitors Reports
                  </Link>
                  {(isVisitorsManagementHR || department === "ICT SYSTEMS") && (
                    <Link
                      className="font-sans mt-1 hover:bg-base-200 rounded p-2"
                      to="/interviews-reports"
                    >
                      Interview Reports
                    </Link>
                  )}
                </div>
              </div>
            )}

            {/* Parking */}
            {(isCustomerExp || department === "ICT SYSTEMS") && (
              <div className="collapse collapse-arrow border border-base-300 bg-base-100 rounded-box my-1">
                <input type="checkbox" className="peer" />
                <div className="collapse-title font-bold">Parking</div>
                <div className="collapse-content -mt-5 flex flex-col menu bg-base-100">
                  <Link
                    className="font-sans mt-1 hover:bg-base-200 rounded p-2"
                    to="/reserve-parking"
                  >
                    Reserve Parking
                  </Link>
                  <Link
                    className="font-sans mt-1 hover:bg-base-200 rounded p-2"
                    to="/reserved-parking"
                  >
                    View Reserved Parking
                  </Link>
                </div>
              </div>
            )}

            {(conferenceUsers ||
              conferenceSuperuser ||
              department === "ICT SYSTEMS") && (
              <div className="collapse collapse-arrow border border-base-300 bg-base-100 rounded-box my-1">
                <input type="checkbox" className="peer" />
                <div className="collapse-title font-bold">Conference</div>
                <div className="collapse-content -mt-5 flex flex-col menu bg-base-100">
                  <Link
                    to="/booking"
                    className="font-sans mt-1 hover:bg-base-200 rounded p-2"
                  >
                    Create Booking
                  </Link>

                  <Link
                    to="/all-rooms"
                    className="font-sans mt-1 hover:bg-base-200 rounded p-2"
                  >
                    View Conference Schedule
                  </Link>
                  {(conferenceSuperuser ||
              department === "ICT SYSTEMS") && (
                  <Link
                    to="/event-booking"
                    className="font-sans mt-1 hover:bg-base-200 rounded p-2"
                  >
                    Event Booking
                  </Link>
                  )}
                </div>
              </div>
            )}
          </ul>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
