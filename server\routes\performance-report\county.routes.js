const express = require("express");
const router = express.Router();
const pdfMake = require("pdfmake/build/pdfmake");
const vfsFonts = require("pdfmake/build/vfs_fonts");

// Register fonts
pdfMake.vfs = vfsFonts.pdfMake.vfs;

module.exports = (pool) => {
  // GET all counties
  router.get("/", (req, res) => {
    const query = `
    SELECT * 
    FROM counties`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        res.json(results);
      }
    });
  });

  // GET counties for Region 1
  router.get("/region1", (req, res) => {
    const regions = ["LEOPARDS", "LIONS", "HQ PLATINUM", "TIGERS"];
    const regionQuery = regions.map((region) => `'${region}'`).join(", ");

    const query = `
    SELECT * 
    FROM counties
    WHERE region IN (${regionQuery})`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        res.json(results);
      }
    });
  });

  // GET counties for Region 2
  router.get("/region2", (req, res) => {
    const regions = [
      "PUMA GREEN",
      "PUMA YELLOW",
      "JAGUAR GREEN",
      "JAGUAR YELLOW",
      "GLOBAL PLATINUM",
    ];
    const regionQuery = regions.map((region) => `'${region}'`).join(", ");

    const query = `
    SELECT * 
    FROM counties
    WHERE region IN (${regionQuery})`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        res.json(results);
      }
    });
  });

  // GET a specific county by ID
  router.get("/:id", (req, res) => {
    const { id } = req.params;
    const query = `
      SELECT * 
      FROM counties 
      WHERE id = ?`;

    pool.query(query, [id], (err, result) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else if (result.length > 0) {
        res.json(result[0]);
      } else {
        res.status(404).json({ message: "County not found" });
      }
    });
  });

  // CREATE new counties
  router.post("/", (req, res) => {
    const counties = Array.isArray(req.body) ? req.body : [req.body];

    // Check if counties is an array and it's not empty
    if (!counties.length) {
      return res.status(400).json({ message: "Invalid county data" });
    }

    const query =
      "INSERT INTO counties (startDate, endDate, region, countyActivated, contactsObtained, siteVisits, salesAchieved, referrals, onQueue) VALUES (?,?,?, ?, ?, ?, ?, ?, ?)";

    // Array to store the results of each INSERT query
    const results = [];

    counties.forEach((county) => {
      const {
        startDate,
        endDate,
        region,
        countyActivated,
        contactsObtained,
        siteVisits,
        salesAchieved,
        referrals,
        onQueue,
      } = county;

      pool.query(
        query,
        [
          startDate,
          endDate,
          region,
          countyActivated,
          contactsObtained,
          siteVisits,
          salesAchieved,
          referrals,
          onQueue,
        ],
        (err, result) => {
          if (err) {
            console.error(err);
            results.push({
              error: true,
              message: "Error creating county",
            });
          } else {
            results.push({
              error: false,
              message: "County created successfully",
            });
          }

          // Check if all queries have completed
          if (results.length === counties.length) {
            // Check if any errors occurred during the queries
            const hasErrors = results.some((result) => result.error);
            if (hasErrors) {
              res.status(500).json({ message: "Server Error" });
            } else {
              res.json({ message: "Counties created successfully" });
            }
          }
        }
      );
    });
  });

  // UPDATE an existing county by ID
  router.patch("/:id", (req, res) => {
    const { id } = req.params;
    const {
      region,
      countyActivated,
      contactsObtained,
      siteVisits,
      salesAchieved,
      referrals,
      onQueue,
    } = req.body;

    const query = `
      UPDATE counties 
      SET region = ?, countyActivated = ?, contactsObtained = ?, siteVisits = ?, salesAchieved = ?, referrals = ?, onQueue = ?
      WHERE id = ?`;

    pool.query(
      query,
      [
        region,
        countyActivated,
        contactsObtained,
        siteVisits,
        salesAchieved,
        referrals,
        onQueue,
        id,
      ],
      (err, result) => {
        if (err) {
          console.error(err);
          res.status(500).json({ message: "Server Error" });
        } else if (result.affectedRows > 0) {
          res.json({ message: "County updated successfully" });
        } else {
          res.status(404).json({ message: "County not found" });
        }
      }
    );
  });

  // DELETE a county by ID
  router.delete("/:id", (req, res) => {
    const { id } = req.params;
    const query = `
      DELETE FROM counties 
      WHERE id = ?`;

    pool.query(query, [id], (err, result) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else if (result.affectedRows > 0) {
        res.json({ message: "County deleted successfully" });
      } else {
        res.status(404).json({ message: "County not found" });
      }
    });
  });

  // NEW: Generate a PDF report for multiple tables using pdfMake
  router.get("/download-pdf/multi-table-report", async (req, res) => {
    try {
      const { startDate, endDate } = req.query;
      // Check if startDate and endDate are defined
      if (!startDate || !endDate) {
        return res
          .status(400)
          .json({ message: "Missing startDate or endDate parameters" });
      }

      // Fetch data from the database for each table with date range
      const queryCounties = `SELECT * FROM counties WHERE startDate >= '${startDate}' AND endDate <= '${endDate}'`;
      // Use Promise.all to execute all queries concurrently
      const [resultCounties] =
        await Promise.all([
          executeQuery(queryCounties, [startDate, endDate]),
        ]);
      // Create a definition for the tables with zebra stripe pattern
      const docDefinition = {
        pageSize: "A4",
        pageOrientation: "landscape",
        content: [
          {
            text: "Activations Report",
            color: "black",
            style: "header",
            decoration: "underline",
          },
          
          {
            table: {
              headerRows: 1,
              widths: ["auto", "auto", "auto", "auto", "auto", "auto", "auto"], // Adjust the widths as needed
              body: [
                [
                  {
                    text: "Region",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "County Activated",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Contacts Obtained",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Site Visits",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Sales Achieved",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Referrals",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "On Queue",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                ],
                ...resultCounties.map((record, index) => [
                  {
                    text: record.region,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.countyActivated,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.contactsObtained,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.siteVisits,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.salesAchieved,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.referrals,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.onQueue,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                ]),
              ],
              // Center the table
              alignment: "center",
            },
          },
          
        ],
        styles: {
          header: {
            fontSize: 20,
            alignment: "center",
            margin: [0, 0, 0, 20],
            bold: true,
            fillColor: "#202A44",
            color: "white",
          },
          tableHeader: {
            bold: true,
            fontSize: 13,
            color: "white",
          },
          tableCell: {
            fontSize: 9,
            margin: [0, 5],
          },
        },
      };

      // Create the PDF document
      const pdfDoc = pdfMake.createPdf(docDefinition);

      // Set response headers
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        'attachment; filename="multi_table_report.pdf"'
      );

      // Pipe the PDF content to the response
      pdfDoc.getBuffer((buffer) => {
        res.end(buffer);
      });
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Server Error" });
    }
  });

  // Download PDF report for HOS
  router.get("/download-pdf/multi-table-report-hos", async (req, res) => {
    try {
      const { startDate, endDate } = req.query;
      // Check if startDate and endDate are defined
      if (!startDate || !endDate) {
        return res
          .status(400)
          .json({ message: "Missing startDate or endDate parameters" });
      }
      // Fetch data from the database for HOS teams
      const queryCounties = `
      SELECT *
      FROM counties
      WHERE startDate >= '${startDate}' AND endDate <= '${endDate}'
      AND region IN ('LEOPARDS', 'LIONS', 'HQ PLATINUM', 'TIGERS')
    `;

      
      // ... (similar queries for hotels and industries)

      const [resultCounties] =
        await Promise.all([
          executeQuery(queryCounties),
        ]);

      // Create a definition for the tables with zebra stripe pattern
      const docDefinition = {
        pageSize: "A4",
        pageOrientation: "landscape",
        content: [
          {
            text: "Activations Report",
            color: "black",
            style: "header",
            decoration: "underline",
          },
          { text: "\nCounties Table\n\n", color: "black", style: "header" },
          {
            table: {
              headerRows: 1,
              widths: ["auto", "auto", "auto", "auto", "auto", "auto", "auto"], // Adjust the widths as needed
              body: [
                [
                  {
                    text: "Region",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "County Activated",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Contacts Obtained",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Site Visits",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Sales Achieved",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Referrals",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "On Queue",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                ],
                ...resultCounties.map((record, index) => [
                  {
                    text: record.region,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.countyActivated,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.contactsObtained,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.siteVisits,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.salesAchieved,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.referrals,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.onQueue,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                ]),
              ],
              // Center the table
              alignment: "center",
            },
          },
          
        ],
        styles: {
          header: {
            fontSize: 20,
            alignment: "center",
            margin: [0, 0, 0, 20],
            bold: true,
            fillColor: "#202A44",
            color: "white",
          },
          tableHeader: {
            bold: true,
            fontSize: 13,
            color: "white",
          },
          tableCell: {
            fontSize: 9,
            margin: [0, 5],
          },
        },
      };

      // Create the PDF document
      const pdfDoc = pdfMake.createPdf(docDefinition);

      // Set response headers
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        'attachment; filename="multi_table_report.pdf"'
      );

      // Pipe the PDF content to the response
      pdfDoc.getBuffer((buffer) => {
        res.end(buffer);
      });
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Server Error" });
    }
  });

  // Download PDF report for GM
  router.get("/download-pdf/multi-table-report-gm", async (req, res) => {
    try {
      const { startDate, endDate } = req.query;
      // Check if startDate and endDate are defined
      if (!startDate || !endDate) {
        return res
          .status(400)
          .json({ message: "Missing startDate or endDate parameters" });
      }
      // Fetch data from the database for GM teams
      const queryCounties = `
      SELECT *
      FROM counties
      WHERE startDate >= '${startDate}' AND endDate <= '${endDate}'
      AND region IN ('PUMA GREEN', 'PUMA YELLOW', 'JAGUAR GREEN', 'JAGUAR YELLOW', 'GLOBAL PLATINUM')
    `;

     

      const [resultCounties] =
        await Promise.all([
          executeQuery(queryCounties),
        ]);

      // Create a definition for the tables with zebra stripe pattern
      const docDefinition = {
        pageSize: "A4",
        pageOrientation: "landscape",
        content: [
          {
            text: "Activations Report",
            color: "black",
            style: "header",
            decoration: "underline",
          },
          { text: "\nCounties Table\n\n", color: "black", style: "header" },
          {
            table: {
              headerRows: 1,
              widths: ["auto", "auto", "auto", "auto", "auto", "auto", "auto"], // Adjust the widths as needed
              body: [
                [
                  {
                    text: "Region",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "County Activated",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Contacts Obtained",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Site Visits",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Sales Achieved",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "Referrals",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                  {
                    text: "On Queue",
                    bold: true,
                    fillColor: "#202A44",
                    color: "white",
                  },
                ],
                ...resultCounties.map((record, index) => [
                  {
                    text: record.region,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.countyActivated,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.contactsObtained,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.siteVisits,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.salesAchieved,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.referrals,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.onQueue,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                ]),
              ],
              // Center the table
              alignment: "center",
            },
          },
          
        ],
        styles: {
          header: {
            fontSize: 20,
            alignment: "center",
            margin: [0, 0, 0, 20],
            bold: true,
            fillColor: "#202A44",
            color: "white",
          },
          tableHeader: {
            bold: true,
            fontSize: 13,
            color: "white",
          },
          tableCell: {
            fontSize: 9,
            margin: [0, 5],
          },
        },
      };

      // Create the PDF document
      const pdfDoc = pdfMake.createPdf(docDefinition);

      // Set response headers
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        'attachment; filename="multi_table_report.pdf"'
      );

      // Pipe the PDF content to the response
      pdfDoc.getBuffer((buffer) => {
        res.end(buffer);
      });
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Server Error" });
    }
  });

  // Function to execute a database query and return a promise
  function executeQuery(query) {
    return new Promise((resolve, reject) => {
      pool.query(query, (err, result) => {
        if (err) {
          console.error(err);
          reject(err);
        } else {
          resolve(result);
        }
      });
    });
  }

  return router;
};
