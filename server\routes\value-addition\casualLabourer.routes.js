const express = require("express");
const authenticateJWT = require("../../middleware/authenticateJWT");
require("dotenv").config();
const util = require("util");
const router = express.Router();



module.exports = (pool) => {
  // Get all Casual Labourers
 
  router.get("/",authenticateJWT,async (req, res) => {
    try {
      pool.query(
        "SELECT cl.*, l.rate_per_day FROM casual_labourer cl JOIN labour l ON cl.labour_id = l.id WHERE status = 'active' ORDER BY name ASC",
        (err, results) => {
          if (err) throw err;

          res.json(results);
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching casual-labourers.",
      });
    }
  });
// route to get data where end date has reached 
router.get("/casual-labourer-end-date",authenticateJWT, (req, res) => {
  try {
    pool.query("SELECT cl.*, l.rate_per_day FROM casual_labourer cl JOIN labour l ON cl.labour_id = l.id WHERE end_date <= CURDATE()", (err, result) => {
      if (err) throw err;
      res.status(200).json(result);
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "An error occurred while fetching casual laborers." });
  }
});

  

  // Get a specific Casual Labourer by ID
  router.get("/:id", authenticateJWT,async (req, res) => {
    try {
      const casualLabourer = await pool
        .promise()
        .query("SELECT * FROM casual_labourer WHERE id = ?", [req.params.id]);

      if (casualLabourer.length === 0) {
        return res.status(404).json({ message: "Casual labourer not found" });
      }

      res.json(casualLabourer[0][0]);
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });
  // route to get casual labourers who have finished three months from  their start date and status set to inactive
  router.get("/inactive-casual-labourers", authenticateJWT,async (req, res) => {
    try {
        // Fetch inactive casual laborers
        pool.query("SELECT * FROM Casual_labourer WHERE status = 'inactive'", (err, result) => {
            if (err) {
                console.error(err);
                return res.status(500).json({ message: "Error fetching inactive casual laborers" });
            }
            res.status(200).json(result);
            console.log(result)
        });
    } catch (error) {
        console.error("Error:", error);
        res.status(500).json({ message: "Internal server error" });
    }
});


  // Create a new Casual Labourer
  router.post("/", async (req, res) => {
    const { name, phone, id_number, labour_id,start_date,end_date,work_description,project_id,status } =
      req.body;

    try {
      const result = await pool
  .promise()
  .query(
    "INSERT INTO casual_labourer (name, phone, id_number, labour_id, start_date, end_date, work_description, project_id, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
    [name, phone, id_number, labour_id, start_date, end_date, work_description, project_id, 'active']
  );


      const newLabourer = {
        id: result.insertId,
        name,
        phone,
        id_number,
        labour_id,
        start_date,
        end_date,
        work_description,
        project_id,
        status: 'active'

      };
      res.json(newLabourer);
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });

  // Update a Casual Labourer by ID
  router.patch("/:id",async (req, res) => {
    const { name, phone, id_number, labour_id,start_date,end_date,work_description,project_id } =
      req.body;

    try {
      const result = await pool
        .promise()
        .query(
          "UPDATE casual_labourer SET name=?, phone=?, id_number=?,labour_id=?,start_date=?,end_date=?,work_description=?,project_id=? WHERE id=?",
          [
            name,
            phone,
            id_number,
            labour_id,
            start_date,end_date,
            work_description,
            project_id,
            req.params.id,
          ]
        );

      if (result.affectedRows === 0) {
        return res.status(404).json({
          message: "The casual labourer with the given ID does not exist",
        });
      }

      res.json({ message: "Casual labourer updated successfully" });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });
  

// update casual labourer status
router.patch("/:id/status",authenticateJWT, (req, res) => {
  const { id } = req.params;
  const { status } = req.body;
  try {
    pool.query("UPDATE casual_labourer SET status = ? WHERE id = ?", [status, id], (error, results) => {
      if (error) {
        console.error("Error updating status:", error);
        res.status(500).json({ message: "Internal server error" });
      } else {
        res.status(200).json({ message: "Status Updated Successfully" });
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Internal server error" });
  }
});


  // Delete a Casual Labourer by Id
  router.delete("/:id", authenticateJWT,async (req, res) => {
    try {
      const result = await pool
        .promise()
        .query("DELETE FROM casual_labourer WHERE id=?", [req.params.id]);

      if (result.affectedRows === 0) {
        return res.status(404).json({
          message: "The casual labourer with the given ID does not exist",
        });
      }

      res.json({ message: "Casual labourer deleted successfully" });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });

  return router;
};
