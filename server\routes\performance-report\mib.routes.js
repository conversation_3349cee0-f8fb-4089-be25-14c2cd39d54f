const express = require("express");
const router = express.Router();

module.exports = (pool) => {
  // GET MIB data for the current month for all offices
  router.get("/", (req, res) => {
    // Get the current month and year in local time zone
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = currentDate.getFullYear();
    // Fetch data from the database for the current month and year in local time zone
    const query = `
      SELECT REPLACE(Amount_LCY, ',', '') as Amount, Type, Transaction_type, Pay_mode
    FROM Recipts
    WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) 
  AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE)
      AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')
      AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
      AND Pay_mode IS NOT NULL`;

    pool.query(query, [currentMonth, currentYear], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        // Separate the results into Posted and Cancelled amounts
        const postedAmount = results
          .filter((row) => row.Type === "Posted" && row.Amount !== null)
          .reduce((sum, row) => {
            const amount = parseFloat(row.Amount);
            if (!isNaN(amount)) {
              return sum + amount;
            } else {
              console.error(
                `Invalid amount value for row: ${JSON.stringify(row)}`
              );
              // Handle the error as needed, e.g., skip this row
              return sum;
            }
          }, 0);

        const cancelledAmount = results
          .filter((row) => row.Type === "Cancelled" && row.Amount !== null)
          .reduce((sum, row) => {
            const amount = parseFloat(row.Amount);
            if (!isNaN(amount)) {
              return sum + amount;
            } else {
              console.error(
                `Invalid amount value for row: ${JSON.stringify(row)}`
              );
              // Handle the error as needed, e.g., skip this row
              return sum;
            }
          }, 0);

        // Calculate the current MIB
        const currentMIB = postedAmount - cancelledAmount;

        // Prepare the response in the desired format
        const responseData = {
          MIB: {
            total: currentMIB.toFixed(2),
          },
        };

        // Send the response to the frontend
        res.json(responseData);
      }
    });
  });

  // GET MIB data for the previous month
  router.get("/previous", (req, res) => {
    // Get the current month and year in local time zone
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = currentDate.getFullYear();

    // Calculate the previous month and year
    const previousMonth = currentMonth - 1 === 0 ? 12 : currentMonth - 1;
    const previousYear = currentMonth - 1 === 0 ? currentYear - 1 : currentYear;

    // Fetch data from the database for the previous month and year in local time zone
    const query = `
    SELECT REPLACE(Amount_LCY, ',', '') as Amount, Type, Transaction_type
    FROM Recipts
    WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) 
  AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE)
  AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
        AND Pay_mode IS NOT NULL
      AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')`;

    pool.query(query, [previousMonth, previousYear], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        // Separate the results into Posted and Cancelled amounts
        const postedAmount = results
          .filter((row) => row.Type === "Posted" && row.Amount !== null)
          .reduce((sum, row) => {
            const amount = parseFloat(row.Amount);
            if (!isNaN(amount)) {
              return sum + amount;
            } else {
              console.error(
                `Invalid amount value for row: ${JSON.stringify(row)}`
              );
              // Handle the error as needed, e.g., skip this row
              return sum;
            }
          }, 0);

        const cancelledAmount = results
          .filter((row) => row.Type === "Cancelled" && row.Amount !== null)
          .reduce((sum, row) => {
            const amount = parseFloat(row.Amount);
            if (!isNaN(amount)) {
              return sum + amount;
            } else {
              console.error(
                `Invalid amount value for row: ${JSON.stringify(row)}`
              );
              // Handle the error as needed, e.g., skip this row
              return sum;
            }
          }, 0);

        // Calculate the previous month's MIB
        const previousMonthMIB = postedAmount - cancelledAmount;

        // Prepare the response in the desired format
        const responseData = {
          MIB: {
            total: previousMonthMIB.toFixed(2),
          },
        };

        // Send the response to the frontend
        res.json(responseData);
      }
    });
  });

  // New route to count total sales in the current month
  router.get("/total-sales", (req, res) => {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();

    const query = `
      SELECT Type, COUNT(*) as count
      FROM Recipts
      WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) 
  AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE)
  AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
        AND Pay_mode IS NOT NULL
        AND Transaction_type IN ('Purchase Price', 'Deposit')
      GROUP BY Type`;

    pool.query(query, [currentMonth, currentYear], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        let postedCount = 0;
        let cancelledCount = 0;

        results.forEach((row) => {
          if (row.Type === "Posted") {
            postedCount = row.count;
          } else if (row.Type === "Cancelled") {
            cancelledCount = row.count;
          }
        });

        const totalSales = postedCount - cancelledCount;

        const responseData = {
          totalSales,
        };

        res.json(responseData);
      }
    });
  });

  // New route to count previous month's total sales
  router.get("/total-sales/previous", (req, res) => {
    const currentDate = new Date();
    const firstDayOfCurrentMonth = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      1
    );
    const firstDayOfPreviousMonth = new Date(firstDayOfCurrentMonth);
    firstDayOfPreviousMonth.setMonth(firstDayOfPreviousMonth.getMonth() - 1);
    const lastDayOfPreviousMonth = new Date(firstDayOfCurrentMonth);
    lastDayOfPreviousMonth.setDate(firstDayOfPreviousMonth.getDate() - 1);

    const query = `
      SELECT Type, COUNT(*) as count
      FROM Recipts
      WHERE STR_TO_DATE(Payment_date, '%m/%d/%y') BETWEEN ? AND ?
        AND Transaction_type IN ('Purchase Price', 'Deposit')
        AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
        AND Pay_mode IS NOT NULL
      GROUP BY Type`;

    pool.query(
      query,
      [firstDayOfPreviousMonth, lastDayOfPreviousMonth],
      (err, results) => {
        if (err) {
          console.error(err);
          res.status(500).json({ message: "Server Error" });
        } else {
          let postedCount = 0;
          let cancelledCount = 0;

          results.forEach((row) => {
            if (row.Type === "Posted") {
              postedCount = row.count;
            } else if (row.Type === "Cancelled") {
              cancelledCount = row.count;
            }
          });

          const totalSales = postedCount - cancelledCount;

          const responseData = {
            totalSales,
          };

          res.json(responseData);
        }
      }
    );
  });

  // Other routes for updating, creating, deleting, etc. can be added similarly

  // GET MIB data for the current month based on office region
  router.get("/absa", (req, res) => {
    //const officeRegion = req.query.officeRegion;
    // Get the current month and year in local time zone
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = currentDate.getFullYear();
    // Fetch data from the database for the current month and year in local time zone
    const query = `
      SELECT REPLACE(Amount_LCY, ',', '') as Amount, Type, Transaction_type
      FROM Recipts
      WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) 
  AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE)
  AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
        AND Pay_mode IS NOT NULL
        AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')
        AND Regions IN ('LEOPARDS', 'LIONS', 'HQ P', 'TIGERS', 'STAFF', 'HQ BLUE', 'HQ PROSPEROUS')`;

    pool.query(query, [currentMonth, currentYear], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        // Separate the results into Posted and Cancelled amounts
        const postedAmount = results
          .filter((row) => row.Type === "Posted" && row.Amount !== null)
          .reduce((sum, row) => {
            const amount = parseFloat(row.Amount);
            if (!isNaN(amount)) {
              return sum + amount;
            } else {
              console.error(
                `Invalid amount value for row: ${JSON.stringify(row)}`
              );
              // Handle the error as needed, e.g., skip this row
              return sum;
            }
          }, 0);

        const cancelledAmount = results
          .filter((row) => row.Type === "Cancelled" && row.Amount !== null)
          .reduce((sum, row) => {
            const amount = parseFloat(row.Amount);
            if (!isNaN(amount)) {
              return sum + amount;
            } else {
              console.error(
                `Invalid amount value for row: ${JSON.stringify(row)}`
              );
              // Handle the error as needed, e.g., skip this row
              return sum;
            }
          }, 0);

        // Calculate the current MIB
        const currentMIB = postedAmount - cancelledAmount;

        // Prepare the response in the desired format
        const responseData = {
          MIB: {
            total: currentMIB.toFixed(2),
          },
        };

        // Send the response to the frontend
        res.json(responseData);
      }
    });
  });

  // GET MIB data for the previous month based on office region
  router.get("/previous/absa", (req, res) => {
    //const officeRegion = req.query.officeRegion;
    // Get the current month and year in local time zone
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = currentDate.getFullYear();

    // Calculate the previous month and year
    const previousMonth = currentMonth - 1 === 0 ? 12 : currentMonth - 1;
    const previousYear = currentMonth - 1 === 0 ? currentYear - 1 : currentYear;

    // Fetch data from the database for the previous month and year in local time zone
    const query = `
    SELECT REPLACE(Amount_LCY, ',', '') as Amount, Type, Transaction_type
    FROM Recipts
    WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) 
  AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE)
  AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
        AND Pay_mode IS NOT NULL
      AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')
      AND Regions IN ('LEOPARDS', 'LIONS', 'HQ P', 'TIGERS', 'STAFF', 'HQ BLUE', 'HQ PROSPEROUS')`;

    pool.query(query, [previousMonth, previousYear], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        // Separate the results into Posted and Cancelled amounts
        const postedAmount = results
          .filter((row) => row.Type === "Posted" && row.Amount !== null)
          .reduce((sum, row) => {
            const amount = parseFloat(row.Amount);
            if (!isNaN(amount)) {
              return sum + amount;
            } else {
              console.error(
                `Invalid amount value for row: ${JSON.stringify(row)}`
              );
              // Handle the error as needed, e.g., skip this row
              return sum;
            }
          }, 0);

        const cancelledAmount = results
          .filter((row) => row.Type === "Cancelled" && row.Amount !== null)
          .reduce((sum, row) => {
            const amount = parseFloat(row.Amount);
            if (!isNaN(amount)) {
              return sum + amount;
            } else {
              console.error(
                `Invalid amount value for row: ${JSON.stringify(row)}`
              );
              // Handle the error as needed, e.g., skip this row
              return sum;
            }
          }, 0);

        // Calculate the previous month's MIB
        const previousMonthMIB = postedAmount - cancelledAmount;

        // Prepare the response in the desired format
        const responseData = {
          MIB: {
            total: previousMonthMIB.toFixed(2),
          },
        };

        // Send the response to the frontend
        res.json(responseData);
      }
    });
  });

  // GET MIB data for the current month for PUMA, JAGUAR, REGION KAREN
  router.get("/karen", (req, res) => {
    // Get the current month and year in local time zone
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = currentDate.getFullYear();
    // Fetch data from the database for the current month and year in local time zone
    const query = `
    SELECT REPLACE(Amount_LCY, ',', '') as Amount, Type, Transaction_type
    FROM Recipts
    WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) 
  AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE)
  AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
        AND Pay_mode IS NOT NULL
      AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')
      AND Regions IN ('PUMA', 'JAGUAR', 'REGION KAREN', 'KAREN BLUE')`;

    pool.query(query, [currentMonth, currentYear], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        // Separate the results into Posted and Cancelled amounts
        const postedAmount = results
          .filter((row) => row.Type === "Posted" && row.Amount !== null)
          .reduce((sum, row) => {
            const amount = parseFloat(row.Amount);
            if (!isNaN(amount)) {
              return sum + amount;
            } else {
              console.error(
                `Invalid amount value for row: ${JSON.stringify(row)}`
              );
              // Handle the error as needed, e.g., skip this row
              return sum;
            }
          }, 0);

        const cancelledAmount = results
          .filter((row) => row.Type === "Cancelled" && row.Amount !== null)
          .reduce((sum, row) => {
            const amount = parseFloat(row.Amount);
            if (!isNaN(amount)) {
              return sum + amount;
            } else {
              console.error(
                `Invalid amount value for row: ${JSON.stringify(row)}`
              );
              // Handle the error as needed, e.g., skip this row
              return sum;
            }
          }, 0);

        // Calculate the current MIB
        const currentMIB = postedAmount - cancelledAmount;

        // Prepare the response in the desired format
        const responseData = {
          MIB: {
            total: currentMIB.toFixed(2),
          },
        };

        // Send the response to the frontend
        res.json(responseData);
      }
    });
  });

  // GET MIB data for the previous month for PUMA, JAGUAR, REGION KAREN
  router.get("/previous/karen", (req, res) => {
    // Get the current month and year in local time zone
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = currentDate.getFullYear();

    // Calculate the previous month and year
    const previousMonth = currentMonth - 1 === 0 ? 12 : currentMonth - 1;
    const previousYear = currentMonth - 1 === 0 ? currentYear - 1 : currentYear;

    // Fetch data from the database for the previous month and year in local time zone
    const query = `
  SELECT REPLACE(Amount_LCY, ',', '') as Amount, Type, Transaction_type
  FROM Recipts
  WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) 
  AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE)
  AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
        AND Pay_mode IS NOT NULL
    AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')
    AND Regions IN ('PUMA', 'JAGUAR', 'REGION KAREN', 'KAREN BLUE')`;

    pool.query(query, [previousMonth, previousYear], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        // Separate the results into Posted and Cancelled amounts
        const postedAmount = results
          .filter((row) => row.Type === "Posted" && row.Amount !== null)
          .reduce((sum, row) => {
            const amount = parseFloat(row.Amount);
            if (!isNaN(amount)) {
              return sum + amount;
            } else {
              console.error(
                `Invalid amount value for row: ${JSON.stringify(row)}`
              );
              // Handle the error as needed, e.g., skip this row
              return sum;
            }
          }, 0);

        const cancelledAmount = results
          .filter((row) => row.Type === "Cancelled" && row.Amount !== null)
          .reduce((sum, row) => {
            const amount = parseFloat(row.Amount);
            if (!isNaN(amount)) {
              return sum + amount;
            } else {
              console.error(
                `Invalid amount value for row: ${JSON.stringify(row)}`
              );
              // Handle the error as needed, e.g., skip this row
              return sum;
            }
          }, 0);

        // Calculate the previous month's MIB
        const previousMonthMIB = postedAmount - cancelledAmount;

        // Prepare the response in the desired format
        const responseData = {
          MIB: {
            total: previousMonthMIB.toFixed(2),
          },
        };

        // Send the response to the frontend
        res.json(responseData);
      }
    });
  });

  // GET MIB data for the current month for PUMA, JAGUAR, REGION KAREN
  router.get("/blue/karen", (req, res) => {
    // Get the current month and year in local time zone
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = currentDate.getFullYear();
    // Fetch data from the database for the current month and year in local time zone
    const query = `
    SELECT REPLACE(Amount_LCY, ',', '') as Amount, Type, Transaction_type
    FROM Recipts
    WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) 
  AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE)
  AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
        AND Pay_mode IS NOT NULL
      AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')
      AND Regions IN ('KAREN BLUE')`;

    pool.query(query, [currentMonth, currentYear], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        // Separate the results into Posted and Cancelled amounts
        const postedAmount = results
          .filter((row) => row.Type === "Posted" && row.Amount !== null)
          .reduce((sum, row) => {
            const amount = parseFloat(row.Amount);
            if (!isNaN(amount)) {
              return sum + amount;
            } else {
              console.error(
                `Invalid amount value for row: ${JSON.stringify(row)}`
              );
              // Handle the error as needed, e.g., skip this row
              return sum;
            }
          }, 0);

        const cancelledAmount = results
          .filter((row) => row.Type === "Cancelled" && row.Amount !== null)
          .reduce((sum, row) => {
            const amount = parseFloat(row.Amount);
            if (!isNaN(amount)) {
              return sum + amount;
            } else {
              console.error(
                `Invalid amount value for row: ${JSON.stringify(row)}`
              );
              // Handle the error as needed, e.g., skip this row
              return sum;
            }
          }, 0);

        // Calculate the current MIB
        const currentMIB = postedAmount - cancelledAmount;

        // Prepare the response in the desired format
        const responseData = {
          MIB: {
            total: currentMIB.toFixed(2),
          },
        };

        // Send the response to the frontend
        res.json(responseData);
      }
    });
  });

  // GET MIB data for the current month for PUMA, JAGUAR, REGION KAREN
  router.get("/blue/absa", (req, res) => {
    // Get the current month and year in local time zone
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = currentDate.getFullYear();
    // Fetch data from the database for the current month and year in local time zone
    const query = `
  SELECT REPLACE(Amount_LCY, ',', '') as Amount, Type, Transaction_type
  FROM Recipts
  WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) 
  AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE)
  AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
        AND Pay_mode IS NOT NULL
    AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')
    AND Regions IN ('HQ BLUE')`;

    pool.query(query, [currentMonth, currentYear], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        // Separate the results into Posted and Cancelled amounts
        const postedAmount = results
          .filter((row) => row.Type === "Posted" && row.Amount !== null)
          .reduce((sum, row) => {
            const amount = parseFloat(row.Amount);
            if (!isNaN(amount)) {
              return sum + amount;
            } else {
              console.error(
                `Invalid amount value for row: ${JSON.stringify(row)}`
              );
              // Handle the error as needed, e.g., skip this row
              return sum;
            }
          }, 0);

        const cancelledAmount = results
          .filter((row) => row.Type === "Cancelled" && row.Amount !== null)
          .reduce((sum, row) => {
            const amount = parseFloat(row.Amount);
            if (!isNaN(amount)) {
              return sum + amount;
            } else {
              console.error(
                `Invalid amount value for row: ${JSON.stringify(row)}`
              );
              // Handle the error as needed, e.g., skip this row
              return sum;
            }
          }, 0);

        // Calculate the current MIB
        const currentMIB = postedAmount - cancelledAmount;

        // Prepare the response in the desired format
        const responseData = {
          MIB: {
            total: currentMIB.toFixed(2),
          },
        };

        // Send the response to the frontend
        res.json(responseData);
      }
    });
  });

  // New route to count total sales in the current month absa for specified regions
  router.get("/total-sales-absa/current", (req, res) => {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();

    const query = `
      SELECT Type, COUNT(*) as count
      FROM Recipts
      WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) 
  AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE)
  AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
        AND Pay_mode IS NOT NULL
        AND Transaction_type IN ('Purchase Price', 'Deposit')
        AND Regions IN ('LEOPARDS', 'LIONS', 'HQ P', 'TIGERS', 'STAFF', 'HQ PROSPEROUS')
      GROUP BY Type`;

    pool.query(query, [currentMonth, currentYear], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        let postedCount = 0;
        let cancelledCount = 0;

        results.forEach((row) => {
          if (row.Type === "Posted") {
            postedCount = row.count;
          } else if (row.Type === "Cancelled") {
            cancelledCount = row.count;
          }
        });

        const totalSales = postedCount - cancelledCount;

        const responseData = {
          totalSales,
        };

        res.json(responseData);
      }
    });
  });

  // New route to count previous month's total sales absa for specified regions
  router.get("/total-sales-absa/previous", (req, res) => {
    const currentDate = new Date();
    const firstDayOfCurrentMonth = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      1
    );
    const firstDayOfPreviousMonth = new Date(firstDayOfCurrentMonth);
    firstDayOfPreviousMonth.setMonth(firstDayOfPreviousMonth.getMonth() - 1);
    const lastDayOfPreviousMonth = new Date(firstDayOfCurrentMonth);
    lastDayOfPreviousMonth.setDate(firstDayOfPreviousMonth.getDate() - 1);

    const query = `
      SELECT Type, COUNT(*) as count
      FROM Recipts
      WHERE STR_TO_DATE(Payment_date, '%m/%d/%y') BETWEEN ? AND ?
        AND Transaction_type IN ('Purchase Price', 'Deposit')
        AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
        AND Pay_mode IS NOT NULL
        AND Regions IN ('LEOPARDS', 'LIONS', 'HQ P', 'TIGERS', 'STAFF', 'HQ PROSPEROUS')
      GROUP BY Type`;

    pool.query(
      query,
      [firstDayOfPreviousMonth, lastDayOfPreviousMonth],
      (err, results) => {
        if (err) {
          console.error(err);
          res.status(500).json({ message: "Server Error" });
        } else {
          let postedCount = 0;
          let cancelledCount = 0;

          results.forEach((row) => {
            if (row.Type === "Posted") {
              postedCount = row.count;
            } else if (row.Type === "Cancelled") {
              cancelledCount = row.count;
            }
          });

          const totalSales = postedCount - cancelledCount;

          const responseData = {
            totalSales,
          };

          res.json(responseData);
        }
      }
    );
  });

  // New route to count total sales karen in the current month for specified regions
  router.get("/total-sales-karen/current", (req, res) => {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();

    const query = `
      SELECT Type, COUNT(*) as count
      FROM Recipts
      WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) 
  AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE)
  AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
        AND Pay_mode IS NOT NULL
        AND Transaction_type IN ('Purchase Price', 'Deposit')
        AND Regions IN ('PUMA', 'JAGUAR', 'REGION KAREN')
      GROUP BY Type`;

    pool.query(query, [currentMonth, currentYear], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        let postedCount = 0;
        let cancelledCount = 0;

        results.forEach((row) => {
          if (row.Type === "Posted") {
            postedCount = row.count;
          } else if (row.Type === "Cancelled") {
            cancelledCount = row.count;
          }
        });

        const totalSales = postedCount - cancelledCount;

        const responseData = {
          totalSales,
        };

        res.json(responseData);
      }
    });
  });

  // New route to count previous month's total sales karen for specified regions
  router.get("/total-sales-karen/previous", (req, res) => {
    const currentDate = new Date();
    const firstDayOfCurrentMonth = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      1
    );
    const firstDayOfPreviousMonth = new Date(firstDayOfCurrentMonth);
    firstDayOfPreviousMonth.setMonth(firstDayOfPreviousMonth.getMonth() - 1);
    const lastDayOfPreviousMonth = new Date(firstDayOfCurrentMonth);
    lastDayOfPreviousMonth.setDate(firstDayOfPreviousMonth.getDate() - 1);

    const query = `
      SELECT Type, COUNT(*) as count
      FROM Recipts
      WHERE STR_TO_DATE(Payment_date, '%m/%d/%y') BETWEEN ? AND ?
        AND Transaction_type IN ('Purchase Price', 'Deposit')
        AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
        AND Pay_mode IS NOT NULL
        AND Regions IN ('PUMA', 'JAGUAR', 'REGION KAREN')
      GROUP BY Type`;

    pool.query(
      query,
      [firstDayOfPreviousMonth, lastDayOfPreviousMonth],
      (err, results) => {
        if (err) {
          console.error(err);
          res.status(500).json({ message: "Server Error" });
        } else {
          let postedCount = 0;
          let cancelledCount = 0;

          results.forEach((row) => {
            if (row.Type === "Posted") {
              postedCount = row.count;
            } else if (row.Type === "Cancelled") {
              cancelledCount = row.count;
            }
          });

          const totalSales = postedCount - cancelledCount;

          const responseData = {
            totalSales,
          };

          res.json(responseData);
        }
      }
    );
  });

  // Function to fetch MIB data for a specific month, year, and team
  const fetchMIBData = (month, year, team, res) => {
    const query = `
  SELECT REPLACE(Amount_LCY, ',', '') as Amount, Type, Transaction_type
  FROM Recipts
  WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) 
  AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE)
  AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
        AND Pay_mode IS NOT NULL
    AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')
    AND Regions IN (?)
  `;

    pool.query(query, [month, year, team], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        const postedAmount = results
          .filter((row) => row.Type === "Posted" && row.Amount !== null)
          .reduce((sum, row) => sum + parseFloat(row.Amount) || 0, 0);

        const cancelledAmount = results
          .filter((row) => row.Type === "Cancelled" && row.Amount !== null)
          .reduce((sum, row) => sum + parseFloat(row.Amount) || 0, 0);

        const currentMIB = postedAmount - cancelledAmount;

        const responseData = {
          MIB: {
            total: currentMIB.toFixed(2),
          },
        };

        res.json(responseData);
      }
    });
  };

  // GET current MIB data for the specific team
  router.get("/current/:team", (req, res) => {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();
    const team = req.params.team.toLowerCase(); 

    fetchMIBData(currentMonth, currentYear, team, res);
  });

  // GET MIB data for the previous month based on the specific team
  router.get("/previous/:team", (req, res) => {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();
    const team = req.params.team.toLowerCase();

    const previousMonth = currentMonth - 1 === 0 ? 12 : currentMonth - 1;
    const previousYear = currentMonth - 1 === 0 ? currentYear - 1 : currentYear;

    fetchMIBData(previousMonth, previousYear, team, res);
  });

  router.get("/monthly-target/:team", (req, res) => {
    const team = req.params.team.toLowerCase();

    const query = `
    SELECT monthly_target
    FROM perf_reports.targets
    WHERE category = ?
  `;

    pool.query(query, [team], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        const monthlyTarget = results[0]?.monthly_target || "0";

        res.json({ monthlyTarget });
      }
    });
  });

  
  router.get("/combined-mib", (req, res) => {
    const currentDate = new Date();
    const currentDay = currentDate.getDate();
    const currentMonth = currentDate.getMonth() + 1; 
    const currentYear = currentDate.getFullYear();

    const queries = {
      currentMonth: `
      SELECT REPLACE(Amount_LCY, ',', '') as Amount, Type, Transaction_type, Pay_mode
      FROM Recipts
        WHERE MONTH(STR_TO_DATE(Date_Posted, '%m/%d/%y')) = ? AND YEAR(STR_TO_DATE(Date_Posted, '%m/%d/%y')) = ?
        AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')
        AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
        AND Pay_mode IS NOT NULL`,
      daily: `
      SELECT REPLACE(Amount_LCY, ',', '') as Amount, Type, Transaction_type, Pay_mode
      FROM Recipts
      WHERE POSTED_DATE1 LIKE CURRENT_DATE
        AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')
        AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
        AND Pay_mode IS NOT NULL`,
      installments: `
      SELECT REPLACE(Amount_LCY, ',', '') as Amount, Type, Transaction_type, Pay_mode
      FROM Recipts
      WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) 
  AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE)
        AND Transaction_type = 'Installment'
        AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
        AND Pay_mode IS NOT NULL`,
      transferCost: `
      SELECT REPLACE(Amount_LCY, ',', '') as Amount, Type, Transaction_type, Pay_mode
      FROM Recipts
      WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) 
  AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE)
        AND Transaction_type = 'Transfer Cost'
        AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
        AND Pay_mode IS NOT NULL`,
      deposits: `
      SELECT REPLACE(Amount_LCY, ',', '') as Amount, Type, Transaction_type, Pay_mode
      FROM Recipts
      WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) 
  AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE)
        AND Transaction_type = 'Deposit'
        AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
        AND Pay_mode IS NOT NULL`,
      additionalDeposit: `
      SELECT REPLACE(Amount_LCY, ',', '') as Amount, Type, Transaction_type, Pay_mode
      FROM Recipts
      WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) 
  AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE)
        AND Transaction_type = 'Additional Deposit'
        AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
        AND Pay_mode IS NOT NULL`,
    };

    const getMIB = (results) => {
      const postedAmount = results
        .filter((row) => row.Type === "Posted" && row.Amount !== null)
        .reduce((sum, row) => {
          const amount = parseFloat(row.Amount);
          return !isNaN(amount) ? sum + amount : sum;
        }, 0);

      const cancelledAmount = results
        .filter((row) => row.Type === "Cancelled" && row.Amount !== null)
        .reduce((sum, row) => {
          const amount = parseFloat(row.Amount);
          return !isNaN(amount) ? sum + amount : sum;
        }, 0);

      return postedAmount - cancelledAmount;
    };

    const queryDatabase = (query, params) => {
      return new Promise((resolve, reject) => {
        pool.query(query, params, (err, results) => {
          if (err) {
            reject(err);
          } else {
            resolve(results);
          }
        });
      });
    };

    Promise.all([
      queryDatabase(queries.currentMonth, [currentMonth, currentYear]),
      queryDatabase(queries.daily, [currentDay, currentMonth, currentYear]),
      queryDatabase(queries.installments, [currentMonth, currentYear]),
      queryDatabase(queries.transferCost, [currentMonth, currentYear]),
      queryDatabase(queries.deposits, [currentMonth, currentYear]),
      queryDatabase(queries.additionalDeposit, [currentMonth, currentYear]),
    ])
      .then((results) => {
        const [
          currentMonthResults,
          dailyResults,
          installmentsResults,
          transferCostResults,
          depositsResults,
          additionalDepositResults,
        ] = results;

        const responseData = {
          MIB: {
            currentMonth: getMIB(currentMonthResults).toFixed(2),
            daily: getMIB(dailyResults).toFixed(2),
            installments: getMIB(installmentsResults).toFixed(2),
            transferCost: getMIB(transferCostResults).toFixed(2),
            deposits: getMIB(depositsResults).toFixed(2),
            additionalDeposit: getMIB(additionalDepositResults).toFixed(2),
          },
        };

        res.json(responseData);
      })
      .catch((err) => {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      });
  });


  //testing kama system yangu inanipima
  // dragon moto testing
 // Existing Route: Get all unique project names
router.get("/recon", async (req, res) => {
  try {
    pool.query(
      `SELECT DISTINCT Project_Name 
       FROM Posted_Receipts 
       WHERE Project_Name IS NOT NULL`,
      (err, results) => {
        if (err) {
          console.error(err);
          return res.status(500).json({
            message: "An error occurred while fetching project names.",
          });
        }

        res.json({
          projects: results, // Array of { Project_Name: '...' }
        });
      }
    );
  } catch (error) {
    console.error(error);
    res.status(500).json({
      message: "An error occurred while fetching project names.",
    });
  }
});
/********************************************
 * 1) GET: Specific Projects
 ********************************************/
router.get("/recon/specific", async (req, res) => {
  try {
    // We want to fetch projects where the name is LIKE any of these four:
    const searchTerms = ["%vipingo%", "%joy lovers%", "%great oasis%", "%ushindi%"];

    // Build a query that checks each term with OR
    const placeholders = searchTerms.map(() => "Project_Name LIKE ?").join(" OR ");
    const query = `
      SELECT DISTINCT
        Project_Name
      FROM Posted_Receipts
      WHERE (${placeholders})
        AND Project_Name IS NOT NULL
    `;

    pool.query(query, searchTerms, (err, results) => {
      if (err) {
        console.error(err);
        return res.status(500).json({
          message: "An error occurred while fetching specific project names.",
        });
      }
      return res.json({ projects: results });
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      message: "An error occurred while fetching specific project names.",
    });
  }
});

/********************************************
 * MAPPING LOGIC for Bank Accounts + DB Names
 * We'll reuse this in endpoints that need to
 * filter by projectName and bank account.
 ********************************************/
function getProjectInfo(projectNameParam) {
  const lowerName = projectNameParam.toLowerCase();

  // If the user passes anything with "vipingo":
  if (lowerName.includes("vipingo")) {
    return {
      projectNames: ["Ocean View Ridge -Vipingo", "VIPINGO PRIME"],
      bankAccount: "*************",
    };
  }
  // If the user passes "joy lovers club" (or anything containing "joy lovers")
  else if (lowerName.includes("joy lovers")) {
    return {
      // Use the EXACT name from your DB
      projectNames: ["Joy Lovers Club"],
      bankAccount: "*************",
    };
  }
  else if (lowerName.includes("ushindi")) {
    return {
      // Use the EXACT name from your DB
      projectNames: ["Ushindi Gardens-Nakuru"],
      bankAccount: "*************",
    };
  }
  else if (lowerName.includes("great oasis")) {
    return {
      // Use the EXACT name from your DB
      projectNames: ["The Great Oasis-Nanyuki"],
      bankAccount: "*************",
    };
  }

  // If none of these match, it returns empty => 400
  return { projectNames: [], bankAccount: "" };
}

/********************************************
 * 2) GET: Receipts for a specific project
 *    Includes all four projects
 ********************************************/
router.get("/receipts/:projectName", async (req, res) => {
  const { projectName } = req.params;
  const { projectNames, bankAccount } = getProjectInfo(projectName);

  if (!projectNames.length || !bankAccount) {
    return res
      .status(400)
      .json({ message: `No known mapping for project "${projectName}".` });
  }

  try {
    const placeholders = projectNames.map(() => "?").join(", ");
    const sql = `
      SELECT
        id,
        Bank_Account,
        Customer_Name,
        Receipt_No,
        PAYMENT_DATE1,
        Pay_mode,
        Project_Name,
        Plot_NO,
        assigned_to,
        discharge,
        level1_comments,
        level2_comments,
        legal_comments,
        approval_status,
        approval_comments,
        Narration,
        Amount_LCY
      FROM Posted_Receipts
      WHERE Project_Name IN (${placeholders})
        AND Bank_Account LIKE ?
      ORDER BY id DESC
    `;

    const params = [...projectNames, `%${bankAccount}%`];

    pool.query(sql, params, (err, results) => {
      if (err) {
        console.error(err);
        return res
          .status(500)
          .json({ message: "An error occurred while fetching receipts." });
      }
      if (!results || results.length === 0) {
        return res
          .status(404)
          .json({ message: "No receipts found for this project." });
      }
      return res.json({ receipts: results });
    });
  } catch (error) {
    console.error(error);
    return res
      .status(500)
      .json({ message: "An error occurred while fetching receipts." });
  }
});

/********************************************
 * 3) GET: Distinct plots for a specific project
 *    Updated to handle all four projects
 ********************************************/
router.get("/projects/:projectName/plots", async (req, res) => {
  const { projectName } = req.params;
  const { projectNames } = getProjectInfo(projectName);

  // If no known mapping was found, return an error:
  if (!projectNames.length) {
    return res.status(400).json({
      message: `No known DB project names for project "${projectName}".`,
    });
  }

  try {
    const placeholders = projectNames.map(() => "?").join(", ");
    const query = `
      SELECT DISTINCT Plot_NO
      FROM Posted_Receipts
      WHERE Project_Name IN (${placeholders})
    `;

    pool.query(query, projectNames, (err, results) => {
      if (err) {
        console.error("Error fetching plots:", err);
        return res
          .status(500)
          .json({ message: "An error occurred while fetching plots." });
      }
      return res.json({ plots: results });
    });
  } catch (error) {
    console.error("Unexpected error:", error);
    res.status(500).json({ message: "An unexpected error occurred." });
  }
});

/********************************************
 * 4) GET: Plots with discharge = 'initiated'
 *    Now includes all four known bank accounts
 ********************************************/
router.get("/plots/discharge-initiated", async (req, res) => {
  try {
    // If you want to strictly limit to the known 4 bank accounts:
    const bankAccounts = [
      "*************", // Vipingo
      "*************", // Joy Lovers
      "*************", // Great Oasis
      "*************", // Ushindi
    ];

    // Build an OR condition for the bank accounts
    // e.g. (r.Bank_Account LIKE '%05501%' OR r.Bank_Account LIKE '%05502%' ...)
    const orConditions = bankAccounts
      .map(() => "r.Bank_Account LIKE ?")
      .join(" OR ");

    const sql = `
      SELECT DISTINCT
        lf.plot_number,
        lf.purchase_price,
        r.discharge,
        r.Bank_Account,
        r.Customer_Name
      FROM defaultdb.Posted_Receipts r
      JOIN CRM.lead_files lf ON r.Lead_file_no = lf.lead_file_no
      WHERE r.discharge = 'initiated'
        AND (${orConditions})
    `;

    // For partial matching, wrap each account in '%...%'
    const queryParams = bankAccounts.map((acc) => `%${acc}%`);

    pool.query(sql, queryParams, (err, results) => {
      if (err) {
        console.error(err);
        return res.status(500).json({
          message: "Error while fetching discharge-initiated plots.",
          error: err,
        });
      }
      if (!results || results.length === 0) {
        return res
          .status(404)
          .json({ message: "No plots found with discharge initiated." });
      }
      return res.json({ plots: results });
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
});

/********************************************
 * 5) GET: Plots with discharge in pending states
 *    Now includes all four known bank accounts
 ********************************************/
router.get("/plots/discharge-pending", async (req, res) => {
  try {
    const bankAccounts = [
      "*************",
      "*************",
      "*************",
      "*************",
    ];
    const orConditions = bankAccounts
      .map(() => "r.Bank_Account LIKE ?")
      .join(" OR ");

    const sql = `
      SELECT DISTINCT
        lf.plot_number,
        lf.purchase_price,
        r.discharge,
        r.Bank_Account,
        r.Customer_Name
      FROM defaultdb.Posted_Receipts r
      JOIN CRM.lead_files lf ON r.Lead_file_no = lf.lead_file_no
      WHERE r.discharge IN (
        'Submitted',
        'Approved Level 1',
        'Rejected Level 1',
        'Approved Level 2',
        'Rejected Level 2',
        'Submitted to Legal',
        'Approved Legal',
        'Rejected Legal'
      )
      AND (${orConditions})
    `;
    const queryParams = bankAccounts.map((acc) => `%${acc}%`);

    pool.query(sql, queryParams, (err, results) => {
      if (err) {
        console.error(err);
        return res
          .status(500)
          .json({ message: "Error fetching pending discharge plots", error: err });
      }
      if (!results || results.length === 0) {
        return res
          .status(404)
          .json({ message: "No pending discharge plots found." });
      }
      return res.json({ plots: results });
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
});

/********************************************
 * 6) GET: Receipts for a specific plotNo
 *    Now includes all four bank accounts
 ********************************************/
router.get("/plots/:plotNo", async (req, res) => {
  const { plotNo } = req.params;

  try {
    const bankAccounts = [
      "*************",
      "*************",
      "*************",
      "*************",
    ];
    const orConditions = bankAccounts
      .map(() => "r.Bank_Account LIKE ?")
      .join(" OR ");

    const query = `
      SELECT
        r.id,
        r.Bank_Account,
        r.Customer_Name,
        r.Receipt_No,
        r.Pay_mode,
        r.PAYMENT_DATE1,
        r.Project_Name,
        r.Plot_NO,
        r.assigned_to,
        r.reassign_comment,
        r.discharge,
        r.level1_comments,
        r.level2_comments,
        r.legal_comments,
        r.approval_status,
        r.approval_comments,
        r.Narration,
        r.Amount_LCY,
        lf.purchase_price
      FROM defaultdb.Posted_Receipts r
      JOIN CRM.lead_files lf ON r.Lead_file_no = lf.lead_file_no
      WHERE 
        (${orConditions})
        AND (
          (r.Plot_NO = ? AND (r.assigned_to IS NULL OR r.assigned_to = ''))
          OR r.assigned_to = ?
        )
      ORDER BY r.Narration DESC
    `;

    const bankParams = bankAccounts.map((acc) => `%${acc}%`);
    const params = [...bankParams, plotNo, plotNo];

    pool.query(query, params, (err, results) => {
      if (err) {
        console.error(err);
        return res
          .status(500)
          .json({ message: "An error occurred while fetching receipts.", error: err });
      }
      if (!results || results.length === 0) {
        return res.status(404).json({
          message:
            "No receipts found for this plot number (with the specified bank accounts).",
        });
      }
      res.json({ receipts: results });
    });
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ message: "An error occurred while fetching receipts.", error: error.message });
  }
});

/********************************************
 * 7) PATCH: Update the discharge status
 *    for a given plot. (Unchanged logic)
 ********************************************/
router.patch("/plots/:plotNo/discharge", async (req, res) => {
  const { plotNo } = req.params;
  const { discharge, comments } = req.body;

  if (!discharge) {
    return res.status(400).json({ message: "Discharge status is required." });
  }

  let updateQuery = "";
  let updateValues = [];

  if (discharge.includes("Level 1")) {
    updateQuery = `
      UPDATE defaultdb.Posted_Receipts
      SET discharge = ?, level1_comments = ?
      WHERE Plot_NO = ?
    `;
    updateValues = [discharge, comments || null, plotNo];
  } else if (discharge.includes("Level 2")) {
    updateQuery = `
      UPDATE defaultdb.Posted_Receipts
      SET discharge = ?, level2_comments = ?
      WHERE Plot_NO = ?
    `;
    updateValues = [discharge, comments || null, plotNo];
  } else if (discharge.includes("Legal")) {
    updateQuery = `
      UPDATE defaultdb.Posted_Receipts
      SET discharge = ?, legal_comments = ?
      WHERE Plot_NO = ?
    `;
    updateValues = [discharge, comments || null, plotNo];
  } else {
    // Default update (initiated or other statuses)
    updateQuery = `
      UPDATE defaultdb.Posted_Receipts
      SET discharge = ?
      WHERE Plot_NO = ?
    `;
    updateValues = [discharge, plotNo];
  }

  pool.query(updateQuery, updateValues, (err, results) => {
    if (err) {
      console.error(err);
      return res
        .status(500)
        .json({ message: "Error updating discharge status.", error: err });
    }
    if (results.affectedRows === 0) {
      return res.status(404).json({ message: "Plot not found." });
    }
    return res.json({ message: "Discharge status updated successfully.", discharge });
  });
});

/********************************************
 * 8) PATCH: Re-assign transaction to a new plot
 *    (Unchanged logic)
 ********************************************/
router.patch("/transactions/:id/assign", async (req, res) => {
  const { id } = req.params;
  const { newPlotNo, reassign_comment } = req.body;

  if (!newPlotNo || !reassign_comment || reassign_comment.trim() === "") {
    return res.status(400).json({
      message: "New plot number and a reassignment comment are required.",
    });
  }

  const query = `
    UPDATE defaultdb.Posted_Receipts
    SET assigned_to = ?, reassign_comment = ?
    WHERE id = ?
  `;
  pool.query(
    query,
    [newPlotNo.trim(), reassign_comment.trim(), id],
    (err, result) => {
      if (err) {
        console.error("Error updating assigned_to:", err);
        return res
          .status(500)
          .json({ message: "Error updating assigned_to", error: err });
      }
      if (result.affectedRows === 0) {
        return res.status(404).json({
          message: `No transaction found with id=${id} or update not performed.`,
        });
      }
      return res.json({ message: "Transaction reassigned successfully." });
    }
  );
});

/********************************************
 * 9) GET: All transactions for a specific project
 *    (Already updated to handle all four)
 ********************************************/
router.get("/projects/:projectName/transactions", async (req, res) => {
  try {
    const { projectName } = req.params;
    const { projectNames, bankAccount } = getProjectInfo(projectName);

    // If no known mapping:
    if (!projectNames.length || !bankAccount) {
      return res.status(400).json({
        message: `No known bank account mapping for project name "${projectName}".`,
      });
    }

    const placeholders = projectNames.map(() => "?").join(", ");
    const query = `
      SELECT
        id,
        Bank_Account,
        Receipt_No,
        PAYMENT_DATE1,
        Pay_mode,
        Customer_Name,
        Project_Name,
        Plot_NO,
        assigned_to,
        discharge,
        level1_comments,
        level2_comments,
        legal_comments,
        approval_status,
        approval_comments,
        Narration,
        Amount_LCY
      FROM Posted_Receipts
      WHERE Project_Name IN (${placeholders})
        AND Bank_Account LIKE ?
      ORDER BY Narration DESC
    `;

    // partial match for bank account
    const queryParams = [...projectNames, `%${bankAccount}%`];

    pool.query(query, queryParams, (err, results) => {
      if (err) {
        console.error("Error fetching transactions:", err);
        return res
          .status(500)
          .json({ message: "An error occurred while fetching transactions." });
      }
      if (!results || results.length === 0) {
        return res
          .status(404)
          .json({ message: "No transactions found for this project." });
      }
      return res.json({ transactions: results });
    });
  } catch (error) {
    console.error("Error in transactions route:", error);
    return res
      .status(500)
      .json({ message: "An error occurred while fetching transactions." });
  }
});

/********************************************
 * 10) PATCH: Update the approval status
 *     for a single receipt (Unchanged logic)
 ********************************************/
router.patch("/receipts/:receiptId/approval", async (req, res) => {
  const { receiptId } = req.params;
  const { approval_status, approval_comments } = req.body;

  if (!approval_status) {
    return res.status(400).json({ message: "Approval status is required." });
  }

  const validStatuses = [
    "DischargeApproved",
    "DischargeRejected",
    "Level1Approved",
    "Level1Rejected",
    "Level2Approved",
    "Level2Rejected",
    "LegalApproved",
    "LegalRejected",
  ];
  if (!validStatuses.includes(approval_status)) {
    return res.status(400).json({
      message: `Invalid approval status provided. Must be one of: ${validStatuses.join(", ")}`,
    });
  }

  const sql = `
    UPDATE defaultdb.Posted_Receipts
    SET approval_status = ?, approval_comments = ?
    WHERE id = ?
  `;

  pool.query(sql, [approval_status, approval_comments || null, receiptId], (err, result) => {
    if (err) {
      console.error("Error updating approval status:", err);
      return res.status(500).json({ message: "Error updating approval status", error: err });
    }
    if (result.affectedRows === 0) {
      return res
        .status(404)
        .json({ message: `No receipt found with id=${receiptId} or update not performed.` });
    }
    return res.json({ message: "Approval status updated successfully.", approval_status });
  });
});

// Add a new route for server-side pagination of plots
router.get("/projects/:projectName/plots/paginated", async (req, res) => {
  try {
    const { projectName } = req.params;
    const { page = 1, limit = 10 } = req.query; 
    const { projectNames } = getProjectInfo(projectName);

    if (!projectNames.length) {
      return res.status(400).json({
        message: `No known DB project names for project "${projectName}".`,
      });
    }

    const pageNum = parseInt(page, 10) || 1;
    const perPage = parseInt(limit, 10) || 10;
    const offset = (pageNum - 1) * perPage;

    const placeholders = projectNames.map(() => "?").join(", ");

    const countQuery = `
      SELECT COUNT(DISTINCT r.Plot_NO) AS total
      FROM Posted_Receipts r
      WHERE r.Project_Name IN (${placeholders})
        AND r.Plot_NO IS NOT NULL
    `;

    const mainQuery = `
      SELECT 
        r.Plot_NO,
        COALESCE(MAX(lf.purchase_price), 0) AS purchase_price,
        SUM(r.Amount_LCY) AS totalPaid,
        MAX(r.Customer_Name) AS Customer_Name
      FROM Posted_Receipts r
      LEFT JOIN CRM.lead_files lf ON r.Lead_file_no = lf.lead_file_no
      WHERE r.Project_Name IN (${placeholders})
        AND r.Plot_NO IS NOT NULL
      GROUP BY r.Plot_NO
      ORDER BY r.Plot_NO ASC
      LIMIT ?, ?
    `;

    pool.query(countQuery, projectNames, (countErr, countResults) => {
      if (countErr) {
        console.error("Error in countQuery:", countErr);
        return res
          .status(500)
          .json({ message: "Error counting plots.", error: countErr });
      }

      const totalRows = countResults?.[0]?.total ?? 0;

      // Now run the mainQuery to fetch just one page of results
      pool.query(
        mainQuery,
        [...projectNames, offset, perPage],
        (mainErr, plotResults) => {
          if (mainErr) {
            console.error("Error in mainQuery:", mainErr);
            return res
              .status(500)
              .json({ message: "Error retrieving paginated plots.", error: mainErr });
          }

          // Format the rows into a clean array of plot objects
          const plots = plotResults.map((p) => ({
            Plot_NO: p.Plot_NO,
            purchase_price: parseFloat(p.purchase_price) || 0,
            totalPaid: parseFloat(p.totalPaid) || 0,
            Customer_Name: p.Customer_Name || null,
            Customer_id: p.Customer_id || null,
          }));

          // Return JSON with totalRows (needed by the frontend) + the plots
          return res.json({
            totalRows,
            plots,
          });
        }
      );
    });
  } catch (error) {
    console.error("Error in /paginated route:", error);
    return res.status(500).json({
      message: "An error occurred while fetching paginated plots.",
    });
  }
});


  return router;
};

