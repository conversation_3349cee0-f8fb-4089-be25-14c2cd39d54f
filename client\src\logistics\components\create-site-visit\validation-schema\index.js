import * as Yup from "yup";

export const siteVisitSchema = Yup.object().shape({
  project_id: Yup.string().required("Site is required"),
  pickup_location: Yup.string().required("Pickup location is required"),
  pickup_date: Yup.date().required("Pickup date is required"),
  pickup_time: Yup.string().required("Pickup time is required"),
});

export const clientSchema = Yup.array().of(
  Yup.object().shape({
    clientFirstName: Yup.string().required("First name is required"),
    clientLastName: Yup.string().required("Last name is required"),
    phone_number: Yup.string()
      .required("Phone number is required")
      .matches(/^\+?\d+(-\d+)*$/, "Phone number is not valid"),
    email: Yup.string().email("Invalid email"),
  })
);
