const express = require("express");
const router = express.Router();
const authenticateJWT = require("../../middleware/authenticateJWT");

module.exports = (pool) => {
router.get("/",authenticateJWT, (req, res) => {
    try {
      pool.query(
        "SELECT * FROM value_addition_users ORDER BY name ASC",
        (err, results) => {
          if (err) throw err;
          res.json(results);
        }
      );
    } catch (err) {
      console.error(err);
      res.status(500).json({
        message: "Error in fetching construction managers",
      });
    }
  });
    // get single user by id
    router.get('/:id', authenticateJWT,(req, res) => {
      const { id } = req.params;
      try {
        pool.query(
          "SELECT * FROM value_addition_users WHERE id = ?",
          id,
          (err, results) => {
            if (err) {
              throw err;
            }
            // res.status(200).json({ results, message: "user  fetched Successfully" });
            res.json(results[0])
          }
        );
      } catch (err) {
        res.status(500).json({ message: "Error in fetching user" });
      }
    });
  router.post("/",authenticateJWT, async (req, res) => {
    const { name, contact, role } = req.body;

    try {
      const result = await pool
        .promise()
        .query(
          "INSERT INTO value_addition_users(name, contact, role) VALUES ( ?, ?, ?)",
          [ name, contact, role]
        );

      const newUser = {
        user_id: result.insertId,
        name,
        contact,
        role,
      };
      res.json(newUser);
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });
  router.patch("/:id",authenticateJWT, async (req, res) => {
    const {name, contact, role } = req.body;

    try {
      const result = await pool
        .promise()
        .query(
          "UPDATE value_addition_users SET name=?, contact=?, role=? WHERE id=?",
          [name, contact, role, req.params.id]
        );

      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "user not found" });
      }

      res.status(200).json({ message: "user updated successfully" });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });

  // Delete labour by ID
  
  router.delete("/:id", authenticateJWT,async (req, res) => {
    try {
      // Check if the  labour exists
      const labour = await pool
        .promise()
        .query("SELECT id FROM value_addition_users WHERE id=?", [req.params.id]);
  
      if (labour[0].length === 0) {
        return res.status(404).json({ message: "Labour not found" });
      }
  
      // Now, delete the  labour
      const result = await pool
        .promise()
        .query("DELETE FROM value_addition_users WHERE id=?", [req.params.id]);
  
      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "user not found" });
      }
  
      res.json({ message: " user deleted successfully" });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });
  return router
};