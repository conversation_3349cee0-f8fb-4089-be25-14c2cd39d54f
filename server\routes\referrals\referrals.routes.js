const express = require("express");
const router = express.Router();
const axios = require("axios");

module.exports = (pool) => {
  // Handle referral submissions
  router.post("/", (req, res) => {
    const {
      name,
      phoneNumber,
      email,
      message,
      agreedToTerms,
      referralCode,
      projectId,
    } = req.body;

    // Basic validation
    if (!name || !phoneNumber || !email || !agreedToTerms) {
      return res.status(400).json({ error: "Required fields are missing" });
    }

    // Validate referralCode format
    if (!referralCode || !referralCode.includes("-")) {
      return res.status(400).json({ error: "Invalid referral code format" });
    }

    // Extract referred_by from referralCode
    const [referredBy, projectIdFromCode] = referralCode.split("-");

    // Optional: Verify that projectId matches projectIdFromCode
    if (projectId !== projectIdFromCode) {
      return res.status(400).json({ error: "Project ID mismatch" });
    }

    // Insert into the database
    const query = `INSERT INTO referrals
      (name, phone_number, email, message, referral_code, project_id, referred_by, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, NOW())`;

    const values = [
      name,
      phoneNumber,
      email,
      message,
      referralCode,
      projectId,
      referredBy,
    ];

    pool.query(query, values, (err, result) => {
      if (err) {
        console.error("Error submitting referral:", err);
        return res.status(500).json({ error: "Internal server error" });
      }

      // Prepare data to send to the external endpoint
      const externalData = {
        name,
        phone: phoneNumber,
        email,
        message,
        referral_code: referralCode,
        project_id: projectId,
        referred_by: referredBy,
      };

      // Send data to the external endpoint
      axios
        .post("https://crm.optiven.co.ke/API/clients_app.php", externalData, {
          headers: { "Content-Type": "application/json" },
        })
        .then(() => {
          // External API call succeeded
          console.log("External API call succeeded");
        })
        .catch((externalError) => {
          console.error(
            "Error sending data to external endpoint:",
            externalError.message
          );
          // Handle the error appropriately
        });

      // Send response to client immediately
      res.status(201).json({ message: "Referral submitted successfully" });
    });
  });

  return router;
};
