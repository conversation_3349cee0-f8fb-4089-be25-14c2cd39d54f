import React, { useCallback, useEffect, useMemo, useState } from "react";
import Sidebar from "../components/Sidebar";
import { io } from "socket.io-client";
import { useSelector, useDispatch } from "react-redux";
import { formatDistanceToNowStrict } from "date-fns";
import { Link } from "react-router-dom";

import {
  fetchNotifications,
  setNotifications as updateNotifications,
  addNotification,
} from "../../redux/logistics/features/notifications/notificationsSlice";

import huh from "../../assets/app-illustrations/Shrug-bro.png";
import formatToAMPM from "../../utils/formatToAMPM";
import {
  BadgeCheck,
  Ban,
  CheckCheck,
  CircleCheckBig,
  CircleX,
} from "lucide-react";
import { TailSpin } from "react-loader-spinner";

const NotificationItem = React.memo(
  ({ notification, markAsRead, siteVisit }) => {
    const { id, type, isRead, timestamp, remarks, site_visit_id } =
      notification;

    // Icon based on notification type
    const notificationIcons = {
      rejected: <CircleX className="text-red-500 w-6 h-6" />,
      approved: <BadgeCheck className="text-green-500 w-6 h-6" />,
      completed: <CircleCheckBig className="text-blue-500 w-6 h-6" />,
      cancelled: <Ban className="text-gray-500 w-6 h-6" />,
    };

    // Detailed message
    const getDetailedMessage = () => {
      if (!siteVisit) {
        return notification.message;
      }

      const dateStr = new Date(siteVisit.pickup_date).toLocaleDateString(
        "en-GB"
      );
      const timeStr =
        formatToAMPM(siteVisit.pickup_time) || siteVisit.pickup_time;
      const siteName = siteVisit.site_name || "Unknown Site";

      switch (type) {
        case "approved":
          return `Your site visit to ${siteName} on ${dateStr} at ${timeStr} has been approved!`;
        case "completed":
          return `Your site visit to ${siteName} on ${dateStr} at ${timeStr} has been completed. Please fill out the survey!`;
        case "rejected":
          return `Your site visit to ${siteName} on ${dateStr} at ${timeStr} has been rejected.`;
        case "cancelled":
          return `Your site visit to ${siteName} on ${dateStr} at ${timeStr} has been cancelled.`;
        default:
          return notification.message;
      }
    };

    return (
      <div
        className={`flex flex-col sm:flex-row sm:items-start justify-between p-5 rounded-lg shadow-md transition bg-white hover:shadow-lg ${
          !isRead ? "border-l-4 border-blue-500 bg-blue-50" : ""
        }`}
      >
        <div className="flex items-start">
          <div className="mr-4 mt-1">{notificationIcons[type]}</div>
          <div>
            <p className="text-gray-800 font-medium">{getDetailedMessage()}</p>
            {remarks && (
              <p className="text-gray-600 text-sm mt-1">Remarks: {remarks}</p>
            )}
            {timestamp && (
              <p className="text-gray-500 text-xs italic mt-1">
                {formatDistanceToNowStrict(new Date(timestamp))} ago
              </p>
            )}
            <div className="flex flex-wrap mt-2 space-x-2">
              {type === "completed" && (
                <Link
                  to={`/survey/${site_visit_id}`}
                  className="text-primary underline text-sm hover:text-primary-dark"
                >
                  Complete the survey
                </Link>
              )}
              {site_visit_id && (
                <Link
                  to={`/sv-details/${site_visit_id}`}
                  className="text-blue-600 underline text-sm hover:text-blue-800"
                >
                  View details
                </Link>
              )}
            </div>
          </div>
        </div>

        <div className="mt-4 sm:mt-0 sm:text-right">
          {/* Physical Mark as Read button */}
          {!isRead ? (
            <button
              onClick={() => markAsRead(id)}
              className="text-sm bg-blue-500 text-white px-4 py-2 rounded-full hover:bg-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-300 transition"
            >
              Mark as Read
            </button>
          ) : (
            <CheckCheck className="text-gray-400 w-5 h-5" aria-label="Read" />
          )}
        </div>
      </div>
    );
  }
);

const EmptyState = () => (
  <div className="flex flex-col items-center mt-20">
    <img src={huh} alt="No notifications" className="w-64 md:w-80 mb-6" />
    <h2 className="text-xl font-semibold text-gray-700 text-center">
      No notifications. Check back later.
    </h2>
  </div>
);

const Notifications = () => {
  const { notifications } = useSelector((state) => state.notifications);
  const { status, error } = useSelector((state) => state.notifications);
  const token = useSelector((state) => state.user.token);

  const dispatch = useDispatch();

  const notificationsArray = useMemo(() => 
    Array.isArray(notifications?.notifications)
      ? notifications.notifications
      : [], 
    [notifications]
  );

  const [siteVisitsMap, setSiteVisitsMap] = useState({});
  const [isFetchingSiteVisits, setIsFetchingSiteVisits] = useState(false);
  const [filterType, setFilterType] = useState("all");

  const markAsRead = useCallback(
    async (notificationId) => {
      if (!notificationId) return;
      try {
        const response = await fetch(
          `https://workspace.optiven.co.ke/api/notifications/${notificationId}`,
          {
            method: "PATCH",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ isRead: true }),
          }
        );
        if (response.ok) {
          // Update Redux state
          dispatch(
            updateNotifications(
              notificationsArray.map((notification) =>
                notification.id === notificationId
                  ? { ...notification, isRead: true }
                  : notification
              )
            )
          );
        } else {
          console.error("Error marking notification as read:", response.status);
        }
      } catch (error) {
        console.error("Error marking notification as read:", error);
      }
    },
    [dispatch, notificationsArray, token]
  );

  const markAllAsRead = useCallback(async () => {
    if (!Array.isArray(notificationsArray)) return;
    const unread = notificationsArray.filter((notif) => !notif.isRead);
    if (unread.length === 0) return;

    try {
      const promises = unread.map((notification) =>
        fetch(
          `https://workspace.optiven.co.ke/api/notifications/${notification.id}`,
          {
            method: "PATCH",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ isRead: true }),
          }
        )
      );
      await Promise.all(promises);

      dispatch(
        updateNotifications(
          notificationsArray.map((notification) => ({
            ...notification,
            isRead: true,
          }))
        )
      );
    } catch (err) {
      console.error("Error marking all as read:", err);
    }
  }, [dispatch, notificationsArray, token]);

  useEffect(() => {
    if (status === "idle") {
      dispatch(fetchNotifications());
    }
  }, [dispatch, status]);

  useEffect(() => {
    const fetchSiteVisits = async () => {
      if (!notificationsArray || notificationsArray.length === 0) return;
      const siteVisitIds = [
        ...new Set(
          notificationsArray.map((n) => n.site_visit_id).filter(Boolean)
        ),
      ];
      if (siteVisitIds.length === 0) return;

      try {
        setIsFetchingSiteVisits(true);
        const resp = await fetch(
          `https://workspace.optiven.co.ke/api/site-visit-requests?ids=${siteVisitIds.join(
            ","
          )}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (!resp.ok) {
          throw new Error(`Error fetching site visits: ${resp.status}`);
        }
        const data = await resp.json();

        const visitsMap = {};
        data.forEach((sv) => {
          visitsMap[sv.id] = sv;
        });
        setSiteVisitsMap(visitsMap);
      } catch (err) {
        console.error("Error fetching site visits:", err);
      } finally {
        setIsFetchingSiteVisits(false);
      }
    };

    fetchSiteVisits();
  }, [notificationsArray, token]);

  useEffect(() => {
    const socket = io("https://workspace.optiven.co.ke");

    if (notificationsArray.length === 0 && status === "succeeded") {
      dispatch(fetchNotifications());
    }

    const handleNewNotification = (notif) => {
      const formattedNotification = {
        type: notif.type,
        message: notif.message,
        site_visit_id: notif.site_visit_id,
        remarks: notif.remarks,
        timestamp: new Date(notif.timestamp),
        isRead: false,
      };
      dispatch(addNotification(formattedNotification));
    };

    // Generic handlers based on event
    socket.on("siteVisitRejected", (notif) =>
      handleNewNotification({
        ...notif,
        type: "rejected",
        message: "Your site visit request has been rejected :(",
      })
    );
    socket.on("siteVisitApproved", (notif) =>
      handleNewNotification({
        ...notif,
        type: "approved",
        message: "Your site visit request has been approved!",
      })
    );
    socket.on("siteVisitCompleted", (notif) =>
      handleNewNotification({
        ...notif,
        type: "completed",
        message: "A site visit has been completed. Please fill out the survey.",
      })
    );
    socket.on("siteVisitCancelled", (notif) =>
      handleNewNotification({
        ...notif,
        type: "cancelled",
        message: "A site visit has been cancelled.",
      })
    );

    return () => {
      socket.disconnect();
    };
  }, [dispatch, notificationsArray, status]);

  const filteredNotifications = (() => {
    if (!Array.isArray(notificationsArray)) return [];
    switch (filterType) {
      case "unread":
        return notificationsArray.filter((n) => !n.isRead);
      case "survey":
        // "Fill Survey" typically means type: "completed"
        return notificationsArray.filter((n) => n.type === "completed");
      default:
        return notificationsArray; // 'all'
    }
  })();

  const isLoading = status === "loading";
  const hasNotifications =
    Array.isArray(notificationsArray) && notificationsArray.length > 0;

  return (
    <Sidebar>
      {/* Centering the content similarly to RequestVehicle */}
      <div className="hero">
        <div className="hero-content w-full max-w-3xl flex flex-col bg-gray-100 p-6 rounded-lg shadow-md">
          <header className="mb-6 text-center">
            <h1 className="text-3xl font-bold text-gray-800 mb-3">
              Notifications
            </h1>
            {isLoading && (
              <div className="flex items-center justify-center mt-2">
                <TailSpin height="24" width="24" color="#666666" />
                <span className="ml-2 text-sm text-gray-600 italic">
                  Loading notifications...
                </span>
              </div>
            )}
            {isFetchingSiteVisits && (
              <div className="flex items-center justify-center mt-2">
                <span className="text-sm text-gray-600 italic">
                  Loading additional details...
                </span>
              </div>
            )}
          </header>

          {/* Filters & Mark All as Read */}
          {hasNotifications && (
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4">
              <div className="flex gap-2 items-center mr-1">
                <span className="font-medium text-sm text-gray-700">
                  Filter:
                </span>
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="select select-bordered select-sm"
                >
                  <option value="all">All</option>
                  <option value="unread">Unread</option>
                  <option value="survey">Fill Survey</option>
                </select>
              </div>

              <button
                onClick={markAllAsRead}
                className="btn btn-sm btn-primary mt-3 sm:mt-0"
              >
                Mark All as Read
              </button>
            </div>
          )}

          {/* Main Content */}
          <main className="w-full">
            {/* Error or Loaded State */}
            {error && (
              <p className="text-red-500 text-center mb-4">
                Error fetching notifications: {error}
              </p>
            )}

            {filteredNotifications.length > 0 ? (
              <div className="space-y-6">
                {filteredNotifications.map((notification) => (
                  <NotificationItem
                    key={notification.id}
                    notification={notification}
                    markAsRead={markAsRead}
                    siteVisit={siteVisitsMap[notification.site_visit_id]}
                  />
                ))}
              </div>
            ) : !hasNotifications ? (
              <EmptyState />
            ) : (
              <div className="text-center py-6 text-gray-600 font-medium">
                No notifications in this category.
              </div>
            )}
          </main>
        </div>
      </div>
    </Sidebar>
  );
};

export default Notifications;
