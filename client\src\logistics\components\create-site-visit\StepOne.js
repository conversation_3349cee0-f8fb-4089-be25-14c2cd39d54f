import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { ChevronRight } from "lucide-react";

function StepOne({
  siteId,
  setSiteId,
  siteName,
  setSiteName,
  pickupLocation,
  setPickupLocation,
  pickupDate,
  setPickupDate,
  pickupTime,
  setPickupTime,
  onNext,
}) {
  const [sites, setSites] = useState([]);
  const [selfDrive, setSelfDrive] = useState(false);

  const token = useSelector((state) => state.user?.token);

  useEffect(() => {
    // Fetch sites from your endpoint
    const fetchSites = async () => {
      try {
        const response = await fetch(
          "https://workspace.optiven.co.ke/api/sites",
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const data = await response.json();
        setSites(data);
      } catch (err) {
        console.error("Error fetching sites:", err);
      }
    };
    fetchSites();
  }, [token]);

  const handleSiteChange = (e) => {
    const selectedId = e.target.value;
    setSiteId(selectedId);

    
    const found = sites.find((s) => String(s.project_id) === selectedId);

   
    if (found) {
     console.log("Selected site project_id:", found.project_id);
     
    }
  
    setSiteName(found ? found.name : "");
  };

  const handleSelfDriveChange = (e) => {
    const isChecked = e.target.checked;
    setSelfDrive(isChecked);
    if (isChecked) {
      setPickupLocation("Self Drive");
    } else {
      setPickupLocation("");
    }
  };

  return (
    <div className="space-y-4">
      <div className="form-control">
        <label className="label">
          <span className="label-text">Site Name</span>
        </label>
        <select
          className="select select-bordered"
          value={siteId}
          onChange={handleSiteChange}
        >
          <option value="">Select a site</option>
          {sites.map((site) => (
            <option key={site.project_id} value={site.project_id}>
              {site.name}
            </option>
          ))}
        </select>
      </div>

      {/* Pickup Location */}
      <div className="form-control">
        <label className="label">
          <span className="label-text">Pickup Location</span>
        </label>
        <input
          type="text"
          className="input input-bordered"
          placeholder="e.g. ABSA Towers"
          disabled={selfDrive}
          value={selfDrive ? "Self Drive" : pickupLocation}
          onChange={(e) => setPickupLocation(e.target.value)}
        />
        {/* Self-Drive Checkbox */}
        <div className="flex items-center mt-2">
          <input
            type="checkbox"
            className="checkbox"
            checked={selfDrive}
            onChange={handleSelfDriveChange}
          />
          <span className="text-sm font-bold ml-2">
            Client(s) opted for self-drive
          </span>
        </div>
      </div>

      {/* Pickup Date & Time */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="form-control">
          <label className="label">
            <span className="label-text">Pickup Date</span>
          </label>
          <input
            type="date"
            className="input input-bordered"
            value={pickupDate}
            onChange={(e) => setPickupDate(e.target.value)}
          />
        </div>

        <div className="form-control">
          <label className="label">
            <span className="label-text">Pickup Time</span>
          </label>
          <input
            type="time"
            className="input input-bordered"
            value={pickupTime}
            onChange={(e) => setPickupTime(e.target.value)}
          />
        </div>
      </div>

      {/* Next button */}
      <div className="flex justify-end">
        <button
          type="button"
          onClick={onNext}
          className="btn btn-primary rounded-full text-white"
        >
          <ChevronRight />
        </button>
      </div>
    </div>
  );
}

export default StepOne;
