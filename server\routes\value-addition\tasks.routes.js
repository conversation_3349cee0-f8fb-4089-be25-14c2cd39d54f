const express = require("express");
const router = express.Router();
const authenticateJWT = require("../../middleware/authenticateJWT");
module.exports = (pool, ValueAddition) => {
 
// automatically fetch tasks through fetching them from value addition_level_subtasks table
router.get("/:valueAdditionLevelId/value-addition-subtasks", (req, res) => {
  const valueAdditionLevelId = req.params.valueAdditionLevelId;

  // Query the value_addition_level_subtasks table to get the subtask IDs associated with the value addition level
  pool.query(
      "SELECT value_addition_subtasks_id FROM value_addition_level_subtasks WHERE value_addition_level_id = ?",
      [valueAdditionLevelId],
      (err, levelSubtasksResult) => {
          if (err) {
              console.error("Error fetching value addition level subtasks:", err);
              res.status(500).json({ message: "Error fetching value addition level subtasks" });
          } else {
              // Extract the subtask IDs from the result
              const subtaskIds = levelSubtasksResult.map(result => result.value_addition_subtasks_id);

              if (subtaskIds.length === 0) {
                  // If there are no subtask IDs, respond with an empty array
                  res.status(200).json([]);
              } else {
                  // Query the value_addition_subtasks table to get the subtasks corresponding to the extracted subtask IDs
                  pool.query(
                      "SELECT * FROM value_addition_subtasks WHERE id IN (?)",
                      [subtaskIds],
                      (err, subtasksResult) => {
                          if (err) {
                              console.error("Error fetching value addition subtasks:", err);
                              res.status(500).json({ message: "Error fetching value addition subtasks" });
                          } else {
                              res.status(200).json(subtasksResult);
                          }
                      }
                  );
              }
          }
      }
  );
});
// routes to get tasks for specific value Addition through value addition id
router.get("/:valueAdditionId/value-addition-tasks", (req, res) => {
  const valueAdditionId = req.params.valueAdditionId;
  try {
    pool.query(
      "SELECT * FROM task WHERE valueAdditionId = ?",
      [valueAdditionId],
      (err, result) => {
        if (err) {
          console.error(err);
          return res.status(500).json({ message: "Error fetching data" });
        }
        res.status(200).json(result);
      }
    );
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error fetching data" });
  }
});

// get value-addition subtasks
// Express route
router.get("/value-addition-subtasks/:taskTitleId",authenticateJWT,(req,res) =>{
  const taskTitleId = req.params.taskTitleId;
  try{
    pool.query("SELECT * FROM value_addition_subtasks WHERE id =? ",[taskTitleId],(err,result) =>{
      if (err) throw err;
      res.status(200).json(result);
    });
  } catch(error) {
    console.error(error);
    res.status(500).json({message:"Error fetching Data"});
  }
});
// calculate the total cost of tasks per valueAddition
router.get("/:id/tasks-cost",authenticateJWT, (req, res) => {
  try {
    const valueAdditionId = req.params.id;

    if (!valueAdditionId) {
      return res.status(400).json({ message: "Missing valueAdditionId parameter" });
    }

    pool.query(
      "SELECT valueAdditionId, SUM(cost) AS totalCost FROM task WHERE valueAdditionId = ? GROUP BY valueAdditionId",
      [valueAdditionId],
      (err, result) => {
        if (err) {
          console.error(err);
          return res.status(500).json({ message: "Error while fetching data" });
        }
        
        res.status(200).json(result);
      }
    );
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error while fetching data" });
  }
});
// route to check if all the tasks are set to status completed and then update the value addition as completed.
router.get("/:id/all-completed",authenticateJWT,async (req, res) => {
  try {
    const valueAdditionId = req.params.id;

    // Query the database to check if all statuses are completed
    const query = "SELECT COUNT(*) AS totalTasks, SUM(CASE WHEN status = 'Completed' THEN 1 ELSE 0 END) AS completedTasks FROM task WHERE valueAdditionId = ?";
    const [result] = await pool.promise().query(query, [valueAdditionId]);

    

    // Check if all tasks are completed
    if (result && result.length > 0) {
      const totalTasks = result[0].totalTasks;
      const completedTasks = result[0].completedTasks;
      
      if (parseInt(totalTasks > 0 && totalTasks) === parseInt(completedTasks)) {
        res.status(200).json({ message: "Completed" });
      } else {
        res.status(200).json({ message: "Pending tasks" });
      }
    } else {
      res.status(200).json({ message: "Pending Tasks" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error while checking completed status" });
  }
});






 //completed handle status
 router.patch("/:id/completed",authenticateJWT,  (req, res) => {
  try {
    const { status } = req.body;
    const id = req.params.id;

    // Execute the UPDATE query and await the result
    const result =  pool.query(
      "UPDATE task SET status = ? WHERE id = ?",
      [status, id]
    );

    // Check if any rows were affected by the update
    if (result.affectedRows === 0) {
      return res.status(404).json({ message: "task not found" });
    }

    // If the update was successful, send a success response
    res.json({ message: "task updated successfully" });
  } catch (error) {
    // If an error occurs, log it and send a 500 (Internal Server Error) response
    console.error(error);
    res.status(500).json({ message: "An error occurred" });
  }
});
 // stalled status
 router.patch("/:id/stalled",authenticateJWT, (req, res) => {
  try {
    const { status } = req.body;
    const id = req.params.id;

    // Execute the UPDATE query and await the result
    const result =  pool.query(
      "UPDATE task SET status = ? WHERE id = ?",
      [status, id]
    );

    // Check if any rows were affected by the update
    if (result.affectedRows === 0) {
      return res.status(404).json({ message: "task not found" });
    }

    // If the update was successful, send a success response
    res.json({ message: "task updated successfully" });
  } catch (error) {
    // If an error occurs, log it and send a 500 (Internal Server Error) response
    console.error(error);
    res.status(500).json({ message: "An error occurred" });
  }
});

  // Get all tasks for a value addition
  
  router.get("/",authenticateJWT,async (req, res) => {
    try {
      pool.query(
        "SELECT * FROM task ",
        (err, results) => {
          if (err) throw err;

          res.json(results);
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching tasks.",
      });
    }
  });

  // Get one task
  router.get("/:id",authenticateJWT, async (req, res) => {
    try {
      const task = await pool
        .promise()
        .query("SELECT * FROM task WHERE id = ?", [req.params.id]);

      if (task.length === 0) {
        return res.status(404).json({ message: "Task not found" });
      }

      res.json(task[0][0]);
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });

  // Create a task
  router.post("/", authenticateJWT,async (req, res) => {
    const {
      title,
      
      status,
     
      cost,
      startDate,
      expectedDateOfCompletion,
      
      valueAdditionId,
    } = req.body;

    try {
      pool.query(
        "INSERT INTO task (title,  status,  cost, startDate, expectedDateOfCompletion, valueAdditionId) VALUES (?, ?, ?, ?, ?, ?)",
        [
          title,
          status,
          cost,
          startDate,
          expectedDateOfCompletion,
          valueAdditionId,
        ]
      );
      const newTask = {
        id: result.insertId,
        title,
        status,
        cost,
        startDate,
        expectedDateOfCompletion,
        valueAdditionId,
      };

      const valueAddition = await ValueAddition.findById(valueAdditionId);
      if (valueAddition) {
        valueAddition.tasks.push(result.insertId);
        await valueAddition.save();
      }
      res.status(201).json(newTask);
    } catch (error) {
      res.status(400).send(error);
    }
  });



  // Update a task
  router.patch("/:id", authenticateJWT,async (req, res) => {
    const {
      title,
      status, 
      cost,
      startDate,
      expectedDateOfCompletion,
    } = req.body;

    try {
      const result = await pool
        .promise()
        .query(
          "UPDATE task SET title=?,  status=?, cost=?, startDate=?, expectedDateOfCompletion=? WHERE id=?",
          [
            title,
            status,
            cost,
            startDate,
            expectedDateOfCompletion,
            req.params.id,
          ]
        );

      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Task not found" });
      }

      res.json({ message: "Task updated successfully" });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });

  // Delete a task
  router.delete("/:id", authenticateJWT,async (req, res) => {
    try {
      const result = await pool
        .promise()
        .query("DELETE FROM task WHERE id=?", [req.params.id]);

      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Task not found" });
      }
      res.json({ message: "Task deleted successfully" });
    } catch (err) {
      console.error(err);
      res.status(500).send("Server error");
    }
  });

  return router;
};
