const express = require("express");
const { body, validationResult } = require("express-validator");
const authenticateJWT = require("../../middleware/authenticateJWT");
const router = express.Router();

module.exports = (pool) => {
  // Get the current campaign
  router.get("/", async (req, res) => {
    try {
      pool.query("SELECT * FROM campaigns LIMIT 1", (err, results) => {
        if (err) throw err;
        if (results.length === 0) {
          return res.status(404).json({ message: "No campaign found." });
        }
        res.json(results[0]);
      });
    } catch (error) {
      console.error("Error fetching campaign:", error);
      res.status(500).json({
        message: "An error occurred while fetching the campaign.",
      });
    }
  });

  // Update the current campaign
  router.put(
    "/",

    [
      body("title").optional().isString(),
      body("description").optional().isString(),
      body("banner_image_url")
        .optional()
        .isURL()
        .withMessage("Banner image URL must be valid"),
      body("month")
        .optional()
        .isInt({ min: 1, max: 12 })
        .withMessage("Month must be between 1 and 12"),
      body("year")
        .optional()
        .isInt({ min: 2000 })
        .withMessage("Year must be a valid year"),
      body("featured").optional().isBoolean(),
      body("link").optional().isURL().withMessage("Link must be a valid URL"),
    ],
    async (req, res) => {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const {
        title,
        description,
        banner_image_url,
        month,
        year,
        featured,
        link,
      } = req.body;

      try {
        const fields = [];
        const values = [];

        if (typeof title !== "undefined") {
          fields.push("title = ?");
          values.push(title);
        }
        if (typeof description !== "undefined") {
          fields.push("description = ?");
          values.push(description);
        }
        if (typeof banner_image_url !== "undefined") {
          fields.push("banner_image_url = ?");
          values.push(banner_image_url);
        }
        if (typeof month !== "undefined") {
          fields.push("month = ?");
          values.push(month);
        }
        if (typeof year !== "undefined") {
          fields.push("year = ?");
          values.push(year);
        }
        if (typeof featured !== "undefined") {
          fields.push("featured = ?");
          values.push(featured);
        }
        if (typeof link !== "undefined") {
          fields.push("link = ?");
          values.push(link);
        }

        if (fields.length === 0) {
          return res.status(400).json({ message: "No fields to update." });
        }

        const sql = `UPDATE campaigns SET ${fields.join(
          ", "
        )} ORDER BY id DESC LIMIT 1`;

        pool.query(sql, values, (err, result) => {
          if (err) {
            console.error("Error updating campaign:", err);
            return res.status(500).json({
              message: "An error occurred while updating the campaign.",
            });
          }
          if (result.affectedRows === 0) {
            return res.status(404).json({ message: "Campaign not found." });
          }
          res.json({ message: "Campaign updated successfully." });
        });
      } catch (error) {
        console.error("Exception updating campaign:", error);
        res.status(500).json({
          message: "An error occurred while updating the campaign.",
        });
      }
    }
  );

  return router;
};
