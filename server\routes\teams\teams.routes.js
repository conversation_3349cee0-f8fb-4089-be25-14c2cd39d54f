const express = require("express");
const authenticateJWT = require("../../middleware/authenticateJWT");
const router = express.Router();

module.exports = (pool) => {
  // Get all teams
  router.get("/", async (req, res) => {
    try {
      // Query the database to get all teams
      const teams = await pool.query(
        "SELECT t.*, r.name as region, r.regional_manager FROM teams t JOIN region r WHERE t.region = r.id"
      );
      res.json(teams);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: "Unable to fetch teams" });
    }
  });

  router.get("/region-teams", async (req, res) => {
    try {
      const { teamIds } = req.query;

      // Query the database to get teams that the user has access to
      const teams = await pool.query("SELECT * FROM teams WHERE id IN (?)", [
        teamIds,
      ]);
      res.json(teams);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: "Unable to fetch teams" });
    }
  });

  // Get one team by ID
  router.get("/:id", async (req, res) => {
    const teamId = req.params.id;

    try {
      // Query the database to get one team by ID
      const team = await pool.query("SELECT * FROM teams WHERE id = ?", [
        teamId,
      ]);

      if (team.length === 0) {
        res.status(404).json({ error: "Team not found" });
      } else {
        res.json(team[0]);
      }
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: "Unable to fetch team" });
    }
  });

  // Create a new team (POST)
  router.post("/", async (req, res) => {
    const { name, team_leader, region } = req.body;

    try {
      // Insert a new team into the database
      await pool.query(
        "INSERT INTO teams (name, team_leader, region) VALUES (?, ?, ?)",
        [name, team_leader, region]
      );
      res.status(201).json({ message: "Team created successfully" });
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: "Unable to create a team" });
    }
  });

  // Update an existing team by ID (PUT)
  router.put("/:id", async (req, res) => {
    const teamId = req.params.id;
    const { name, team_leader, region } = req.body;

    try {
      // Update the team in the database
      await pool.query(
        "UPDATE teams SET name = ?, team_leader = ?, region = ? WHERE id = ?",
        [name, team_leader, region, teamId]
      );
      res.json({ message: "Team updated successfully" });
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: "Unable to update the team" });
    }
  });

  // Delete a team by ID (DELETE)
  router.delete("/:id", async (req, res) => {
    const teamId = req.params.id;

    try {
      // Delete the team from the database
      await pool.query("DELETE FROM teams WHERE id = ?", [teamId]);
      res.json({ message: "Team deleted successfully" });
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: "Unable to delete the team" });
    }
  });

  return router;
};
