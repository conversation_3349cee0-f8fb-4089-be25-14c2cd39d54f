const express = require("express");
const router = express.Router();
const nodemailer = require("nodemailer");

// Helper function to send emails
const sendEmail = async (userEmail, subject, text) => {
  try {
    let transporter = nodemailer.createTransport({
      host: "smtp.zoho.com",
      port: 465,
      secure: true,
      auth: {
        user: process.env.DOMAIN_EMAIL,
        pass: process.env.DOMAIN_PASSWORD,
      },
    });

    let info = await transporter.sendMail({
      from: '"Optiven Conference Platform 💂" <<EMAIL>>', // sender address
      to: userEmail, // list of receivers
      subject: subject, // Subject line
      text: text, // plain text body
    });
  } catch (error) {
    console.error("Error sending email:", error);
  }
};

module.exports = (pool) => {
  // GET all bookings
  router.get("/", (req, res) => {
    pool.query("SELECT * FROM conference.events_booking", (err, results) => {
      if (err) {
        return res.status(500).send({ message: "Error retrieving bookings: " + err });
      }
      res.send(results);
    });
  });

  // GET a single booking by ID
  router.get("/:id", (req, res) => {
    const { id } = req.params;
    pool.query(
      "SELECT * FROM conference.events_booking WHERE id = ?",
      [id],
      (err, result) => {
        if (err) {
          return res.status(500).send({ message: "Error retrieving booking: " + err });
        }
        if (result.length > 0) {
          res.send(result[0]);
        } else {
          res.status(404).send({ message: "Booking not found" });
        }
      }
    );
  });

  // POST a new booking
  router.post("/", (req, res) => {
    const { user_id, purpose, venue, date, description, selectedUsers } = req.body;

    if (!user_id || !purpose || !venue || !date || !description || !selectedUsers.length) {
      return res.status(400).send({ message: "All fields are required." });
    }

    // Fetch the user's email and full name from the database
    pool.query(
      "SELECT email, fullnames FROM defaultdb.users WHERE user_id = ?",
      [user_id],
      async (err, results) => {
        if (err) {
          console.error("Error retrieving user details:", err);
          return res.status(500).send({ message: "Error retrieving user details: " + err });
        }

        if (results.length === 0) {
          return res.status(404).send({ message: "User not found" });
        }

        const { email, fullnames } = results[0];

        // Check for booking conflicts in the same venue
        const conflictQuery = `
          SELECT * FROM conference.events_booking
          WHERE venue = ? AND date = ?
        `;

        pool.query(conflictQuery, [venue, date], (err, results) => {
          if (err) {
            console.error("Error checking for booking conflicts:", err);
            return res.status(500).send({
              message: "Error checking for booking conflicts: " + err,
            });
          }

          if (results.length > 0) {
            // Conflict found, send an error response
            return res.status(409).send({
              message: "Booking conflict detected. Please choose another date or venue.",
            });
          }

          // No conflicts, proceed to insert the booking
          const insertQuery = `
            INSERT INTO conference.events_booking (user_id, purpose, venue, date, description, selected_users)
            VALUES (?, ?, ?, ?, ?, ?)
          `;
          pool.query(
            insertQuery,
            [user_id, purpose, venue, date, description, JSON.stringify(selectedUsers)],
            async (err, result) => {
              if (err) {
                console.error("Error adding booking:", err);
                return res.status(500).send({ message: "Error adding booking: " + err });
              }

              // Send confirmation email to the user who created the booking
              await sendEmail(
                email,
                "Booking Confirmation",
                `Hi there,

                We are pleased to confirm your booking for ${date} at ${venue}.

                This event was created by ${fullnames}.

                Thank you for choosing our services. If you have any questions or need further assistance, please do not hesitate to contact us.

                Best regards,
                ${fullnames}.`
              );

              if (selectedUsers.length > 0) {
                // Fetch emails of selected users from the database
                pool.query(
                  `SELECT email FROM defaultdb.users WHERE user_id IN (${selectedUsers.join(",")})`,
                  [selectedUsers],
                  async (err, userResults) => {
                    if (err) {
                      console.error("Error retrieving selected users' emails:", err);
                      return res.status(500).send({ message: "Error retrieving selected users' emails: " + err });
                    }

                    const selectedUserEmails = userResults.map((user) => user.email);

                    // Send email to each selected user
                    for (const userEmail of selectedUserEmails) {
                      await sendEmail(
                        userEmail,
                        "Event Notification",
                        `Hi There,

                        You have been invited to an event with the following details:

                        Purpose: ${purpose}
                        Venue: ${venue}
                        Date: ${date}
                        Description: ${description}

                        This event was created by ${fullnames}.

                        Please make necessary arrangements to attend the event.

                        Best regards,
                        ${fullnames}.`
                      );
                    }

                    res.status(201).send({
                      message: "Booking added successfully and emails sent to selected users",
                      bookingId: result.insertId,
                    });
                  }
                );
              } else {
                res.status(201).send({
                  message: "Booking added successfully",
                  bookingId: result.insertId,
                });
              }
            }
          );
        });
      }
    );
  });

  // PUT to update a booking by ID
  router.put("/:id", (req, res) => {
    const { id } = req.params;
    const { user_id, purpose, venue, date, description, selectedUsers } = req.body;

    if (!user_id || !purpose || !venue || !date || !description || !selectedUsers.length) {
      return res.status(400).send({ message: "All fields are required." });
    }

    pool.query(
      "UPDATE conference.events_booking SET user_id = ?, purpose = ?, venue = ?, date = ?, description = ?, selected_users = ? WHERE id = ?",
      [user_id, purpose, venue, date, description, JSON.stringify(selectedUsers), id],
      (err, result) => {
        if (err) {
          return res.status(500).send({ message: "Error updating booking: " + err });
        }
        if (result.affectedRows == 0) {
          return res.status(404).send({ message: "Booking not found" });
        }
        res.send({ message: "Booking updated successfully" });
      }
    );
  });

  // DELETE a booking by ID
  router.delete("/:id", (req, res) => {
    const { id } = req.params;
    pool.query(
      "DELETE FROM conference.events_booking WHERE id = ?",
      [id],
      (err, result) => {
        if (err) {
          return res.status(500).send({ message: "Error deleting booking: " + err });
        }
        if (result.affectedRows == 0) {
          return res.status(404).send({ message: "Booking not found" });
        }
        res.send({ message: "Booking deleted successfully" });
      }
    );
  });

  return router;
};
