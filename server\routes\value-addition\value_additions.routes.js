const express = require("express");
const router = express.Router();
const pdfMakePrinter = require("pdfmake/src/printer");
const authenticateJWT = require("../../middleware/authenticateJWT");
function formatDate(inputDate) {
  const date = new Date(inputDate);

  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0"); // Months are zero-based
  const day = date.getDate().toString().padStart(2, "0");

  return `${year}-${month}-${day}`;
}

const fonts = {
  Roboto: {
    normal: "node_modules/roboto-font/fonts/Roboto/roboto-regular-webfont.ttf",
    bold: "node_modules/roboto-font/fonts/Roboto/roboto-bold-webfont.ttf",
    italic: "node_modules/roboto-font/fonts/Roboto/roboto-italic-webfont.ttf",
    bolditalics:
      "node_modules/roboto-font/fonts/Roboto/roboto-bolditalic-webfont.ttf",
  },
};
// Create a new printer with the fonts
const printer = new pdfMakePrinter(fonts);

// Function to format data into PDF rows
function dataToPdfRows(data) {
  const pdfRows = [];

  // Iterate over the data and map it to rows
  let totalBudget = 0; // Initialize total budget

  data.forEach((item) => {
    const taskCost = parseFloat(item.task_cost);
    if (!isNaN(taskCost)) {
        totalBudget += taskCost;
        pdfRows.push([
            { text: item.project_name, style: "tableCell" },
            { text: item.value_addition_title, style: "tableCell" },
            { text: item.value_addition_budget.toString(), style: "tableCell" },
            { text: formatDate(item.value_addition_startDate.toString()), style: "tableCell" },
            { text: formatDate(item.value_addition_endDate.toString()), style: "tableCell" },
            { text: item.task_title, style: "tableCell" },
            { text: item.task_status, style: "tableCell" },
            { text: taskCost.toString(), style: "tableCell" },
        ]);
    } else {
        console.warn(`Invalid task cost: ${item.task_cost}`);
    }
});

pdfRows.push([
    { text: "Total Budget", style: "tableCell", colSpan: 6 },
    {}, {}, {}, {}, {}, {},
    { text: isNaN(totalBudget) ? "N/A" : totalBudget.toString(), style: "tableCell" },
]);

return pdfRows;

}

module.exports = (pool, Project) => {
  // get value addition cost and payments cost
  router.get("/cost",authenticateJWT, (req, res) => {
    try {
      const query = `
      SELECT 
    p.project_name AS project_name,
    COALESCE(va.projectId, p.id) AS project_Id,
    COALESCE(va.total_value_addition_budget, 0) AS total_value_addition_budget,
    COALESCE(py.total_payments, 0) AS total_payments,
    COALESCE(sp.total_supplier_payments, 0) AS total_supplier_payments,
    (COALESCE(va.total_value_addition_budget, 0) + COALESCE(py.total_payments, 0) + COALESCE(sp.total_supplier_payments, 0)) AS total_amount
FROM 
    projects p
LEFT JOIN 
    (SELECT projectId, SUM(budget) AS total_value_addition_budget
     FROM value_addition
     GROUP BY projectId) va ON p.id = va.projectId
LEFT JOIN 
    (SELECT project_data, SUM(total_amount) AS total_payments
     FROM payments
     GROUP BY project_data) py ON p.id = py.project_data
LEFT JOIN 
    (SELECT projectId, SUM(amount) AS total_supplier_payments
     FROM supplier s
     JOIN supplier_payments sp ON s.id = sp.supplier_id AND sp.status ='PAID'
     GROUP BY projectId) sp ON p.id = sp.projectId
ORDER BY 
    total_amount DESC;
  `;

      pool.query(query, (err, result) => {
        if (err) throw err;
        res.status(200).json(result);
      });
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  // Generating report route
  router.get("/report", authenticateJWT,(req, res) => {
    let vaQuery = `SELECT 
        va.title AS value_addition_title,
        va.budget AS value_addition_budget,
        va.startDate AS value_addition_startDate,
        va.endDate  AS value_addition_endDate,
        t.status AS task_status,
        t.title AS task_title,
        t.cost AS task_cost,
        p.project_name AS project_name 
        FROM value_addition va 
        LEFT JOIN task t ON va.id = t.valueAdditionId
        LEFT JOIN projects p ON va.projectId = p.id`;

    const { project_name } = req.query;

    if (project_name) {
      // Filter by project_name if provided
      vaQuery += ` WHERE p.project_name = '${project_name}'`;
    }

    try {
      // Execute the SQL query
      pool.query(vaQuery, (err, result) => {
        if (err) {
          console.error(err);
          res
            .status(500)
            .json({ error: "An error occurred while fetching data." });
        } else {
          // Define the document definition for the PDF
          const docDefinition = {
            pageSize: "A4",
            pageOrientation: "landscape",
            content: [
              {
                table: {
                  headerRows: 1,
                  widths: ["*", "*", "*", "*", "*", "*", "*","*"], // Adjust column widths as needed
                  body: [
                    [
                      { text: "Project Name", style: "tableHeader" },
                      { text: "Value Addition Title", style: "tableHeader" },
                      { text: "Value Addition Budget", style: "tableHeader" },
                      {
                        text: "Value Addition start Date",
                        style: "tableHeader",
                      },
                      { text: "Value Addition End Date", style: "tableHeader" },
                      { text: "Task Title", style: "tableHeader" },
                      { text: "Task Status", style: "tableHeader" },
                      { text: "Task Actual cost", style: "tableHeader" },
                    ],
                    ...dataToPdfRows(result), // Add data rows
                  ],
                },
              },
            ],
            styles: {
              tableHeader: {
                bold: true,
                fontSize: 12,
                fillColor: "#CCCCCC", // Header background color
              },
              tableCell: {
                fontSize: 10,
              },
            },
          };

          // Create the PDF document using pdfmake
          const pdfDoc = printer.createPdfKitDocument(docDefinition);

          // Set the response headers to indicate a PDF file
          res.setHeader("Content-Type", "application/pdf");

          // Stream the PDF document as the response
          pdfDoc.pipe(res);
          pdfDoc.end();
        }
      });
    } catch (error) {
      console.error(error);
      res
        .status(500)
        .json({ error: "An error occurred while processing the request." });
    }
  });

  // get *one* value addition
  router.get("/:id", authenticateJWT,async (req, res) => {
    try {
      const valueAddition = await pool
        .promise()
        .query("SELECT * FROM value_addition WHERE id = ?", [req.params.id]);

      if (valueAddition.length === 0) {
        return res.status(404).json({ message: "Value addition not found" });
      }

      res.json(valueAddition[0][0]);
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });
  // get all value additions
  router.get("/", authenticateJWT,async (req, res) => {
    try {
      pool.query("SELECT * FROM value_addition", (err, results) => {
        if (err) throw err;

        res.json(results);
      });
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching value_additions.",
      });
    }
  });

  // create a value addition
  router.post("/",authenticateJWT, async (req, res) => {
    const {
      title,
      details,
      budget,
      construction_manager,
      service_provider,
      startDate,
      endDate,
      plot,
      projectId,
      value_addition_level_id
    } = req.body;

    try {
      const result =await pool.promise().query(
        "INSERT INTO value_addition (title, details, budget, construction_manager,service_provider, startDate, endDate,plot, projectId,value_addition_level_id) VALUES (?, ?, ?, ?, ?, ?, ?,?,?,?)",
        [
          title,
          details,
          budget,
          construction_manager,
          service_provider,
          startDate,
          endDate,
          plot,
          projectId,
          value_addition_level_id
        ]
      );
      
      const newValueAddition = {
        id: result.insertId,
        title,
        details,
        plot,
        budget,
        construction_manager,
        service_provider,
        startDate,
        endDate,
        projectId,
        value_addition_level_id
      };

      const project = await Project.findById(projectId);
      if (project) {
        project.valueAdditions.push(result.insertId);
        await project.save();
      }

      res.status(201).json(newValueAddition);
    } catch (error) {
      res.status(400).json({ error: "Failed to create value addition" });
    }
  });

  // update a value addition
  router.patch("/:id",authenticateJWT,async (req, res) => {
    const {
      title,
      details,
      budget,
      construction_manager,
      service_provider,
      startDate,
      endDate,
      plot,
    } = req.body;

    try {
      const result = await pool
        .promise()
        .query(
          "UPDATE value_addition SET title=?, details=?, budget=?, construction_manager=?,service_provider=?, startDate=?, endDate=?,plot=? WHERE id=?",
          [
            title,
            details,
            budget,
            construction_manager,
            service_provider,
            startDate,
            endDate,
            plot,
            req.params.id,
          ]
        );

      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Value addition not found" });
      }

      res.json({ message: "Value addition updated successfully" });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });
 
 

  // delete a value addition

  router.delete("/:id", authenticateJWT,async (req, res) => {
    try {
      // Check if the value_addition exists
      const checkValueAddition = await pool
        .promise()
        .query("SELECT id FROM value_addition WHERE id=?", [req.params.id]);

      if (checkValueAddition[0].length === 0) {
        return res.status(404).json({ message: "value_addition not found" });
      }

      // Delete associated records in the tasks table
      await pool
        .promise()
        .query("DELETE FROM task WHERE valueAdditionId=?", [req.params.id]);

      // Now, delete the value_addition
      const result = await pool
        .promise()
        .query("DELETE FROM value_addition WHERE id=?", [req.params.id]);

      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "value_addition not found" });
      }

      res.json({ message: "value_addition deleted successfully" });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  });

  return router;
};
