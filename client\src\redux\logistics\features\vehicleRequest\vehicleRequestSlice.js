import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

const initialState = {
  requests: [],
  activeRequests: [],
  pendingVehicleRequests: [],
  status: "idle",
  error: null,
};

export const fetchActiveVehicleRequests = createAsyncThunk(
  "vehicleRequest/fetchActiveVehicleRequests",
  async (_, { getState, rejectWithValue }) => {
    const token = getState().user.token;
    const userId = getState().user.user.user_id;
    try {
      const response = await axios.get(
        `https://workspace.optiven.co.ke/api/vehicle-requests/active/active?user_id=${userId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      console.log("Server error:", error.response);
      return rejectWithValue(
        error.response?.data || "Error fetching active vehicle requests"
      );
    }
  }
);

export const fetchPendingVehicleRequests = createAsyncThunk(
  "vehicleRequest/fetchPendingVehicleRequests",
  async (_, { getState, rejectWithValue }) => {
    const token = getState().user.token;
    try {
      const response = await axios.get(
        "https://workspace.optiven.co.ke/api/vehicle-requests/pending-vehicle-requests",
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      console.log("Server error:", error.response);
      return rejectWithValue(
        error.response?.data || "Error fetching pending vehicle requests"
      );
    }
  }
);

export const approveVehicleRequest = createAsyncThunk(
  "vehicleRequest/approveVehicleRequest",
  async ({ id, data }, { getState, rejectWithValue }) => {
    try {
      const token = getState().user.token;
      const response = await axios.patch(
        `https://workspace.optiven.co.ke/api/vehicle-requests/pending-vehicle-requests/${id}`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data || "Error approving vehicle request"
      );
    }
  }
);

const vehicleRequestSlice = createSlice({
  name: "vehicleRequest",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // fetchActiveVehicleRequests
      .addCase(fetchActiveVehicleRequests.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchActiveVehicleRequests.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.activeRequests = action.payload;
      })
      .addCase(fetchActiveVehicleRequests.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload;
      })

      // approveVehicleRequest
      .addCase(approveVehicleRequest.pending, (state) => {
        state.status = "loading";
      })
      .addCase(approveVehicleRequest.fulfilled, (state, action) => {
        state.status = "succeeded";
        // Find and update the request in pendingVehicleRequests:
        const updatedRequest = action.payload;
        state.pendingVehicleRequests = state.pendingVehicleRequests.map((req) =>
          req.id === updatedRequest.id ? updatedRequest : req
        );
      })
      .addCase(approveVehicleRequest.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload;
      })

      // fetchPendingVehicleRequests
      .addCase(fetchPendingVehicleRequests.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchPendingVehicleRequests.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.pendingVehicleRequests = action.payload;
      })
      .addCase(fetchPendingVehicleRequests.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload;
      });
  },
});

// Example selector for your pendingVehicleRequests array
export const selectPendingVehicleRequests = (state) =>
  state.vehicleRequest.pendingVehicleRequests;

export default vehicleRequestSlice.reducer;
