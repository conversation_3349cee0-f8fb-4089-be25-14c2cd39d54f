// common.js

function findMostRepeatedValueFromArray(array) {
  if (!Array.isArray(array) || array.length === 0) {
    throw new Error("Input should be a valued array.");
  }

  // Create a frequency map
  const frequencyMap = new Map();
  array.forEach((value) => {
    frequencyMap.set(value, (frequencyMap.get(value) || 0) + 1);
  });

  // Find the most repeated value
  let mostRepeatedValue;
  let highestFrequency = 0;

  frequencyMap.forEach((frequency, value) => {
    if (frequency > highestFrequency) {
      highestFrequency = frequency;
      mostRepeatedValue = value;
    }
  });

  return mostRepeatedValue;
}

module.exports = {
  findMostRepeatedValueFromArray,
};
