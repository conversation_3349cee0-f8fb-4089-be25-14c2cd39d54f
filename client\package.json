{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@fullcalendar/core": "^6.1.8", "@fullcalendar/daygrid": "^6.1.8", "@fullcalendar/interaction": "^6.1.8", "@fullcalendar/list": "^6.1.8", "@fullcalendar/react": "^6.1.8", "@fullcalendar/resource": "^6.1.8", "@fullcalendar/resource-timeline": "^6.1.8", "@fullcalendar/timegrid": "^6.1.8", "@reduxjs/toolkit": "^1.9.3", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "aos": "^2.3.4", "axios": "^1.3.4", "buffer": "^6.0.3", "chart.js": "^4.4.4", "daisyui": "^2.51.5", "date-fns": "^2.30.0", "feather-icons": "^4.29.1", "file-saver": "^2.0.5", "formik": "^2.4.6", "framer-motion": "^10.18.0", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.3", "leaflet": "^1.9.4", "leaflet-search": "^4.0.0", "lucide-react": "^0.439.0", "moment": "^2.29.4", "pdfmake": "^0.2.10", "postcss": "^8.4.32", "react": "^18.2.0", "react-big-calendar": "^1.13.4", "react-chartjs-2": "^5.2.0", "react-csv": "^2.2.2", "react-data-table-component": "^7.7.0", "react-datepicker": "^7.3.0", "react-dom": "^18.2.0", "react-feather": "^2.0.10", "react-hook-form": "^7.49.2", "react-icons": "^5.3.0", "react-loader-spinner": "^6.1.6", "react-modal": "^3.16.1", "react-redux": "^8.0.5", "react-responsive-carousel": "^3.2.23", "react-router-dom": "^6.9.0", "react-scripts": "5.0.1", "react-select": "^5.8.0", "react-time-picker": "^7.0.0", "react-toastify": "^9.1.2", "react-use-measure": "^2.1.1", "recharts": "^2.10.3", "redux-persist": "^6.0.0", "socket.io-client": "^4.6.1", "styled-components": "^6.1.0", "use-debounce": "^10.0.4", "victory": "^36.7.0", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "yup": "^1.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "tailwindcss": "^3.3.6"}}