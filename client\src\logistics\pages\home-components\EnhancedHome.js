import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import moment from "moment";
import { Tail<PERSON><PERSON> } from "react-loader-spinner";
import { toast } from "react-toastify";
import { CircleAlert, NotebookPen, MapPinned, BusFront, Bus } from "lucide-react";

import Sidebar from "../../components/Sidebar";
import {
  fetchActiveSiteVisits,
  selectActiveSiteVisits,
} from "../../../redux/logistics/features/siteVisit/siteVisitSlice";
import { fetchNotifications } from "../../../redux/logistics/features/notifications/notificationsSlice";
import formatToAMPM from "../../../utils/formatToAMPM";

const EnhancedHome = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { user, token, accessRole } = useSelector((state) => state.user);
  const activeVisits = useSelector(selectActiveSiteVisits);
  const siteVisitStatus = useSelector((state) => state.siteVisit.status);

  const accessRoles = accessRole.split("#");
  const isMarketer = accessRoles.includes("113");
  const isAdmin = accessRoles.includes("logisticsAdmin");
  const isDriver = accessRoles.includes("driver");

  const [canBookSiteVisit, setCanBookSiteVisit] = useState(true);
  const [latestNotification, setLatestNotification] = useState(null);
  const [allUserSiteVisits, setAllUserSiteVisits] = useState([]);
  const [isLoadingFullData, setIsLoadingFullData] = useState(false);

  const userId = user?.user_id || user?.id || "";
  const firstName = user?.fullnames
    ? user.fullnames.trim().split(" ")[0]
    : "User";

  const hour = new Date().getHours();
  let greeting;
  if (hour < 12) {
    greeting = "Good morning,";
  } else if (hour >= 12 && hour < 17) {
    greeting = "Good afternoon,";
  } else {
    greeting = "Good evening,";
  }

  useEffect(() => {
    dispatch(fetchActiveSiteVisits());

    dispatch(fetchNotifications())
      .unwrap()
      .then((notificationsData) => {
        const latest = notificationsData.notifications[0];
        setLatestNotification(latest);

        // Example condition from your existing logic
        if (
          latest &&
          latest.type === "approved" &&
          moment().diff(latest.timestamp, "hours") > 24
        ) {
          setCanBookSiteVisit(false);
        }
      });

    fetchFullSiteVisits();
  }, [dispatch, token, userId]);

  useEffect(() => {
    dispatch(fetchActiveSiteVisits());
  }, [dispatch]);

  const fetchFullSiteVisits = async () => {
    try {
      setIsLoadingFullData(true);
      const response = await fetch(
        "https://workspace.optiven.co.ke/api/site-visit-requests",
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const data = await response.json();
      const userSpecificVisits = data.filter(
        (visit) => visit.marketer_id === userId
      );
      setAllUserSiteVisits(userSpecificVisits);
    } catch (error) {
      console.error("Error fetching site visits:", error);
      toast.error("Failed to load site visits. Please try again later.", {
        position: "top-center",
        autoClose: 3000,
      });
    } finally {
      setIsLoadingFullData(false);
    }
  };

  const startTrip = async (id) => {
    try {
      const response = await fetch(
        `https://workspace.optiven.co.ke/api/site-visits/start-trip/${id}`,
        {
          method: "PATCH",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        setAllUserSiteVisits((prev) =>
          prev.map((sv) =>
            sv.id === id ? { ...sv, status: "in_progress" } : sv
          )
        );
        toast.success("Trip set to in progress.", {
          position: "top-center",
          autoClose: 3000,
        });
      } else {
        const data = await response.json();
        toast.error("An error occurred while attempting to start trip.", {
          position: "top-center",
          autoClose: 3000,
        });
        console.error("Error starting trip:", data.message);
      }
    } catch (error) {
      toast.error("An error occurred while attempting to start trip.", {
        position: "top-center",
        autoClose: 3000,
      });
      console.error("Error starting trip:", error);
    }
  };

  const endTrip = async (id) => {
    try {
      const response = await fetch(
        `https://workspace.optiven.co.ke/api/site-visits/end-trip/${id}`,
        {
          method: "PATCH",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        setAllUserSiteVisits((prev) =>
          prev.map((sv) => (sv.id === id ? { ...sv, status: "complete" } : sv))
        );
        toast.success("Trip set to complete.", {
          position: "top-center",
          autoClose: 3000,
        });
      } else {
        const data = await response.json();
        toast.error("An error occurred while attempting to end trip.", {
          position: "top-center",
          autoClose: 3000,
        });
        console.error("Error ending trip:", data.message);
      }
    } catch (error) {
      toast.error("An error occurred while attempting to end trip.", {
        position: "top-center",
        autoClose: 3000,
      });
      console.error("Error ending trip:", error);
    }
  };

  const shouldDisableSiteVisitButton = () => {
    if (
      latestNotification?.type === "approved" &&
      moment().diff(latestNotification.timestamp, "hours") > 24
    ) {
      return true;
    }
    if (
      siteVisitStatus === "succeeded" &&
      activeVisits.some((v) =>
        ["in_progress", "complete", "reviewed"].includes(v.status)
      )
    ) {
      return true;
    }
    return false;
  };

  const surveysToComplete = allUserSiteVisits.filter(
    (visit) => visit.status === "complete"
  );

  const reasonsBookingBlocked = [];
  if (
    siteVisitStatus === "succeeded" &&
    activeVisits.some((v) => v.status === "in_progress")
  ) {
    reasonsBookingBlocked.push(
      "A site visit is currently in progress. Please complete or cancel it."
    );
  }
  if (surveysToComplete.length > 0) {
    reasonsBookingBlocked.push(
      `You have ${surveysToComplete.length} incomplete survey${
        surveysToComplete.length > 1 ? "s" : ""
      }.`
    );
  }
  if (
    latestNotification?.type === "approved" &&
    moment().diff(latestNotification.timestamp, "hours") > 24
  ) {
    reasonsBookingBlocked.push(
      "24 hours have passed since your last approval. You must wait or update your visit."
    );
  }

  const isDisabled = !canBookSiteVisit || shouldDisableSiteVisitButton();
  const bookSiteVisitBtnClasses = `
    flex-1 px-4 py-3 rounded-full text-sm font-medium  
    flex items-center justify-center gap-2 shadow transition-colors
    ${
      isDisabled
        ? "opacity-60 cursor-not-allowed bg-base-200 text-black"
        : "bg-primary text-white hover:bg-green-400"
    }
  `;

  const activeUserSiteVisits = allUserSiteVisits.filter((visit) =>
    ["pending", "approved", "in_progress"].includes(visit.status)
  );

  if (isLoadingFullData) {
    return (
      <Sidebar>
        <div className="flex flex-col items-center justify-center min-h-[70vh]">
          <TailSpin height="40" width="40" color="#999999" />
          <p className="mt-4 italic text-gray-700 text-base">Loading...</p>
        </div>
      </Sidebar>
    );
  }

  // Stats
  const totalVisits = allUserSiteVisits.filter(
    (v) => v.status !== "pending"
  ).length;
  const completedVisits = allUserSiteVisits.filter(
    (v) => v.status === "reviewed" || v.status === "complete"
  ).length;
  const cancelledVisits = allUserSiteVisits.filter(
    (v) => v.status === "cancelled"
  ).length;
  const rejectedVisits = allUserSiteVisits.filter(
    (v) => v.status === "rejected"
  ).length;

  return (
    <Sidebar>
      <div className="mx-auto w-full max-w-7xl px-4 py-6 md:py-8 space-y-6">
        {/* GREETING */}
        <div className="rounded-2xl shadow bg-blue-800 p-6">
          <h1 className="text-3xl font-semibold text-neutral-content">
            {greeting} {firstName}
          </h1>
        </div>

        {/* STATS + QUICK ACTIONS */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* My Stats */}
          <div className="bg-base-100 rounded-2xl shadow p-4">
            <h2 className="text-lg font-semibold text-gray-700 mb-3">
              My Stats
            </h2>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 rounded-xl bg-base-200 p-3">
                <p className="text-gray-600 text-sm">Total Visits</p>
                <p className="text-xl font-bold">{totalVisits}</p>
              </div>
              <div className="flex-1 rounded-xl bg-base-200 p-3">
                <p className="text-gray-600 text-sm">Completed</p>
                <p className="text-xl font-bold">{completedVisits}</p>
              </div>
              <div className="flex-1 rounded-xl bg-base-200 p-3">
                <p className="text-gray-600 text-sm">Cancelled</p>
                <p className="text-xl font-bold">{cancelledVisits}</p>
              </div>
              <div className="flex-1 rounded-xl bg-base-200 p-3">
                <p className="text-gray-600 text-sm">Rejected</p>
                <p className="text-xl font-bold">{rejectedVisits}</p>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="rounded-2xl shadow bg-base-100 p-4 flex flex-col gap-4">
            <h2 className="text-lg font-semibold text-gray-700">
              Quick Actions
            </h2>

            {/* If booking is disabled, show the reasons */}
            {isDisabled && reasonsBookingBlocked.length > 0 && (
              <div className="text-sm text-red-600 rounded-lg bg-red-50 p-3 border border-red-200">
                <p className="font-semibold">
                  You cannot book a new site visit for the following reason(s):
                </p>
                <ul className="list-disc ml-5 mt-1">
                  {reasonsBookingBlocked.map((reason, idx) => (
                    <li key={idx}>{reason}</li>
                  ))}
                </ul>
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-3">
              {(isMarketer || isAdmin) && (
                <button
                  onClick={() => navigate("/book-site-visit")}
                  disabled={isDisabled}
                  className={bookSiteVisitBtnClasses}
                >
                  <MapPinned />
                  Book Site Visit
                </button>
              )}

              {(isDriver || isAdmin) && (
                <button
                  onClick={() => navigate("/assigned-site-visits")}
                  className="flex-1 px-4 py-3 rounded-full text-sm font-medium
                    flex items-center justify-center gap-2 shadow transition-colors text-white
                    bg-secondary hover:bg-blue-400
                  "
                >
                  <Bus />
                  Complete Trips
                </button>
              )}

              <button
                onClick={() => navigate("/request-vehicle")}
                className="flex-1 px-4 py-3 rounded-full text-sm font-medium
                  flex items-center justify-center gap-2 shadow transition-colors text-white
                  bg-gray-800 hover:bg-gray-600
                "
              >
                <BusFront />
                Request Vehicle
              </button>
            </div>
          </div>
        </div>

        {/* INCOMPLETE SURVEYS */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-base-100 p-4 rounded-2xl shadow">
            <h2 className="text-lg font-bold text-red-600">
              Incomplete Surveys
            </h2>
            {surveysToComplete.length > 0 && (
              <p className="text-sm font-semibold text-red-500 mt-1 mb-2">
                You have {surveysToComplete.length} incomplete survey
                {surveysToComplete.length > 1 ? "s" : ""}. You might not be able
                to book a new site visit until you complete them.
              </p>
            )}

            <div className="h-[400px] overflow-y-auto space-y-3">
              {surveysToComplete.length > 0 ? (
                surveysToComplete.map((visit) => (
                  <div
                    key={visit.id}
                    className="p-4 rounded-xl border border-base-200 shadow flex flex-col gap-1"
                  >
                    <h3 className="font-semibold text-neutral-800">
                      {visit.site_name || "Untitled Site"}
                    </h3>
                    <p className="text-sm text-neutral-700">
                      <span className="font-medium">When:</span>{" "}
                      {new Date(visit.pickup_date).toLocaleDateString("en-GB")}{" "}
                      at {formatToAMPM(visit.pickup_time)}
                    </p>
                    {visit.num_clients && (
                      <p className="text-sm text-neutral-700">
                        <span className="font-medium">Clients:</span>{" "}
                        {visit.num_clients}
                      </p>
                    )}
                    {visit.pickup_location && (
                      <p className="text-sm text-neutral-700">
                        <span className="font-medium">Pickup:</span>{" "}
                        {visit.pickup_location}
                      </p>
                    )}
                    <button
                      onClick={() => navigate(`/survey/${visit.id}`)}
                      className="mt-3 px-4 py-2 rounded-full text-sm font-medium 
                        flex items-center justify-center gap-2 shadow
                        bg-primary text-white hover:bg-green-400 self-start
                      "
                    >
                      <NotebookPen />
                      Complete Survey
                    </button>
                  </div>
                ))
              ) : (
                <div className="rounded-xl mt-2 p-4 bg-gray-100 text-sm font-bold">
                  Congratulations! You've filled out all your surveys! 🥳🎉
                </div>
              )}
            </div>
          </div>

          <div className="bg-base-100 p-4 rounded-2xl shadow">
            <h2 className="text-lg font-semibold text-gray-700 mb-2">
              Active Site Visits
            </h2>
            <div className="h-[400px] overflow-y-auto space-y-3">
              {activeUserSiteVisits.length === 0 ? (
                <p className="text-sm text-gray-500">
                  You have no active visits at the moment.
                </p>
              ) : (
                activeUserSiteVisits
                  .sort(
                    (a, b) =>
                      new Date(a.pickup_date).getTime() -
                      new Date(b.pickup_date).getTime()
                  )
                  .map((visit) => {
                    const isInProgress = visit.status === "in_progress";
                    const isComplete = visit.status === "complete";

                    return (
                      <div
                        key={visit.id}
                        className="p-3 rounded-xl border border-base-200"
                      >
                        <p className="font-semibold text-gray-700">
                          {visit.site_name || "Untitled Site"}
                        </p>
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">Date:</span>{" "}
                          {new Date(visit.pickup_date).toLocaleDateString(
                            "en-GB"
                          )}
                          {" at "}
                          {formatToAMPM(visit.pickup_time)}
                        </p>
                        <p className="text-sm text-gray-600 capitalize">
                          <span className="font-medium">Status:</span>{" "}
                          {visit.status.replace("_", " ")}
                        </p>

                        {(visit.status === "approved" || isInProgress) &&
["own means", "reliever", "to be discussed"].includes(
  (visit.driver_name || "").trim().toLowerCase()
) && (
                            <button
                              className={`mt-3 px-4 py-2 rounded-full text-sm font-medium flex items-center gap-2 shadow ${
                                isInProgress
                                  ? "bg-red-600 hover:bg-red-500"
                                  : "bg-primary hover:bg-green-400"
                              } text-white`}
                              onClick={() =>
                                isInProgress
                                  ? endTrip(visit.id)
                                  : startTrip(visit.id)
                              }
                            >
                              {isInProgress ? "End Trip" : "Start Trip"}
                            </button>
                          )}

                        {/* If complete => user can fill survey */}
                        {isComplete && (
                          <button
                            className="mt-3 px-4 py-2 rounded-full text-sm font-medium 
                              flex items-center justify-center gap-2 shadow
                              bg-accent text-white hover:bg-accent-focus
                            "
                            onClick={() => navigate(`/survey/${visit.id}`)}
                          >
                            Complete Survey
                          </button>
                        )}
                      </div>
                    );
                  })
              )}
            </div>
          </div>
        </div>

        <div className="space-y-3">
          {activeVisits
            .map((visit) => {
              let message = "";
              let showAlert = false;

              if (
                visit.status === "approved" &&
                latestNotification?.type === "approved" &&
                moment().diff(latestNotification.timestamp, "hours") > 24
              ) {
                message =
                  "Please wait until your approved site visit is completed or canceled before booking a new one.";
                showAlert = true;
              } else if (visit.status === "in_progress") {
                message =
                  "Your Chauffeur is en route for a site visit. You will receive an email once it's marked as complete.";
                showAlert = true;
              }

              if (!showAlert) return null;
              return (
                <div
                  key={visit.id}
                  className="flex items-center justify-start gap-2 bg-base-100 p-4 rounded-2xl border border-error"
                >
                  <CircleAlert className="text-error mt-1" />
                  <span className="text-sm text-error">{message}</span>
                </div>
              );
            })
            .filter(Boolean)}
        </div>
      </div>
    </Sidebar>
  );
};

export default EnhancedHome;
