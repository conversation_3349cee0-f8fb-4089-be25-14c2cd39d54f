import React from "react";
import Sidebar from "../../components/Sidebar";

const feedbackData = [
  {
    id: 1,
    name: "<PERSON>",
    phoneNumber: "0700123456",
    marketer: "<PERSON>",
    siteVisited: "ABC Site",
    dateVisited: "2022-04-01",
    likedSite: "Yes",
  },
  {
    id: 2,
    name: "<PERSON>",
    phoneNumber: "0711123456",
    marketer: "<PERSON>",
    siteVisited: "XYZ Site",
    dateVisited: "2022-03-28",
    likedSite: "No",
  },
  {
    id: 3,
    name: "<PERSON>",
    phoneNumber: "0722123456",
    marketer: "<PERSON>",
    siteVisited: "LMN Site",
    dateVisited: "2022-03-25",
    likedSite: "Yes",
  },
  {
    id: 4,
    name: "<PERSON>",
    phoneNumber: "0733123456",
    marketer: "<PERSON>",
    siteVisited: "DEF Site",
    dateVisited: "2022-03-20",
    likedSite: "No",
  },
  {
    id: 5,
    name: "<PERSON>",
    phoneNumber: "0744123456",
    marketer: "<PERSON>",
    siteVisited: "GHI Site",
    dateVisited: "2022-03-15",
    likedSite: "Yes",
  },
  {
    id: 6,
    name: "<PERSON> <PERSON>",
    phoneNumber: "0755123456",
    marketer: "Tom <PERSON>",
    siteVisited: "PQR Site",
    dateVisited: "2022-03-10",
    likedSite: "No",
  },
  {
    id: 7,
    name: "Mike Davis",
    phoneNumber: "0766123456",
    marketer: "Anna Brown",
    siteVisited: "STU Site",
    dateVisited: "2022-03-05",
    likedSite: "Yes",
  },
  {
    id: 8,
    name: "Olivia White",
    phoneNumber: "0777123456",
    marketer: "Bob Green",
    siteVisited: "VWX Site",
    dateVisited: "2022-03-01",
    likedSite: "No",
  },
  {
    id: 9,
    name: "Chris Harris",
    phoneNumber: "0788123456",
    marketer: "Karen Lewis",
    siteVisited: "YZA Site",
    dateVisited: "2022-02-25",
    likedSite: "Yes",
  },
  {
    id: 10,
    name: "Kim Kim",
    phoneNumber: "0799123456",
    marketer: "Jerry Lee",
    siteVisited: "BCD Site",
    dateVisited: "2022-02-20",
    likedSite: "No",
  },
];

const ClientsFeedback = () => {
  return (
    <>
      <Sidebar>
        <div className="container px-4 py-6 mx-auto">
          <h1 className="text-xl font-bold mb-4">Client Feedback</h1>
          <div className="overflow-x-auto card bg-base-100 shadow-xl">
            <table className="table table-zebra w-full">
              <thead>
                <tr>
                  <th>#</th>
                  <th>Name</th>
                  <th>Phone Number</th>
                  <th>Marketer</th>
                  <th>Site Visited</th>
                  <th>Date of Visit</th>
                  <th>Purchased?</th>
                </tr>
              </thead>
              <tbody>
                {feedbackData.map((feedback,i) => (
                  <tr key={feedback.id}>
                    <td>{i +1}</td>
                    <td>{feedback.name}</td>
                    <td>{feedback.phoneNumber}</td>
                    <td>{feedback.marketer}</td>
                    <td>{feedback.siteVisited}</td>
                    <td>{feedback.dateVisited}</td>
                    <td>{feedback.likedSite}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </Sidebar>
    </>
  );
};

export default ClientsFeedback;
