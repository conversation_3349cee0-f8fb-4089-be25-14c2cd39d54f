// src/pages/TransactionsPage.js
import React, { useState, useEffect, useMemo } from "react";
import { useParams } from "react-router-dom";
import axios from "axios";
import Sidebar from "../components/Sidebar";
import { useDebounce } from "use-debounce";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const PAGE_SIZE = 20;

const TransactionsPage = () => {
  const { projectName } = useParams();

  // Data states
  const [transactions, setTransactions] = useState([]);
  const [filteredTransactions, setFilteredTransactions] = useState([]);

  // Search & filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm] = useDebounce(searchTerm, 300);
  const [assignedFilter, setAssignedFilter] = useState("all"); // all | yes | no

  // Sorting and pagination states
  const [sortConfig, setSortConfig] = useState({ key: null, direction: "asc" });
  const [currentPage, setCurrentPage] = useState(1);

  // Loading / error states
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Reassign modal states
  const [showReassignModal, setShowReassignModal] = useState(false);
  const [reassignTransactionId, setReassignTransactionId] = useState(null);
  const [newPlotNo, setNewPlotNo] = useState("");
  const [reassignComment, setReassignComment] = useState("");

  // Available plots
  const [availablePlots, setAvailablePlots] = useState([]);
  const [loadingPlots, setLoadingPlots] = useState(false);

  // Fetch transactions and plots
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setLoadingPlots(true);

        const [txResponse, plotsResponse] = await Promise.all([
          axios.get(
            `https://workspace.optiven.co.ke/api/mib/projects/${encodeURIComponent(
              projectName
            )}/transactions`
          ),
          axios.get(
            `https://workspace.optiven.co.ke/api/mib/projects/${encodeURIComponent(
              projectName
            )}/plots`
          ),
        ]);

        if (Array.isArray(txResponse.data.transactions)) {
          const enhanced = txResponse.data.transactions.map((tx) => ({
            ...tx,
            assigned: tx.assigned_to && tx.assigned_to.trim() !== "",
          }));
          setTransactions(enhanced);
        } else {
          throw new Error("Unexpected transaction format");
        }

        if (Array.isArray(plotsResponse.data.plots)) {
          setAvailablePlots(plotsResponse.data.plots);
        }
      } catch (err) {
        console.error(err);
        setError(
          err.response?.data?.message ||
            "Failed to load transactions or plots."
        );
        toast.error(
          err.response?.data?.message ||
            "Failed to load transactions or plots."
        );
      } finally {
        setLoading(false);
        setLoadingPlots(false);
      }
    };
    fetchData();
  }, [projectName]);

  // Filter, search, sort
  useEffect(() => {
    let data = [...transactions];

    // Assigned filter
    if (assignedFilter !== "all") {
      const isAssigned = assignedFilter === "yes";
      data = data.filter((t) => t.assigned === isAssigned);
    }

    // Search filter
    if (debouncedSearchTerm) {
      const term = debouncedSearchTerm.toLowerCase();
      data = data.filter((t) => {
        return (
          t.id.toString().toLowerCase().includes(term) ||
          t.Receipt_No.toLowerCase().includes(term) ||
          (t.Narration || "").toLowerCase().includes(term)
        );
      });
    }

    // Sorting
    if (sortConfig.key) {
      data.sort((a, b) => {
        let aVal, bVal;
        switch (sortConfig.key) {
          case "id":
            aVal = a.id;
            bVal = b.id;
            break;
          case "Receipt_No":
            aVal = parseInt(a.Receipt_No.replace(/^REC/i, ""), 10) || 0;
            bVal = parseInt(b.Receipt_No.replace(/^REC/i, ""), 10) || 0;
            break;
          case "Amount_LCY":
            aVal = parseFloat(a.Amount_LCY) || 0;
            bVal = parseFloat(b.Amount_LCY) || 0;
            break;
          case "PAYMENT_DATE1":
            aVal = new Date(a.PAYMENT_DATE1).getTime() || 0;
            bVal = new Date(b.PAYMENT_DATE1).getTime() || 0;
            break;
          case "assigned":
            aVal = a.assigned ? 1 : 0;
            bVal = b.assigned ? 1 : 0;
            break;
          default:
            return 0;
        }
        if (aVal < bVal) return sortConfig.direction === "asc" ? -1 : 1;
        if (aVal > bVal) return sortConfig.direction === "asc" ? 1 : -1;
        return 0;
      });
    }

    setFilteredTransactions(data);
    setCurrentPage(1);
  }, [transactions, debouncedSearchTerm, assignedFilter, sortConfig]);

  // Paginated subset
  const paginatedTransactions = useMemo(() => {
    const start = (currentPage - 1) * PAGE_SIZE;
    return filteredTransactions.slice(start, start + PAGE_SIZE);
  }, [filteredTransactions, currentPage]);

  const totalPages = Math.max(
    1,
    Math.ceil(filteredTransactions.length / PAGE_SIZE)
  );

  // Sorting handler
  const handleSort = (key) => {
    let direction = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }
    setSortConfig({ key, direction });
  };

  // Reassign logic
  const openReassignModal = (id) => {
    setReassignTransactionId(id);
    setShowReassignModal(true);
    setNewPlotNo("");
    setReassignComment("");
  };
  const closeReassignModal = () => {
    setShowReassignModal(false);
    setReassignTransactionId(null);
    setNewPlotNo("");
    setReassignComment("");
  };
  const handleReassign = async () => {
    if (!newPlotNo.trim() || !reassignComment.trim()) {
      toast.warn("Please fill out all reassignment fields.");
      return;
    }
    try {
      await axios.patch(
        `https://workspace.optiven.co.ke/api/mib/transactions/${reassignTransactionId}/assign`,
        { newPlotNo: newPlotNo.trim(), reassign_comment: reassignComment.trim() }
      );
      // Update local state
      const updated = transactions.map((tx) =>
        tx.id === reassignTransactionId
          ? { ...tx, assigned_to: newPlotNo.trim(), assigned: true }
          : tx
      );
      setTransactions(updated);
      closeReassignModal();
      toast.success("Transaction reassigned successfully.");
    } catch (err) {
      console.error(err);
      toast.error("Failed to reassign transaction.");
    }
  };

  // Render
  if (loading) {
    return (
      <Sidebar>
        <div className="p-6 flex items-center justify-center">
          <div
            className="radial-progress animate-spin"
            style={{ "--value": 70, "--size": "4rem", "--thickness": "0.1rem" }}
          />
          <p className="ml-4">Loading transactions...</p>
        </div>
      </Sidebar>
    );
  }

  if (error) {
    return (
      <Sidebar>
        <div className="p-6">
          <p className="text-red-500">{error}</p>
        </div>
      </Sidebar>
    );
  }

  return (
    <Sidebar>
      {/* Toast container */}
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnHover
        draggable
      />

      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">
          Transactions for {projectName}
        </h1>

        {/* Search & Filters */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 space-y-4 sm:space-y-0">
          <div className="flex space-x-2 w-full sm:w-1/2">
            <input
              type="text"
              placeholder="Search by ID, Receipt No, or Narration..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto bg-white shadow rounded-lg">
          <table className="min-w-full divide-y divide-gray-200 table-auto">
            <thead className="bg-gray-100">
              <tr>
                {[
                  { label: "ID", key: "id" },
                  { label: "Receipt No", key: "Receipt_No" },
                  { label: "Narration", key: null },
                  { label: "Amount", key: "Amount_LCY" },
                  { label: "Date", key: "PAYMENT_DATE1" },
                  { label: "Pay Mode", key: null },
                  { label: "Assigned", key: "assigned" },
                  { label: "Actions", key: null },
                ].map((col) => (
                  <th
                    key={col.label}
                    onClick={() => col.key && handleSort(col.key)}
                    className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
                      col.key ? "cursor-pointer select-none" : ""
                    }`}
                  >
                    <div className="flex items-center space-x-1">
                      <span>{col.label}</span>
                      {sortConfig.key === col.key && (
                        <span>
                          {sortConfig.direction === "asc" ? "🔼" : "🔽"}
                        </span>
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedTransactions.map((tx) => (
                <tr
                  key={tx.id}
                  className="even:bg-gray-50 hover:bg-gray-100"
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    {tx.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    {tx.Receipt_No}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    {tx.Narration}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    Ksh. {tx.Amount_LCY}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    {tx.PAYMENT_DATE1
                      ? new Date(tx.PAYMENT_DATE1).toLocaleDateString()
                      : ""}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    {tx.Pay_mode}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    {tx.assigned ? "Yes" : "No"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      className="px-3 py-1 bg-yellow-500 text-white text-sm rounded-md hover:bg-yellow-600"
                      onClick={() => openReassignModal(tx.id)}
                    >
                      Reassign
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {filteredTransactions.length === 0 && (
            <p className="p-4 text-center text-gray-500">
              No transactions match your criteria.
            </p>
          )}
        </div>

        {/* Pagination Controls */}
        <div className="flex items-center justify-between mt-4">
          <button
            disabled={currentPage === 1}
            onClick={() => setCurrentPage((p) => p - 1)}
            className="px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
          >
            Previous
          </button>
          <span className="text-sm text-gray-600">
            Page {currentPage} of {totalPages}
          </span>
          <button
            disabled={currentPage === totalPages}
            onClick={() => setCurrentPage((p) => p + 1)}
            className="px-4 py-2 bg-white text-sm font-medium text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
          >
            Next
          </button>
        </div>
      </div>

      {/* Reassign Modal */}
      {showReassignModal && (
        <div
          className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
          onClick={closeReassignModal}
        >
          <div
            className="bg-white p-6 rounded-md shadow-md w-11/12 max-w-md"
            onClick={(e) => e.stopPropagation()}
          >
            <h2 className="text-xl font-bold mb-4">Reassign Payment</h2>
            <p className="text-sm text-red-500 mb-2">
              Disclaimer: Reassigning a payment to a different plot may affect
              that plot’s payment history.
            </p>
            <div className="mb-4">
              {loadingPlots ? (
                <p>Loading plots...</p>
              ) : (
                <select
                  value={newPlotNo}
                  onChange={(e) => setNewPlotNo(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">Select a plot</option>
                  {availablePlots.map((plot, idx) => (
                    <option key={idx} value={plot.Plot_NO}>
                      {plot.Plot_NO}
                    </option>
                  ))}
                </select>
              )}
            </div>
            <textarea
              placeholder="Enter reassignment comment"
              value={reassignComment}
              onChange={(e) => setReassignComment(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md mb-4"
              rows={3}
            />
            <div className="flex justify-end space-x-2">
              <button
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                onClick={closeReassignModal}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                onClick={handleReassign}
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
    </Sidebar>
  );
};

export default TransactionsPage;
