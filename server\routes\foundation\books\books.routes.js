const express = require("express");
const authenticateJWT = require("../../../middleware/authenticateJWT");
const router = express.Router();

module.exports = (pool, io) => {
  // Route for the Add Book data modal
  router.post("/", async (req, res) => {
    const { book_name, book_code, book_price, book_copies, authenticateJWT } =
      req.body;
    try {
      pool.query(
        "INSERT INTO `book_uploads`(`book_name`, `book_code`, `book_price`, `book_copies`) VALUES (?, ?, ?, ?)",
        [book_name, book_code, book_price, book_copies],
        (err, result) => {
          if (err) {
            console.error("Database Error:", err);
            return res.status(500).json({
              message: "An error occurred while adding the Book.",
            });
          }
          res.status(201).json({ message: "Book added successfully!" });
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while adding the Book.",
      });
    }
  });
  //   Route to get Book Data
  router.get("/", async (req, res) => {
    try {
      pool.query(
        "SELECT bu.book_code, bu.book_name, COALESCE(SUM(bs.book_copies), 0) AS copies_sold, bu.book_copies, bu.book_price, COALESCE(SUM(bi.book_copies), 0) AS copies_issued, ANY_VALUE(bi.book_office_issued) AS office FROM book_uploads bu LEFT JOIN book_sales bs ON bu.book_code = bs.book_code LEFT JOIN book_issuance bi ON bu.book_code = bi.book_code GROUP BY bu.book_code, bu.book_name, bu.book_copies, bu.book_price;",
        (err, results) => {
          if (err) throw err;

          res.json(results);
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching the Book",
      });
    }
  });

  //route to edit book copies for reload75
  router.patch("/:id", (req, res) => {
    const { id } = req.params;
    const { book_copies } = req.body;

    // Fetch the current value of book_copies from the database
    pool.query(
      "SELECT book_copies FROM book_uploads WHERE book_code = ?",
      [id],
      (selectErr, selectResult) => {
        if (selectErr) {
          console.error(selectErr);
          res.status(500).json({ message: "Server Error" });
        } else if (selectResult.length > 0) {
          // Convert both the current value and the new value to numbers
          const currentValue = parseInt(selectResult[0].book_copies);
          const plusValue = parseInt(book_copies);

          // Perform the addition
          const updatedValue = currentValue + plusValue;

          // Update the database with the new value
          pool.query(
            "UPDATE book_uploads SET book_copies = ? WHERE book_code = ?",
            [updatedValue.toString(), id],
            (updateErr, updateResult) => {
              if (updateErr) {
                console.error(updateErr);
                res.status(500).json({ message: "Server Error" });
              } else if (updateResult.affectedRows > 0) {
                res.json({
                  message: "Book copies updated successfully",
                });
              } else {
                res.status(404).json({ message: "Unsuccessful" });
              }
            }
          );
        } else {
          res.status(404).json({ message: "Book not found" });
        }
      }
    );
  });

  return router;
};
