// Load required modules and environment variables
require("dotenv").config();
const express = require("express");
const axios = require("axios");
const nodemailer = require("nodemailer");
const pdfMakePrinter = require("pdfmake/src/printer");
const authenticateJWT = require("../../../middleware/authenticateJWT");
const router = express.Router();

// Define your fonts
var fonts = {
  Roboto: {
    normal: "node_modules/roboto-font/fonts/Roboto/roboto-regular-webfont.ttf",
    bold: "node_modules/roboto-font/fonts/Roboto/roboto-bold-webfont.ttf",
    italic: "node_modules/roboto-font/fonts/Roboto/roboto-italic-webfont.ttf",
    bolditalics:
      "node_modules/roboto-font/fonts/Roboto/roboto-bolditalic-webfont.ttf",
  },
};

// Create a new printer with the fonts
var printer = new pdfMakePrinter(fonts);

const WATI_TOKEN = process.env.WATI_TOKEN;
const WATI_BASE_URL = process.env.WATI_BASE_URL;

// Nodemailer helper function to send email
async function sendEmail(userEmail, subject, text) {
  // create reusable transporter object using the default SMTP transport
  let transporter = nodemailer.createTransport({
    host: "smtp.zoho.com",
    port: 465,
    secure: true, // true for 465, false for other ports
    auth: {
      user: process.env.DOMAIN_EMAIL, // your domain email account from .env
      pass: process.env.DOMAIN_PASSWORD, // your domain email password
    },
  });

  // send mail with defined transport object
  let info = await transporter.sendMail({
    from: '"Optiven Logistics 🚌" <<EMAIL>>', // sender address
    to: userEmail, // list of receivers
    subject: subject, // Subject line
    text: text, // plain text body
  });
}

// WATI Helper function to send the WhatsApp msg
// const sendWhatsAppMessage = async (
//   phoneNumber,
//   templateName,
//   parameters,
//   broadcastName
// ) => {
//   const config = {
//     headers: {
//       Authorization: `Bearer ${WATI_TOKEN}`,
//     },
//   };
//   const bodyData = {
//     parameters: parameters,
//     template_name: templateName,
//     broadcast_name: broadcastName,
//   };
//   try {
//     const response = await axios.post(
//       `${WATI_BASE_URL}/api/v1/sendTemplateMessage?whatsappNumber=${phoneNumber}`,
//       bodyData,
//       config
//     );
//     return response.data;
//   } catch (error) {
//     console.error("Failed to send WhatsApp message:", error.message);
//     throw error;
//   }
// };

// Define your dataToPdfRows function
function dataToPdfRows(data) {
  return data.map((item, index) => {
    const date = new Date(item.pickup_date);
    const formattedDate = `${date.getFullYear()}-${
      date.getMonth() + 1
    }-${date.getDate()}`;
    return [
      { text: index + 1 ?? "", style: "tableCell" },
      { text: formattedDate ?? "", style: "tableCell" },
      { text: item.marketer_name ?? "", style: "tableCell" },
      { text: item.client_name ?? "", style: "tableCell" },
      { text: item.client_phone ?? "", style: "tableCell" },
      { text: item.site_name ?? "", style: "tableCell" },
      { text: item.driver_name ?? "", style: "tableCell" },
      { text: item.pickup_time ?? "", style: "tableCell" },
      { text: item.vehicle_name ?? "", style: "tableCell" },
      { text: item.pickup_location ?? "", style: "tableCell" },
      { text: item.remarks ?? "", style: "tableCell" },
    ];
  });
}

// Define your dataToPdfRows function
function dataToPdfRows2(results) {
  return results.map((result, index) => {
    const date = new Date(result.date);
    const formattedDate = `${date.getFullYear()}-${
      date.getMonth() + 1
    }-${date.getDate()}`;

    return [
      { text: index + 1, style: "tableCell" },
      { text: formattedDate, style: "tableCell" },
      { text: result.successful, style: "tableCell" },
      { text: result.cancelled, style: "tableCell" },
      { text: result.rejected, style: "tableCell" },
      { text: result.total, style: "tableCell" },
    ];
  });
}

// Define your dataToPdfRows function
function dataToPdfRows3(data) {
  return data.map((item, index) => {
    const date = new Date(item.pickup_date);
    const formattedDate = `${date.getFullYear()}-${
      date.getMonth() + 1
    }-${date.getDate()}`;

    let assignedTo = ""; // Initialize assignedTo variable
    let optivenSite = ""; // Initialize optivenSite variable
    let numClients = ""; // Initialize numClients variable

    // Check if it's a special assignment
    if (item.special_assignment_destination !== null) {
      assignedTo = item.special_assignment_assigned_to || ""; // Capture assigned person
      optivenSite = item.special_assignment_destination || ""; // Capture destination
      numClients = "N/A"; // Set "N/A" for special assignments
    } else {
      assignedTo = item.marketer_name || ""; // Capture marketer name for site visits
      optivenSite = item.site_name || ""; // Capture site name for site visits
      numClients = item.num_clients || ""; // Capture number of clients for site visits
    }

    return [
      { text: index + 1 ?? "", style: "tableCell" },
      { text: formattedDate ?? "", style: "tableCell" },
      { text: assignedTo, style: "tableCell" }, // Display assignedTo or special_assignment_assigned_to
      { text: numClients, style: "tableCell" }, // Display numClients
      { text: optivenSite, style: "tableCell" }, // Display optivenSite or special_assignment_destination
      { text: item.driver_name ?? "", style: "tableCell" },
      { text: item.pickup_time ?? "", style: "tableCell" },
      { text: item.vehicle_name ?? "", style: "tableCell" },
      { text: item.pickup_location ?? "", style: "tableCell" },
      { text: item.remarks ?? "", style: "tableCell" },
    ];
  });
}

module.exports = (pool, io) => {
  // Route to calculate percentile for each marketer
  router.get("/marketers-percentiles", async (req, res) => {
    try {
      const query = `
      SELECT
        users.user_id,
        users.fullnames as marketer_name,
        COUNT(site_visits.id) as num_site_visits
      FROM users
      LEFT JOIN site_visits
        ON users.user_id = site_visits.marketer_id
      WHERE site_visits.status = 'reviewed'
      GROUP BY users.user_id, users.fullnames
      ORDER BY num_site_visits DESC;
    `;

      pool.query(query, async (err, results) => {
        if (err) throw err;

        const marketers = results.map((result, index) => {
          const percentile = ((index + 1) / results.length) * 100;
          return {
            user_id: result.user_id,
            marketer_name: result.marketer_name,
            num_site_visits: result.num_site_visits,
            percentile: 100 - percentile,
          };
        });

        res.status(200).json(marketers);
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Route to calculate percentile for a specific marketer by userId
  router.get("/marketers-percentiles/:userId", async (req, res) => {
    const userId = req.params.userId;
    try {
      const query = `
      SELECT
        users.user_id,
        users.fullnames as marketer_name,
        COUNT(site_visits.id) as num_site_visits
      FROM users
      LEFT JOIN site_visits
        ON users.user_id = site_visits.marketer_id
      WHERE site_visits.status = 'reviewed'
      GROUP BY users.user_id, users.fullnames
      ORDER BY num_site_visits DESC;
    `;

      pool.query(query, async (err, results) => {
        if (err) throw err;

        const marketer = results.find((result) => result.user_id === userId);

        if (!marketer) {
          // Handle the case where the marketer is not found
          const percentile = 0; // Set percentile to 0
          res.status(200).json({
            user_id: userId,
            num_site_visits: 0,
            percentile: percentile,
          });
        } else {
          const percentile =
            100 - ((results.indexOf(marketer) + 1) / results.length) * 100;
          res.status(200).json({
            user_id: marketer.user_id,
            marketer_name: marketer.marketer_name,
            num_site_visits: marketer.num_site_visits,
            percentile: percentile,
          });
        }
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Get single site visit with driver, vehicle info, and all associated clients
  router.get("/:id", authenticateJWT, async (req, res) => {
    try {
      const siteVisitQuery = `
        SELECT 
          site_visits.*,
          Projects.name AS site_name,
          users.fullnames as marketer_name,
          drivers.fullnames as driver_name,
          vehicles.vehicle_registration as vehicle_name,
          drivers.phone_number as driver_contact
        FROM site_visits
        LEFT JOIN Projects
          ON site_visits.project_id = Projects.project_id
        LEFT JOIN users
          ON site_visits.marketer_id = users.user_id
        LEFT JOIN users as drivers
          ON site_visits.driver_id = drivers.user_id
        LEFT JOIN vehicles
          ON site_visits.vehicle_id = vehicles.id
        WHERE site_visits.id = ?
        ORDER BY site_visits.created_at DESC;
      `;
      pool.query(siteVisitQuery, [req.params.id], async (err, results) => {
        if (err) throw err;
        if (results.length > 0) {
          const siteVisit = results[0];
          const clientsQuery = `
            SELECT 
              site_visit_clients.name as client_name,
              site_visit_clients.email as client_email,
              site_visit_clients.phone_number as client_phone
            FROM site_visit_clients
            WHERE site_visit_clients.site_visit_id = ?
          `;
          pool.query(clientsQuery, [req.params.id], (err, results) => {
            if (err) throw err;
            siteVisit.clients = results;
            res.status(200).json(siteVisit);
          });
        } else {
          res.status(404).json({ message: "Site visit not found" });
        }
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
  // Get all site visits with driver and vehicle info
  router.get("/", authenticateJWT, async (req, res) => {
    try {
      const query = `
      SELECT 
          site_visits.*,
          Projects.name AS site_name,
          COUNT(site_visit_clients.id) as num_clients,
          users.fullnames as marketer_name,
          drivers.fullnames as driver_name,
          vehicles.vehicle_registration as vehicle_name
      FROM site_visits
      LEFT JOIN Projects
          ON site_visits.project_id = Projects.project_id
      LEFT JOIN site_visit_clients
          ON site_visits.id = site_visit_clients.site_visit_id
      LEFT JOIN users
          ON site_visits.marketer_id = users.user_id
      LEFT JOIN users as drivers
          ON site_visits.driver_id = drivers.user_id
      LEFT JOIN vehicles
          ON site_visits.vehicle_id = vehicles.id
      GROUP BY site_visits.id
      ORDER BY DATE(site_visits.pickup_time), 
              MIN(TIME(site_visits.pickup_time)) DESC, 
              site_visits.created_at DESC;

    `;
      pool.query(query, (err, results) => {
        if (err) throw err;
        res.status(200).json(results);
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Get all pending site visit requests
  router.get("/pending-site-visits/all", authenticateJWT, async (req, res) => {
    try {
      const query = `
        SELECT 
          site_visits.*
        FROM site_visits
        WHERE site_visits.status IN ('pending');
      `;
      pool.query(query, (err, results) => {
        if (err) throw err;
        res.status(200).json(results);
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
  // Download the approved site visits info in a pdf
  router.get(
    "/download-pdf/approved-site-visits",
    authenticateJWT,
    async (req, res) => {
      try {
        const startDate = req.query.startDate;
        const endDate = req.query.endDate;
        const query = `
          SELECT 
            site_visits.*,
            Projects.name AS site_name,
            site_visit_clients.name as client_name,
            site_visit_clients.phone_number as client_phone,
            users.fullnames as marketer_name,
            drivers.fullnames as driver_name,
            vehicles.vehicle_registration as vehicle_name
          FROM site_visits
          LEFT JOIN Projects
            ON site_visits.project_id = Projects.project_id
          LEFT JOIN site_visit_clients
            ON site_visits.id = site_visit_clients.site_visit_id
          LEFT JOIN users
            ON site_visits.marketer_id = users.user_id
          LEFT JOIN users as drivers
            ON site_visits.driver_id = drivers.user_id
          LEFT JOIN vehicles
            ON site_visits.vehicle_id = vehicles.id
          WHERE site_visits.status = 'approved'
            AND site_visits.pickup_date BETWEEN ? AND ?
          ORDER BY site_visits.created_at DESC;
        `;
        pool.query(query, [startDate, endDate], (err, results) => {
          if (err) throw err;
          const docDefinition = {
            pageSize: "A4",
            pageOrientation: "landscape",
            content: [
              {
                table: {
                  headerRows: 1,
                  widths: [
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                  ],
                  body: [
                    [
                      {
                        text: "Index",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Pickup Date",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Converter",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Client Name",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Client Contact",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Site",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Chauffeur",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Pickup Time",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Vehicle Reg No",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Pickup Location",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Admin Remarks",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                    ],
                  ],
                },
                layout: {
                  hLineWidth: function (i, node) {
                    return 0;
                  },
                  vLineWidth: function (i, node) {
                    return 0;
                  },
                  fillColor: function (rowIndex, node, columnIndex) {
                    return rowIndex % 2 === 0 ? "#D3D3D3" : null;
                  },
                },
              },
            ],
            styles: {
              tableHeader: {
                bold: true,
                fontSize: 13,
                color: "white",
              },
              tableBody: {
                italic: true,
              },
            },
          };
          // Populate the body array with your data
          docDefinition.content[0].table.body.push(...dataToPdfRows(results));
          // Create the PDF and send it as a response
          const pdfDoc = printer.createPdfKitDocument(docDefinition);
          res.setHeader("Content-Type", "application/pdf");
          pdfDoc.pipe(res);
          pdfDoc.end();
        });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    }
  );
  // Download driver itenirary
  router.get(
    "/download-pdf/driver-itenirary",
    // authenticateJWT,
    async (req, res) => {
      try {
        const startDate = req.query.startDate;
        const endDate = req.query.endDate;
        const query = `
          SELECT 
            site_visits.*,
            Projects.name AS site_name,
            COUNT(site_visit_clients.id) as num_clients,
            users.fullnames as marketer_name,
            drivers.fullnames as driver_name,
            vehicles.vehicle_registration as vehicle_name
          FROM site_visits
          LEFT JOIN Projects
            ON site_visits.project_id = Projects.project_id
          LEFT JOIN site_visit_clients
            ON site_visits.id = site_visit_clients.site_visit_id
          LEFT JOIN users
            ON site_visits.marketer_id = users.user_id
          LEFT JOIN users as drivers
            ON site_visits.driver_id = drivers.user_id
          LEFT JOIN vehicles
            ON site_visits.vehicle_id = vehicles.id
          WHERE site_visits.status = 'approved'
            AND site_visits.pickup_date BETWEEN ? AND ?
          GROUP BY site_visits.id
          ORDER BY site_visits.created_at DESC;
        `;
        pool.query(query, [startDate, endDate], (err, results) => {
          if (err) throw err;
          const docDefinition = {
            pageSize: "A4",
            pageOrientation: "landscape",
            content: [
              {
                table: {
                  headerRows: 1,
                  widths: [
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                  ],
                  body: [
                    [
                      {
                        text: "No",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Date",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Converter/Persons Assigned",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "No of Clients",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Optiven Site/Destination",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Chauffeur",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Time",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Vehicle",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Pickup Location",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Admin Remarks",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                    ],
                  ],
                },
                layout: {
                  hLineWidth: function (i, node) {
                    return 0;
                  },
                  vLineWidth: function (i, node) {
                    return 0;
                  },
                  fillColor: function (rowIndex, node, columnIndex) {
                    return rowIndex % 2 === 0 ? "#D3D3D3" : null;
                  },
                },
              },
            ],
            styles: {
              tableHeader: {
                bold: true,
                fontSize: 13,
                color: "white",
              },
              tableBody: {
                bold: true,
              },
            },
          };
          // Populate the body array with your data
          docDefinition.content[0].table.body.push(...dataToPdfRows3(results));
          // Create the PDF and send it as a response
          const pdfDoc = printer.createPdfKitDocument(docDefinition);
          res.setHeader("Content-Type", "application/pdf");
          pdfDoc.pipe(res);
          pdfDoc.end();
        });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    }
  );
  // Download the site visits summary into pdf
  router.get(
    "/download-pdf/site-visit-summary",
    authenticateJWT,
    async (req, res) => {
      try {
        const startDate = req.query.startDate;
        const endDate = req.query.endDate;

        const query = `
          SELECT 
            DATE(site_visits.pickup_date) as date,
            SUM(CASE WHEN site_visits.status = 'complete' OR site_visits.status = 'reviewed' THEN 1 ELSE 0 END) as successful,
            SUM(CASE WHEN site_visits.status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
            SUM(CASE WHEN site_visits.status = 'rejected' THEN 1 ELSE 0 END) as rejected,
            COUNT(*) as total
          FROM site_visits
          JOIN users ON site_visits.marketer_id = users.user_id
          WHERE site_visits.pickup_date BETWEEN ? AND ?
          GROUP BY DATE(site_visits.pickup_date)
          ORDER BY DATE(site_visits.pickup_date);
        `;

        pool.query(query, [startDate, endDate], (err, results) => {
          if (err) throw err;

          const docDefinition = {
            pageSize: "A4",
            pageOrientation: "landscape",
            content: [
              {
                text: `Site Visit Summary from ${startDate} to ${endDate}`,
                fontSize: 20,
                alignment: "center",
                margin: [0, 0, 0, 20],
              },
              {
                table: {
                  headerRows: 1,
                  widths: ["auto", "auto", "auto", "auto", "auto", "auto"],
                  body: [
                    [
                      {
                        text: "Index",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Date",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Successful Site Visits",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Cancelled Site Visits",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Rejected Site Visits",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Total Site Visits",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                    ],
                  ],
                },
                layout: {
                  hLineWidth: function (i, node) {
                    return 0;
                  },
                  vLineWidth: function (i, node) {
                    return 0;
                  },
                  fillColor: function (rowIndex, node, columnIndex) {
                    return rowIndex % 2 === 0 ? "#D3D3D3" : null;
                  },
                },
              },
            ],
            styles: {
              tableHeader: {
                bold: true,
                fontSize: 13,
                color: "white",
              },
              tableBody: {
                italic: true,
              },
            },
          };

          // Populate the body array with your data
          docDefinition.content[1].table.body.push(...dataToPdfRows2(results));

          // Create the PDF and send it as a response
          const pdfDoc = printer.createPdfKitDocument(docDefinition);
          res.setHeader("Content-Type", "application/pdf");
          pdfDoc.pipe(res);
          pdfDoc.end();
        });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    }
  );
  // Download most booked sites within a certain date range
  router.get(
    "/download-pdf/most-booked-sites",
    authenticateJWT,
    async (req, res) => {
      try {
        const startDate = req.query.startDate;
        const endDate = req.query.endDate;

        const query = `
          SELECT 
            Projects.name AS site_name, 
            COUNT(*) as total_bookings
          FROM site_visits
          INNER JOIN Projects ON site_visits.project_id = Projects.project_id
          WHERE site_visits.pickup_date BETWEEN ? AND ?
          GROUP BY Projects.name
          ORDER BY total_bookings DESC;
        `;
        pool.query(query, [startDate, endDate], (err, results) => {
          if (err) throw err;

          const docDefinition = {
            pageSize: "A4",
            pageOrientation: "landscape",
            content: [
              {
                text: `Most Booked Sites from ${startDate} to ${endDate}`,
                fontSize: 20,
                alignment: "center",
                margin: [0, 0, 0, 20],
              },
              {
                table: {
                  headerRows: 1,
                  widths: ["auto", "auto"],
                  body: [
                    [
                      {
                        text: "Site Name",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Total Bookings",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                    ],
                    ...results.map((result, index) => [
                      { text: result.site_name, style: "tableCell" },
                      { text: result.total_bookings, style: "tableCell" },
                    ]),
                  ],
                },
                layout: {
                  hLineWidth: function (i, node) {
                    return 0;
                  },
                  vLineWidth: function (i, node) {
                    return 0;
                  },
                  fillColor: function (rowIndex, node, columnIndex) {
                    return rowIndex % 2 === 0 ? "#D3D3D3" : null;
                  },
                },
              },
            ],
            styles: {
              tableHeader: {
                bold: true,
                fontSize: 13,
                color: "white",
              },
              tableCell: {
                fontSize: 12,
              },
            },
          };

          // Create the PDF and send it as a response
          const pdfDoc = printer.createPdfKitDocument(docDefinition);
          res.setHeader("Content-Type", "application/pdf");
          pdfDoc.pipe(res);
          pdfDoc.end();
        });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    }
  );
  // Downloadable PDF for marketer feedback
  router.get(
    "/download-pdf/marketer-feedback",
    authenticateJWT,
    async (req, res) => {
      try {
        const query = `
        SELECT 
          Projects.name AS site_name, 
          users.fullnames AS marketer, 
          site_visit_surveys.amount_reserved,
          IF(site_visit_surveys.booked = 1, 'Yes', 'No') AS booked,
          site_visit_surveys.reason_not_visited,
          site_visit_surveys.reason_not_booked,
          IF(site_visit_surveys.visited = 1, 'Yes', 'No') AS visited
        FROM site_visit_surveys
        INNER JOIN site_visits ON site_visit_surveys.site_visit_id = site_visits.id
        INNER JOIN Projects ON site_visits.project_id = Projects.project_id
        INNER JOIN users ON site_visits.marketer_id = users.user_id
        ORDER BY site_visit_surveys.id;
      `;
        pool.query(query, [], (err, results) => {
          if (err) throw err;

          const docDefinition = {
            pageSize: "A4",
            pageOrientation: "landscape",
            content: [
              {
                text: `Marketer Feedback`,
                fontSize: 20,
                alignment: "center",
                margin: [0, 0, 0, 20],
              },
              {
                table: {
                  headerRows: 1,
                  widths: [
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                  ],
                  body: [
                    [
                      {
                        text: "Index",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Site Name",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Marketer",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Amount Reserved By Client(Ksh)",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Did the Client Book the Plot?",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Reason the Client did not Visit",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Reason the Client did not Book the Plot",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                      {
                        text: "Did the Client Visit the Plot?",
                        fillColor: "#202A44",
                        style: "tableHeader",
                      },
                    ],
                    ...results.map((result, index) => [
                      { text: index + 1, style: "tableCell" },
                      { text: result.site_name, style: "tableCell" },
                      { text: result.marketer, style: "tableCell" },
                      { text: result.amount_reserved, style: "tableCell" },
                      { text: result.booked, style: "tableCell" },
                      {
                        text: result.reason_not_visited || "N/A",
                        style: "tableCell",
                      },
                      {
                        text: result.reason_not_booked || "N/A",
                        style: "tableCell",
                      },
                      { text: result.visited, style: "tableCell" },
                    ]),
                  ],
                },
                layout: {
                  hLineWidth: function (i, node) {
                    return 0;
                  },
                  vLineWidth: function (i, node) {
                    return 0;
                  },
                  fillColor: function (rowIndex, node, columnIndex) {
                    return rowIndex % 2 === 0 ? "#D3D3D3" : null;
                  },
                },
              },
            ],
            styles: {
              tableHeader: {
                bold: true,
                fontSize: 13,
                color: "white",
              },
              tableCell: {
                fontSize: 12,
              },
            },
          };

          // Create the PDF and send it as a response
          const pdfDoc = printer.createPdfKitDocument(docDefinition);
          res.setHeader("Content-Type", "application/pdf");
          pdfDoc.pipe(res);
          pdfDoc.end();
        });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    }
  );
  // Get info on the user, to see if he's booked any active site-visits
  router.get("/active/active", authenticateJWT, async (req, res) => {
    try {
      const userId = req.user.id;
      const query = `
        SELECT
          site_visits.*,
          users.fullnames as marketer_name
        FROM site_visits
        JOIN users ON site_visits.marketer_id = users.user_id
        WHERE site_visits.marketer_id = ? AND (site_visits.status != 'reviewed' AND site_visits.status != 'rejected' AND site_visits.status != 'cancelled' AND site_visits.status != 'pending');
        `;
      pool.query(query, [userId], (err, results) => {
        if (err) throw err;
        res.status(200).json(results);
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
  // Get a single pending site visit request
  router.get("/pending-site-visits/:id", authenticateJWT, async (req, res) => {
    const id = req.params.id;
    const query = `
      SELECT 
        site_visits.*,
        Projects.name AS site_name,
        COUNT(site_visit_clients.id) as num_clients,
        users.fullnames as marketer_name,
        drivers.fullnames as driver_name
      FROM site_visits 
      LEFT JOIN Projects 
        ON site_visits.project_id = Projects.project_id 
      LEFT JOIN site_visit_clients 
        ON site_visits.id = site_visit_clients.site_visit_id
      LEFT JOIN users 
        ON site_visits.marketer_id = users.user_id
      LEFT JOIN users as drivers
        ON site_visits.driver_id = drivers.user_id
      WHERE site_visits.id = ?
      GROUP BY site_visits.id
      ORDER BY site_visits.created_at ASC;
    `;
    pool.query(query, [id], (err, results) => {
      if (err) throw err;
      if (results.length > 0) {
        res.status(200).json(results[0]);
      } else {
        res.status(404).json({ message: "Site visit request not found." });
      }
    });
  });
  // Cancel a site visit request
  router.patch("/cancel-site-visit/:id", authenticateJWT, async (req, res) => {
    try {
      const { id } = req.params;

      const updateSiteVisitStatusQuery = `
      UPDATE site_visits
      SET status = 'cancelled'
      WHERE id = ? AND (status = 'pending' OR status = 'approved')
    `;
      pool.query(updateSiteVisitStatusQuery, [id], (err, result) => {
        if (err) {
          res.status(500).json({ error: err.message });
          return;
        }

        if (result.affectedRows > 0) {
          const getUserIdQuery =
            "SELECT marketer_id FROM site_visits WHERE id = ?";
          pool.query(getUserIdQuery, [id], async (err, userIdResult) => {
            if (err) {
              res.status(500).json({ error: err.message });
              return;
            }

            if (userIdResult.length > 0) {
              const userId = userIdResult[0].marketer_id;

              const notificationQuery = `
              INSERT INTO notifications (user_id, type, message, remarks, site_visit_id)
              VALUES (?, 'cancelled', 'Your site visit request has been cancelled.', 'Cancelled', ?)
            `;
              pool.query(notificationQuery, [userId, id], (err, result) => {
                if (err) {
                  res.status(500).json({ error: err.message });
                  return;
                }

                // Emit the notification via Socket.IO
                io.emit("siteVisitCancelled", {
                  id: req.params.id,
                  message: "Site visit request cancelled",
                });

                const getEmailQuery =
                  "SELECT email FROM users WHERE user_id = ?";
                pool.query(
                  getEmailQuery,
                  [userId],
                  async (err, emailResult) => {
                    if (err) {
                      res.status(500).json({ error: err.message });
                      return;
                    }

                    if (emailResult.length > 0) {
                      const userEmail = emailResult[0].email;
                      const subject = "Site Visit Request Cancelled";
                      const message = `Greetings,\n\nYour site visit request has been cancelled. 😔\nPlease check your notifications in the app for more details.\n\nKind regards,\nOptiven ICT Department`;
                      sendEmail(userEmail, subject, message)
                        .then(() => {
                          res.status(200).json({
                            message:
                              "Site visit request cancelled successfully. An email notification has been sent.",
                          });
                        })
                        .catch((error) => {
                          res.status(500).json({ error: error.message });
                        });
                    } else {
                      res.status(404).json({ message: "User not found." });
                    }
                  }
                );
              });
            }
          });
        } else {
          res.status(400).json({
            message:
              "Invalid site visit ID or the site visit has already been completed or cancelled.",
          });
        }
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
  // Reject site visit request (with remarks)
  router.patch("/reject-site-visit/:id", authenticateJWT, async (req, res) => {
    try {
      const id = req.params.id;
      const { remarks } = req.body;

      const updateQuery =
        "UPDATE site_visits SET status = 'rejected', remarks = ?, vehicle_id = null, driver_id = null WHERE id = ?";
      pool.query(updateQuery, [remarks, id], async (err, result) => {
        if (err) throw err;

        if (result.affectedRows > 0) {
          const getUserIdQuery =
            "SELECT marketer_id FROM site_visits WHERE id = ?";
          pool.query(getUserIdQuery, [id], async (err, userIdResult) => {
            if (err) throw err;

            if (userIdResult.length > 0) {
              const userId = userIdResult[0].marketer_id;

              const notificationQuery = `
              INSERT INTO notifications (user_id, type, message, remarks)
              VALUES (?, 'rejected', 'Your site visit request has been rejected :(', ?)
            `;
              pool.query(
                notificationQuery,
                [userId, remarks],
                (err, result) => {
                  if (err) {
                    return res.status(500).json({ error: err.message });
                  }

                  // Emit the notification via Socket.IO
                  io.emit("siteVisitRejected", {
                    id: req.params.id,
                    message: "Site visit request rejected",
                  });

                  const getEmailQuery =
                    "SELECT email FROM users WHERE user_id = ?";
                  pool.query(
                    getEmailQuery,
                    [userId],
                    async (err, emailResult) => {
                      if (err) {
                        return res.status(500).json({ error: err.message });
                      }

                      if (emailResult.length > 0) {
                        const userEmail = emailResult[0].email;
                        // Send an email to the marketer
                        await sendEmail(
                          userEmail,
                          "Site Visit Request Rejected",
                          "Greetings,\n\nYour site visit request has been rejected. 😔 \nPlease check your notifications in the app for more details. \n\nKind regards,\nOptiven ICT Department"
                        );
                      }
                    }
                  );
                }
              );
            }
          });

          return res
            .status(200)
            .json({ message: "Site visit request rejected." });
        } else {
          return res
            .status(404)
            .json({ message: "Site visit request not found." });
        }
      });
    } catch (error) {
      return res.status(500).json({ error: error.message });
    }
  });
  // View, edit and approve the site visit request
  router.patch(
    "/pending-site-visits/:id",
    authenticateJWT,
    async (req, res) => {
      try {
        const { id } = req.params;
        const {
          vehicle_id,
          pickup_location,
          pickup_date,
          pickup_time,
          remarks,
          status,
          driver_id,
          project_id,
        } = req.body;

        const updateAndSendNotification = async () => {
          if (status === "approved") {
            // Get the user_id from the site_visits table
            const getUserIdQuery =
              "SELECT marketer_id FROM site_visits WHERE id = ?";
            pool.query(getUserIdQuery, [id], async (err, userIdResult) => {
              if (err) throw err;
              if (userIdResult.length > 0) {
                const userId = userIdResult[0].marketer_id;
                // Insert a record into the notifications table
                const notificationQuery = `
                  INSERT INTO notifications 
                    (user_id, type, message, remarks, site_visit_id)
                  VALUES (?, 'approved', 'Your site visit request has been approved!', ?, ?);
                `;
                pool.query(
                  notificationQuery,
                  [userId, remarks, id],
                  (err, result) => {
                    if (err) res.status(500).json({ error: err.message });

                    // Send an email to the marketer
                    const getEmailQuery =
                      "SELECT email FROM users WHERE user_id = ?";
                    pool.query(
                      getEmailQuery,
                      [userId],
                      async (err, emailResult) => {
                        if (err) res.status(500).json({ error: err.message });
                        if (emailResult.length > 0) {
                          const userEmail = emailResult[0].email;
                          await sendEmail(
                            userEmail,
                            "Site Visit Request Approved",
                            "Greetings,\n\nYour site visit request has been approved!😃 \n Please check your notifications in the app for more details.\n\n Kind regards,\nOptiven ICT Department"
                          );
                        }
                      }
                    );

                    // Send an email to the driver
                    const getDriverEmailQuery =
                      "SELECT email FROM users WHERE user_id = ?";
                    pool.query(
                      getDriverEmailQuery,
                      [driver_id],
                      async (err, driverEmailResult) => {
                        if (err) {
                          res.status(500).json({ error: err.message });
                          return;
                        }

                        if (driverEmailResult.length > 0) {
                          const driverEmail = driverEmailResult[0].email;
                          await sendEmail(
                            driverEmail,
                            "Site Visit Assignment",
                            `Greetings,\n\nYou have been assigned to a site visit.\nPlease check the app for more details.\n\n Kind regards,\nOptiven ICT Department`
                          );
                        }
                      }
                    );

                    // Send WhatsApp message to the client
                    // const getWhatsAppMessageSiteVisitDetailsQuery = `SELECT 
                    //   site_visit_clients.phone_number, 
                    //   site_visit_clients.name,
                    //   Projects.name AS site_name,
                    //   users.fullnames AS marketer_name
                    // FROM site_visit_clients
                    // INNER JOIN site_visits ON site_visit_clients.site_visit_id = site_visits.id
                    // LEFT JOIN Projects ON site_visits.project_id = Projects.project_id
                    // LEFT JOIN users ON site_visits.marketer_id = users.user_id
                    // WHERE site_visit_clients.site_visit_id = ?
                    // `;
                    // pool.query(
                    //   getWhatsAppMessageSiteVisitDetailsQuery,
                    //   [id],
                    //   async (err, WhatsAppMessageDetails) => {
                    //     if (err) res.status(500).json({ error: err.message });
                    //     if (WhatsAppMessageDetails.length > 0) {
                    //       const clientPhoneNumber =
                    //         WhatsAppMessageDetails[0].phone_number;
                    //       const clientName = WhatsAppMessageDetails[0].name;
                    //       const siteLocation =
                    //         WhatsAppMessageDetails[0].site_name;
                    //       const marketerName =
                    //         WhatsAppMessageDetails[0].marketer_name;
                    //       const pickupTime = req.body.pickup_time;
                    //       const pickupDate = req.body.pickup_date;
                    //       const pickupLocation = req.body.pickup_location;
                    //       const templateName = "sv_approved";
                    //       const parameters = [
                    //         { name: "client_name", value: clientName },
                    //         { name: "site_location", value: siteLocation },
                    //         { name: "marketer_name", value: marketerName },
                    //         { name: "pickup_time", value: pickupTime },
                    //         { name: "pickup_date", value: pickupDate },
                    //         { name: "pickup_location", value: pickupLocation },
                    //       ];
                    //       const broadcastName = "test_broadcast";

                    //       try {
                    //         await sendWhatsAppMessage(
                    //           clientPhoneNumber,
                    //           templateName,
                    //           parameters,
                    //           broadcastName
                    //         );
                    //       } catch (error) {
                    //         // Handle any errors that occur during message sending
                    //         console.error(
                    //           "Failed to send WhatsApp message:",
                    //           error
                    //         );
                    //       }
                    //     }
                    //   }
                    // );

                    // Emit the notification via Socket.IO
                    io.emit("siteVisitApproved", {
                      id: req.params.id,
                      message: "Site visit request approved",
                    });
                  }
                );
              }
            });
          }
        };

        if (vehicle_id) {
          // Check if the vehicle is available and has enough seats
          const checkVehicleQuery = `SELECT number_of_seats, passengers_assigned FROM vehicles WHERE id = ? AND status = 'available'`;
          pool.query(checkVehicleQuery, [vehicle_id], (err, vehicleResults) => {
            if (err) throw err;
            if (vehicleResults.length > 0) {
              const numberOfSeats = vehicleResults[0].number_of_seats;
              const passengersAssigned = vehicleResults[0].passengers_assigned;
              const checkClientsQuery = `SELECT COUNT(*) as client_count
                FROM site_visit_clients 
                WHERE site_visit_id = ?`;
              pool.query(checkClientsQuery, [id], (err, clientResults) => {
                if (err) throw err;
                const clientCount = clientResults[0].client_count;

                // Add 1 for the marketer
                if (numberOfSeats >= clientCount + 1 + passengersAssigned) {
                  // Update site visit
                  const query = `
                      UPDATE site_visits
                      SET 
                        vehicle_id = ?,
                        pickup_location = ?, 
                        pickup_date = ?, 
                        pickup_time = ?, 
                        remarks = ?, 
                        status = ?, 
                        driver_id = ?,
                        project_id = ?
                      WHERE id = ?
                    `;

                  pool.query(
                    query,
                    [
                      vehicle_id,
                      pickup_location,
                      pickup_date,
                      pickup_time,
                      remarks,
                      status === "pending" ? "approved" : status,
                      driver_id,
                      project_id,
                      id,
                    ],
                    async (err, results) => {
                      if (err) {
                        res.status(500).json({ error: err.message });
                        return;
                      }

                      await updateAndSendNotification();

                      // New SELECT query to get the updated site visit with the driver's name
                      const updatedSiteVisitQuery = `
                          SELECT 
                            site_visits.*,
                            Projects.name AS site_name,
                            COUNT(site_visit_clients.id) as num_clients,
                            users.fullnames as marketer_name,
                            drivers.fullnames as driver_name
                          FROM site_visits 
                          LEFT JOIN Projects 
                            ON site_visits.project_id = Projects.project_id 
                          LEFT JOIN site_visit_clients 
                            ON site_visits.id = site_visit_clients.site_visit_id
                          LEFT JOIN users 
                            ON site_visits.marketer_id = users.user_id
                          LEFT JOIN users as drivers
                            ON site_visits.driver_id = drivers.user_id
                          WHERE site_visits.id = ?
                          GROUP BY site_visits.id
                          ORDER BY site_visits.created_at ASC;
                        `;

                      pool.query(
                        updatedSiteVisitQuery,
                        [id],
                        (err, updatedResults) => {
                          if (err) {
                            res.status(500).json({ error: err.message });
                            return;
                          }

                          if (updatedResults.length > 0) {
                            res.status(200).json(updatedResults[0]);
                          } else {
                            res.status(404).json({
                              message: "Updated site visit not found.",
                            });
                          }
                        }
                      );
                    }
                  );
                } else {
                  const seatsExceeded =
                    clientCount + 1 + passengersAssigned - numberOfSeats;
                  res.status(400).json({
                    message: "The selected vehicle does not have enough seats.",
                    exceeded_by: seatsExceeded,
                  });
                }
              });
            } else {
              res.status(404).json({
                message: "Available vehicle not found.",
              });
            }
          });
        } else {
          // Update site visit without vehicle
          const query = `
          UPDATE site_visits
          SET 
            vehicle_id = ?,
            pickup_location = ?, 
            pickup_date = ?, 
            pickup_time = ?, 
            remarks = ?, 
            status = ?, 
            driver_id = ?,
            project_id = ?,
          WHERE id = ?
        `;
          pool.query(
            query,
            [
              null,
              pickup_location,
              pickup_date,
              pickup_time,
              remarks,
              status === "pending" ? "approved" : status,
              driver_id,
              project_id,
              id,
            ],
            async (err, results) => {
              if (err) {
                res.status(500).json({ error: err.message });
                return;
              }

              await updateAndSendNotification();

              res
                .status(200)
                .json({ message: "Site visit updated successfully." });
            }
          );
        }
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    }
  );
  // Submit a survey for a completed site visit
  router.post("/submit-survey/:id", authenticateJWT, async (req, res) => {
    try {
      const siteVisitId = req.params.id;
      const userId = req.user.id;
      const {
        amount_reserved,
        booked,
        plot_details,
        reason_not_visited,
        reason_not_booked,
        visited,
      } = req.body;

      const checkSiteVisitQuery = `
        SELECT *
        FROM site_visits
        WHERE id = ? AND status = 'complete' AND marketer_id = ?
      `;
      pool.query(checkSiteVisitQuery, [siteVisitId, userId], (err, results) => {
        if (err) throw err;
        if (results.length > 0) {
          const insertSurveyQuery = `
            INSERT INTO site_visit_surveys (
              site_visit_id, 
              amount_reserved,
              booked,
              plot_details,
              reason_not_visited,
              reason_not_booked,
              visited
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
          `;
          pool.query(
            insertSurveyQuery,
            [
              siteVisitId,
              amount_reserved,
              booked,
              plot_details,
              reason_not_visited,
              reason_not_booked,
              visited,
            ],
            (err, result) => {
              if (err) throw err;

              // Update the site visit status to 'reviewed'
              const updateSiteVisitStatusQuery = `
                UPDATE site_visits
                SET status = 'reviewed'
                WHERE id = ?
              `;
              pool.query(
                updateSiteVisitStatusQuery,
                [siteVisitId],
                (err, result) => {
                  if (err) throw err;
                  res
                    .status(201)
                    .json({ message: "Survey submitted successfully." });
                }
              );
            }
          );
        } else {
          res.status(400).json({ message: "Invalid site visit or user." });
        }
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
  // Delete a single site visit by ID
  router.delete("/:id", authenticateJWT, async (req, res) => {
    try {
      const siteVisitId = req.params.id;

      // Step 1: Delete related rows from child tables
      const deleteChildRowsQuery = `
        DELETE svc, n, svs
        FROM site_visits sv
        LEFT JOIN site_visit_clients svc ON sv.id = svc.site_visit_id
        LEFT JOIN notifications n ON sv.id = n.site_visit_id
        LEFT JOIN site_visit_surveys svs ON sv.id = svs.site_visit_id
        WHERE sv.id = ?
      `;
      pool.query(
        deleteChildRowsQuery,
        [siteVisitId],
        (err, childDeleteResult) => {
          if (err) {
            res.status(500).json({ error: err.message });
            return;
          }

          // Step 2: Delete row from the site_visits table
          const deleteSiteVisitQuery = "DELETE FROM site_visits WHERE id = ?";
          pool.query(
            deleteSiteVisitQuery,
            [siteVisitId],
            (err, siteVisitDeleteResult) => {
              if (err) {
                res.status(500).json({ error: err.message });
                return;
              }

              res
                .status(200)
                .json({ message: "Site visit request deleted successfully" });
            }
          );
        }
      );
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
  return router;
};
