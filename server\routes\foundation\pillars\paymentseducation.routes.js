const express = require("express");
const pdfMakePrinter = require("pdfmake/src/printer");
const router = express.Router();

// Define your fonts
var fonts = {
  Roboto: {
    normal: "node_modules/roboto-font/fonts/Roboto/roboto-regular-webfont.ttf",
    bold: "node_modules/roboto-font/fonts/Roboto/roboto-bold-webfont.ttf",
    italic: "node_modules/roboto-font/fonts/Roboto/roboto-italic-webfont.ttf",
    bolditalics:
      "node_modules/roboto-font/fonts/Roboto/roboto-bolditalic-webfont.ttf",
  },
};

// Create a new printer with the fonts
var printer = new pdfMakePrinter(fonts);

// Define your dataToPdfRows function
function dataToPdfRows(data) {
  return data.map((item, index) => {
    return [
      { text: index + 1 ?? "", style: "tableCell" },
      { text: item.pay_institution ?? "", style: "tableCell" },
      { text: item.educ_name ?? "", style: "tableCell" },
      { text: item.student_level ?? "", style: "tableCell" },
      { text: item.pay_amount ?? "", style: "tableCell" },
      { text: item.pay_confirmation ?? "", style: "tableCell" },
      { text: item.pay_comment ?? "", style: "tableCell" },
    ];
  });
}

module.exports = (pool) => {
  // Download into PDF for report (TVET)
  router.get("/download-pdf", async (req, res) => {
    try {
      const startDate = req.query.startDate;
      const endDate = req.query.endDate;
      const query = `SELECT *
      FROM education
      JOIN payments ON education.educ_id = payments.student_id
      WHERE payments.created_at BETWEEN ? AND ?
      AND payments.student_level = 'University/TVET' 
      ORDER BY payments.created_at DESC;
      `;
      pool.query(query, [startDate, endDate], (err, results) => {
        if (err) throw err;

        // Sum up the total amount paid
        const totalAmountPaid = results
          .reduce((sum, item) => {
            const amount =
              typeof item.pay_amount === "string"
                ? parseFloat(item.pay_amount.replace(/,/g, ""))
                : item.pay_amount;
            return sum + amount;
          }, 0)
          .toLocaleString();
        // Count the number of payments
        const numberOfPayments = results.length;

        const docDefinition = {
          pageSize: "A4",
          pageOrientation: "landscape",
          content: [
            {
              table: {
                headerRows: 1,
                widths: [
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                ],
                body: [
                  [
                    {
                      text: "Index",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Institution paid for",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Name of the Student",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Student Level",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Amount Paid",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Confirmation Of Pay",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Comment",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                  ],
                ],
              },
              layout: {
                hLineWidth: function (i, node) {
                  return 0;
                },
                vLineWidth: function (i, node) {
                  return 0;
                },
                fillColor: function (rowIndex, node, columnIndex) {
                  return rowIndex % 2 === 0 ? "#D3D3D3" : null;
                },
              },
            },
            {
              text: `Total Amount Paid: ${totalAmountPaid}`,
              style: "totalAmount",
              margin: [0, 20, 0, 0],
            },
            {
              text: `Number of Payments: ${numberOfPayments}`,
              style: "totalAmount",
              margin: [0, 10, 0, 0],
            },
          ],
          styles: {
            tableHeader: {
              fontSize: 13,
              color: "white",
            },
            tableBody: {
              italic: true,
            },
          },
        };

        // Populate the body array with your data using dataToPdfRows
        docDefinition.content[0].table.body.push(...dataToPdfRows(results));

        // Create the PDF and send it as a response
        const pdfDoc = printer.createPdfKitDocument(docDefinition);
        res.setHeader("Content-Type", "application/pdf");
        pdfDoc.pipe(res);
        pdfDoc.end();
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
  // Download into PDF for report (HIGH SCHOOL)
  router.get("/downloadPDF-HighSchool", async (req, res) => {
    try {
      const startDate = req.query.startDate;
      const endDate = req.query.endDate;
      const query = `SELECT *
    FROM education
    JOIN payments ON education.educ_id = payments.student_id
    WHERE payments.created_at BETWEEN ? AND ?
    AND payments.student_level <> 'University/TVET'
    ORDER BY payments.created_at DESC;`;
      pool.query(query, [startDate, endDate], (err, results) => {
        if (err) throw err;

        // Sort the data by level and then by student name
        const levelOrder = {
          "Form 1": 1,
          "Form 2": 2,
          "Form 3": 3,
          "Form 4": 4,
        };
        results.sort((a, b) => {
          if (a.student_level === b.student_level) {
            return a.educ_name.localeCompare(b.educ_name);
          }
          return levelOrder[a.student_level] - levelOrder[b.student_level];
        });

        // Sum up the total amount paid
        const totalAmountPaid = results
          .reduce((sum, item) => {
            const amount =
              typeof item.pay_amount === "string"
                ? parseFloat(item.pay_amount.replace(/,/g, ""))
                : item.pay_amount;
            return sum + amount;
          }, 0)
          .toLocaleString();

        // Count the number of payments
        const numberOfPayments = results.length;

        const docDefinition = {
          pageSize: "A4",
          pageOrientation: "landscape",
          content: [
            {
              table: {
                headerRows: 1,
                widths: [
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                ],
                body: [
                  [
                    {
                      text: "Index",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "School/Institution",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Student Name",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Student Level",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Amount Paid",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Confirmation Of Pay",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Comment",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                  ],
                ],
              },
              layout: {
                hLineWidth: function (i, node) {
                  return 0;
                },
                vLineWidth: function (i, node) {
                  return 0;
                },
                fillColor: function (rowIndex, node, columnIndex) {
                  return rowIndex % 2 === 0 ? "#D3D3D3" : null;
                },
              },
            },
            // Add total amount paid and other stats section
            {
              text: `Total Amount Paid: ${totalAmountPaid}`,
              style: "totalAmount",
              margin: [0, 20, 0, 0],
            },
            {
              text: `Number of Payments: ${numberOfPayments}`,
              style: "totalAmount",
              margin: [0, 10, 0, 0],
            },
          ],
          styles: {
            tableHeader: {
              fontSize: 13,
              color: "white",
            },
            tableBody: {
              italic: true,
            },
            totalAmount: {
              fontSize: 15,
              bold: true,
            },
          },
        };

        // Populate the body array with your data using dataToPdfRows
        docDefinition.content[0].table.body.push(...dataToPdfRows(results));

        // Create the PDF and send it as a response
        const pdfDoc = printer.createPdfKitDocument(docDefinition);
        res.setHeader("Content-Type", "application/pdf");
        pdfDoc.pipe(res);
        pdfDoc.end();
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
  // Download into PDF for report (ALL PAYMENTS)
  router.get("/downloadPDF-ALL", async (req, res) => {
    try {
      const startDate = req.query.startDate;
      const endDate = req.query.endDate;
      const query = `SELECT *
        FROM education
        JOIN payments ON education.educ_id = payments.student_id
        WHERE payments.created_at BETWEEN ? AND ?
        ORDER BY payments.created_at DESC;      
        `;
      pool.query(query, [startDate, endDate], (err, results) => {
        if (err) throw err;

        // Sum up the total amount paid
        const totalAmountPaid = results
          .reduce((sum, item) => {
            const amount =
              typeof item.pay_amount === "string"
                ? parseFloat(item.pay_amount.replace(/,/g, ""))
                : item.pay_amount;
            return sum + amount;
          }, 0)
          .toLocaleString();
        // Count the number of payments
        const numberOfPayments = results.length;

        const docDefinition = {
          pageSize: "A4",
          pageOrientation: "landscape",
          content: [
            {
              table: {
                headerRows: 1,
                widths: [
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                ],
                body: [
                  [
                    {
                      text: "Index",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Institution paid for",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Name of the Student",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Student Level",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Amount Paid",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Confirmation Of Pay",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Comment",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                  ],
                ],
              },
              layout: {
                hLineWidth: function (i, node) {
                  return 0;
                },
                vLineWidth: function (i, node) {
                  return 0;
                },
                fillColor: function (rowIndex, node, columnIndex) {
                  return rowIndex % 2 === 0 ? "#D3D3D3" : null;
                },
              },
            },
            {
              text: `Total Amount Paid: ${totalAmountPaid}`,
              style: "totalAmount",
              margin: [0, 20, 0, 0],
            },
            {
              text: `Number of Payments: ${numberOfPayments}`,
              style: "totalAmount",
              margin: [0, 10, 0, 0],
            },
          ],
          styles: {
            tableHeader: {
              fontSize: 13,
              color: "white",
            },
            tableBody: {
              italic: true,
            },
          },
        };

        // Populate the body array with your data using dataToPdfRows
        docDefinition.content[0].table.body.push(...dataToPdfRows(results));

        // Create the PDF and send it as a response
        const pdfDoc = printer.createPdfKitDocument(docDefinition);
        res.setHeader("Content-Type", "application/pdf");
        pdfDoc.pipe(res);
        pdfDoc.end();
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
  //   Route to get All Schools Data
  router.get("/all-schools", async (req, res) => {
    try {
      pool.query(
        "SELECT DISTINCT pay_institution FROM payments;",
        (err, results) => {
          if (err) throw err;

          res.json(results);
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching School Specific Payments",
      });
    }
  });
  // Download into PDF for report (INSTITUTION SPECIFIC)
  router.get("/download-pdf-institution", async (req, res) => {
    try {
      const { selectedSchool, startDate, endDate } = req.query;

      if (!selectedSchool || !startDate || !endDate) {
        return res
          .status(400)
          .json({ error: "Missing required query parameters" });
      }

      const query = `
        SELECT * FROM education
        JOIN payments ON education.educ_id = payments.student_id
        WHERE payments.created_at BETWEEN ? AND ?
        AND payments.pay_institution = ?
        ORDER BY payments.created_at DESC;
      `;

      pool.query(
        query,
        [startDate, endDate, selectedSchool],
        (err, results) => {
          if (err) {
            console.error("Database query error:", err);
            return res.status(500).json({ error: "Database query failed" });
          }
          // Sum up the total amount paid
          const totalAmountPaid = results
            .reduce((sum, item) => {
              const amount =
                typeof item.pay_amount === "string"
                  ? parseFloat(item.pay_amount.replace(/,/g, ""))
                  : item.pay_amount;
              return sum + amount;
            }, 0)
            .toLocaleString();
          // Count the number of payments
          const numberOfPayments = results.length;

          const docDefinition = {
            pageSize: "A4",
            pageOrientation: "landscape",
            content: [
              {
                table: {
                  headerRows: 1,
                  widths: [
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                  ],
                  body: [
                    [
                      {
                        text: "Index",
                        fillColor: "#202A44",
                        style: "tableHeader",
                        bold: true,
                      },
                      {
                        text: "Institution paid for",
                        fillColor: "#202A44",
                        style: "tableHeader",
                        bold: true,
                      },
                      {
                        text: "Name of the Student",
                        fillColor: "#202A44",
                        style: "tableHeader",
                        bold: true,
                      },
                      {
                        text: "Student Level",
                        fillColor: "#202A44",
                        style: "tableHeader",
                        bold: true,
                      },
                      {
                        text: "Amount Paid",
                        fillColor: "#202A44",
                        style: "tableHeader",
                        bold: true,
                      },
                      {
                        text: "Confirmation Of Pay",
                        fillColor: "#202A44",
                        style: "tableHeader",
                        bold: true,
                      },
                      {
                        text: "Comment",
                        fillColor: "#202A44",
                        style: "tableHeader",
                        bold: true,
                      },
                    ],
                  ],
                },
                layout: {
                  hLineWidth: function (i, node) {
                    return 0;
                  },
                  vLineWidth: function (i, node) {
                    return 0;
                  },
                  fillColor: function (rowIndex, node, columnIndex) {
                    return rowIndex % 2 === 0 ? "#D3D3D3" : null;
                  },
                },
              },
              {
                text: `Total Amount Paid: ${totalAmountPaid}`,
                style: "totalAmount",
                margin: [0, 20, 0, 0],
              },
              {
                text: `Number of Payments: ${numberOfPayments}`,
                style: "totalAmount",
                margin: [0, 10, 0, 0],
              },
            ],
            styles: {
              tableHeader: {
                fontSize: 13,
                color: "white",
              },
              tableBody: {
                italic: true,
              },
            },
          };

          docDefinition.content[0].table.body.push(...dataToPdfRows(results));

          const pdfDoc = printer.createPdfKitDocument(docDefinition);
          res.setHeader("Content-Type", "application/pdf");
          pdfDoc.pipe(res);
          pdfDoc.end();
        }
      );
    } catch (error) {
      console.error("Error generating PDF:", error);
      res.status(500).json({ error: "Error generating PDF" });
    }
  });
  // Download into PDF for (ALL PAYMENTS) FOR EACH STUDENT
  router.get("/download-specific-student-payment", async (req, res) => {
    try {
      const studentId = req.query.studentId;
      const query = `SELECT payments.*, education.educ_name
      FROM payments
      JOIN education ON education.educ_id = payments.student_id
      WHERE payments.student_id = ?;
    `;
      pool.query(query, [studentId], (err, results) => {
        if (err) throw err;

        // Sum up the total amount paid
        const totalAmountPaid = results
          .reduce((sum, item) => {
            const amount =
              typeof item.pay_amount === "string"
                ? parseFloat(item.pay_amount.replace(/,/g, ""))
                : item.pay_amount;
            return sum + amount;
          }, 0)
          .toLocaleString();
        // Count the number of payments
        const numberOfPayments = results.length;

        const docDefinition = {
          pageSize: "A4",
          pageOrientation: "landscape",
          content: [
            {
              table: {
                headerRows: 1,
                widths: [
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                ],
                body: [
                  [
                    {
                      text: "Index",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Institution paid for",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Name of the Student",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Student Level",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Amount Paid",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Confirmation Of Pay",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Comment",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                  ],
                ],
              },
              layout: {
                hLineWidth: function (i, node) {
                  return 0;
                },
                vLineWidth: function (i, node) {
                  return 0;
                },
                fillColor: function (rowIndex, node, columnIndex) {
                  return rowIndex % 2 === 0 ? "#D3D3D3" : null;
                },
              },
            },
            {
              text: `Total Amount Paid: ${totalAmountPaid}`,
              style: "totalAmount",
              margin: [0, 20, 0, 0],
            },
            {
              text: `Number of Payments: ${numberOfPayments}`,
              style: "totalAmount",
              margin: [0, 10, 0, 0],
            },
          ],
          styles: {
            tableHeader: {
              fontSize: 13,
              color: "white",
            },
            tableBody: {
              italic: true,
            },
          },
        };

        // Populate the body array with your data using dataToPdfRows
        docDefinition.content[0].table.body.push(...dataToPdfRows(results));

        // Create the PDF and send it as a response
        const pdfDoc = printer.createPdfKitDocument(docDefinition);
        res.setHeader("Content-Type", "application/pdf");
        pdfDoc.pipe(res);
        pdfDoc.end();
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
  return router;
};
