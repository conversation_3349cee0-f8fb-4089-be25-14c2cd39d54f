require("dotenv").config();
const express = require("express");
const mysql = require("mysql2");
const bodyParser = require("body-parser");
const cors = require("cors");
const http = require("http");
const socketIO = require("socket.io");
const path = require("path");
const app = express();

// Set up the Express app and database connection pool
const defaultDBPool = mysql.createPool({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.LOGISTICS_DB,
  ssl: { rejectUnauthorized: false },
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  connectTimeout: 30000,
});

// Set up the Express app and database connection pool
const visitorManagementPool = mysql.createPool({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.VISITORS_MANAGEMENT_DB,
  ssl: { rejectUnauthorized: false },
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  connectTimeout: 60000,
});

// Set up the Express app and database connection pool
const workplanAutomationPool = mysql.createPool({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.WORKPLAN_AUTOMATION_DB,
  ssl: { rejectUnauthorized: false },
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  connectTimeout: 30000,
});

// Set up the Express app and database connection pool
const foundationPool = mysql.createPool({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.FOUNDATION_DB,
  ssl: { rejectUnauthorized: false },
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  connectTimeout: 30000,
});

// Set up the Express app and database connection pool
const feedbackPool = mysql.createPool({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.FEEDBACK_DB,
  ssl: { rejectUnauthorized: false },
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  connectTimeout: 30000,
});
// Set up the Express app and database connection pool
const valueAdditionPool = mysql.createPool({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.VALUE_DB,
  ssl: { rejectUnauthorized: false },
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  connectTimeout: 30000,
});

// Set up the Express app and database connection pool
const perfPool = mysql.createPool({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.PERFORMANCE_DB,
  ssl: { rejectUnauthorized: false },
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  connectTimeout: 30000,
});
// Set up the Express app and database connection pool
const optivenHomesPool = mysql.createPool({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.OPTIVEN_HOMES_DB,
  ssl: { rejectUnauthorized: false },
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  connectTimeout: 30000,
});
// Set up the Express app and database connection pool
const conferencePool = mysql.createPool({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.CONFERENCE_DB,
  ssl: { rejectUnauthorized: false },
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  connectTimeout: 30000,
});
// Set up the Express app and database connection pool
const surveyPool = mysql.createPool({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.SURVEY_DB,
  ssl: { rejectUnauthorized: false },
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  connectTimeout: 30000,
});
// Set up the Express app and database connection pool
const gmcConferencePool = mysql.createPool({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.GMC_CONFERENCE_DB,
  ssl: { rejectUnauthorized: false },
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  connectTimeout: 30000,
});
// Set up the Express app and database connection pool
const workDiaryPool = mysql.createPool({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.WORK_DIARY_DB,
  ssl: { rejectUnauthorized: false },
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  connectTimeout: 30000,
});
// Set up the Express app and database connection pool
const loyaltyPool = mysql.createPool({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.LOYALTY_DB,
  ssl: { rejectUnauthorized: false },
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  connectTimeout: 30000,
});
// Set up the Express app and database connection pool
const customerPortalPool = mysql.createPool({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.CUSTOMER_PORTAL_DB,
  ssl: { rejectUnauthorized: false },
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  connectTimeout: 30000,
});

// Set up the plots database connection pool
const plotsPool = mysql
  .createPool({
    user: process.env.PLOTS_DB_USER,
    password: process.env.PLOTS_DB_PASSWORD,
    host: process.env.PLOTS_DB_HOST,
    port: process.env.PLOTS_DB_PORT,
    database: process.env.PLOTS_DB_NAME,
    ssl: { rejectUnauthorized: false },
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    connectTimeout: 30000,
  })
  .promise();

// Check plots database connection
plotsPool
  .getConnection()
  .then((connection) => {
    console.log("Connected to the plots database");
    connection.release();
  })
  .catch((err) => {
    console.error("Error connecting to the plots database:", err.message);
  });

// Check database connection
defaultDBPool.getConnection((err, connection) => {
  if (err) {
    console.error("Error connecting to the logistics database:", err.message);
  } else {
    console.log("Connected to the logistics database");
    connection.release();
  }
});

// Check database connection
visitorManagementPool.getConnection((err, connection) => {
  if (err) {
    console.error(
      "Error connecting to the visitors management database:",
      err.message
    );
  } else {
    console.log("Connected to the visitors management database");
    connection.release();
  }
});

// Check database connection
workplanAutomationPool.getConnection((err, connection) => {
  if (err) {
    console.error(
      "Error connecting to the workplan automation database:",
      err.message
    );
  } else {
    console.log("Connected to the workplan automation database");
    connection.release();
  }
});

// Check database connection
foundationPool.getConnection((err, connection) => {
  if (err) {
    console.error("Error connecting to the foundation database:", err.message);
  } else {
    console.log("Connected to the foundation database");
    connection.release();
  }
});

// Check database connection
feedbackPool.getConnection((err, connection) => {
  if (err) {
    console.error("Error connecting to the feedback database:", err.message);
  } else {
    console.log("Connected to the feedback database");
    connection.release();
  }
});
// Check database connection
valueAdditionPool.getConnection((err, connection) => {
  if (err) {
    console.error(
      "Error connecting to the Value Addition database:",
      err.message
    );
  } else {
    console.log("Connected to the Value Addition database");
    connection.release();
  }
});
// Check database connection
perfPool.getConnection((err, connection) => {
  if (err) {
    console.error("Error connecting to the logistics database:", err.message);
  } else {
    console.log("Connected to the Performance database");
    connection.release();
  }
});
// Check database connection
optivenHomesPool.getConnection((err, connection) => {
  if (err) {
    console.error(
      "Error connecting to the optiven homes database:",
      err.message
    );
  } else {
    console.log("Connected to the optiven homes  database");
    connection.release();
  }
});

// Check database connection
conferencePool.getConnection((err, connection) => {
  if (err) {
    console.error("Error connecting to the conference database:", err.message);
  } else {
    console.log("Connected to the conference database");
    connection.release();
  }
});
// Check database connection
gmcConferencePool.getConnection((err, connection) => {
  if (err) {
    console.error(
      "Error connecting to the GMC Conference database:",
      err.message
    );
  } else {
    console.log("Connected to the GMC Conference database");
    connection.release();
  }
});
// Check database connection
surveyPool.getConnection((err, connection) => {
  if (err) {
    console.error("Error connecting to the survey database:", err.message);
  } else {
    console.log("Connected to the survey database");
    connection.release();
  }
});

// Check database connection
workDiaryPool.getConnection((err, connection) => {
  if (err) {
    console.error("Error connecting to the work diary database:", err.message);
  } else {
    console.log("Connected to the work diary database");
    connection.release();
  }
});

// Check database connection
loyaltyPool.getConnection((err, connection) => {
  if (err) {
    console.error("Error connecting to the loyalty database:", err.message);
  } else {
    console.log("Connected to the loyalty database");
    connection.release();
  }
});

// Check database connection
customerPortalPool.getConnection((err, connection) => {
  if (err) {
    console.error("Error connecting to the CP database:", err.message);
  } else {
    console.log("Connected to the CP database");
    connection.release();
  }
});

// Create an HTTP server instance and attach the Express app to it
const server = http.createServer(app);

// Initialize a Socket.IO instance and attach it to the HTTP server
const io = socketIO(server, {
  cors: {
    origin: [
      "http://localhost:3000",
      "https://localhost:3000",
      "https://*************/",
      "https://www.workspace.optiven.co.ke",
      "https://workspace.optiven.co.ke",
    ],
    methods: ["GET", "POST"],
    allowedHeaders: ["Content-Type", "Authorization"],
    credentials: true,
  },
});

// Import auth routes
const login = require("./routes/auth/login.routes");
const logout = require("./routes/auth/logout.routes");
const users = require("./routes/auth/users.routes");

// Import logistics routes
const sites = require("./routes/logistics/sites/sites.routes");
const vehicles = require("./routes/logistics/vehicles/vehicles.routes");
const siteVisitRequests = require("./routes/logistics/site-visit-requests/siteVisitRequests.routes");
const siteVisits = require("./routes/logistics/site-visits/siteVisit.routes");
const drivers = require("./routes/logistics/drivers/drivers.routes");
const vehicleRequests = require("./routes/logistics/vehicle-requests/vehicleRequests.routes");
const clients = require("./routes/logistics/clients/clients.routes");
const notifications = require("./routes/logistics/notifications/notifications.routes");
const specialAssignment = require("./routes/logistics/special-assignment/specialAssignment.routes");

// Import visitors management routes
const visitors = require("./routes/visitors-management/visitors/visitors.routes");
const interviews = require("./routes/visitors-management/interviews/interview.routes");
const parking = require("../server/routes/visitors-management/parking/parking.routes");

// Import workplan automation routes
const workplan = require("./routes/workplan-automation/workplan.routes");
const workplanActivities = require("./routes/workplan-automation/workplan_activities.routes");
const workplanReports = require("./routes/workplan-automation/workplan_reports.routes");
// Teams and Region
const teams = require("./routes/teams/teams.routes");
const region = require("./routes/regions/regions.routes");

// Import feedback routes
const feedback = require("./routes/feedback/feedback.routes");

// Import Map routes
const plots = require("./routes/maps/plots.routes");

// Import Foundation routes
const events = require("./routes/foundation/events/events.routes");
const donors = require("./routes/foundation/donors/donors.routes");
const education = require("./routes/foundation/pillars/education.routes");
const environment = require("./routes/foundation/pillars/environment.routes");
const health = require("./routes/foundation/pillars/health.routes");
const poverty = require("./routes/foundation/pillars/poverty.routes");
const books = require("./routes/foundation/books/books.routes");
const issuance = require("./routes/foundation/books/issuance.routes");
const sales = require("./routes/foundation/books/sales.routes");
const amounts = require("./routes/foundation/pillars/amounts.routes");
const payments = require("./routes/foundation/pillars/payments.routes");
const initiatives = require("./routes/foundation/pillars/initiatives.routes.js");

// Import Value Addition Routes
const casualLabourer = require("./routes/value-addition/casualLabourer.routes");
const labour = require("./routes/value-addition/labour.routes");
const projects = require("./routes/value-addition/projects.routes");
const suppliers = require("./routes/value-addition/suppliers.routes");
const valueAdditionsTasks = require("./routes/value-addition/tasks.routes");
const valueAddition = require("./routes/value-addition/value_additions.routes");
const constructionManager = require("./routes/value-addition/construction_managers.routes");
const value_addition_users = require("./routes/value-addition/value_addition_users.routes");
const requisition = require("./routes/value-addition/requisition.routes");
const valueAdditionPayments = require("./routes/value-addition/payments.routes");
const level = require("./routes/value-addition/levels.routes");
const level_value_addition = require("./routes/value-addition/level_value_addition.routes.js");
const value_addition_level = require("./routes/value-addition/value_addition_level.routes.js");

// Import Performance Routes
const tasks = require("./routes/performance-report/tasks.routes");
const monthlysales = require("./routes/performance-report/monthlysales.routes");
const referal = require("./routes/performance-report/referal.routes");
const countyRoutes = require("./routes/performance-report/county.routes");
const regionRoutes = require("./routes/performance-report/region.routes");
const chartRoutes = require("./routes/performance-report/chart.routes");
const mibRoutes = require("./routes/performance-report/mib.routes");
const fowRoutes = require("./routes/performance-report/fow.routes");
const categoryRoutes = require("./routes/performance-report/category.routes");
const payEduc = require("./routes/foundation/pillars/paymentseducation.routes");
// import Optiven homes routes
const homesClients = require("./routes/optiven-Homes/homesClients.routes.js");
const designPhase = require("./routes/optiven-Homes/designPhase.routes.js");
const roomsRoutes = require("./routes/conference/rooms.routes.js");
const roomBookingRoutes = require("./routes/conference/roomBooking.routes.js");
const eventBookingRoutes = require("./routes/conference/eventBooking.routes.js");
// import system survey routes
const surveyRoutes = require("./routes/sys_survey/survey.routes.js");

const customersurevyRoutes = require("./routes/sys_survey/customersurevy.routes.js");

const gmcRoomsRoutes = require("./routes/gmc-conference/gmcRooms.routes.js");
const gmcRoomBookingRoutes = require("./routes/gmc-conference/gmcRoomBooking.routes.js");
// import work-diary routes
const workDiary = require("./routes/work-diary/work_diary.routes.js");
// import work-diary routes
const loyalty = require("./routes/loyalty/customers.routes.js");
// import CP routes
const cp = require("./routes/referrals/referrals.routes.js");
// import CP routes
const cpadmin = require("./routes/campaigns/campaigns.routes.js");
const salesAgreementRoutes = require("./routes/performance-report/salesAgreement.routes.js");

// Configure CORS options
const corsOptions = {
  origin: [
    "http://localhost:3000",
    "https://localhost:3000",
    "https://*************",
    "https://www.workspace.optiven.co.ke",
    "https://workspace.optiven.co.ke",
    "*",
  ],
  methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization"],
  credentials: true,
};
app.use(cors(corsOptions));

// Apply middlewares
app.use(cors(corsOptions));
app.use(express.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());
// Apply route middlewares
app.use("/api/login", login(defaultDBPool));
app.use("/api/logout", logout);
app.use("/api/users", users(defaultDBPool));
app.use("/api/sites", sites(defaultDBPool));
app.use("/api/vehicles", vehicles(defaultDBPool));
app.use("/api/site-visit-requests", siteVisitRequests(defaultDBPool, io));
app.use("/api/site-visits", siteVisits(defaultDBPool, io));
app.use("/api/drivers", drivers(defaultDBPool));
app.use("/api/vehicle-requests", vehicleRequests(defaultDBPool));
app.use("/api/clients", clients(defaultDBPool));
app.use("/api/notifications", notifications(defaultDBPool));
app.use("/api/visitors", visitors(visitorManagementPool));
app.use("/api/special-assignments", specialAssignment(defaultDBPool));
app.use("/api/interviews", interviews(visitorManagementPool));
app.use("/api/workplans", workplan(workplanAutomationPool));
app.use("/api/workplan-activities", workplanActivities(workplanAutomationPool));
app.use("/api/workplan-reports", workplanReports(workplanAutomationPool));
app.use("/api/reserve-parking", parking(visitorManagementPool));
app.use("/api/reserved-parking", parking(visitorManagementPool));
app.use("/api/feedback", feedback(feedbackPool));
app.use("/api/teams", teams(defaultDBPool));
app.use("/api/regions", region(defaultDBPool));
app.use("/api/events", events(foundationPool));
app.use("/api/donors", donors(foundationPool));
app.use("/api/education", education(foundationPool));
app.use("/api/environment", environment(foundationPool));
app.use("/api/health", health(foundationPool));
app.use("/api/poverty", poverty(foundationPool));
app.use("/api/books", books(foundationPool));
app.use("/api/issuance", issuance(foundationPool));
app.use("/api/sales", sales(foundationPool));
app.use("/api/amounts", amounts(foundationPool));
app.use("/api/payments", payments(foundationPool));
app.use("/api/payments-education", payEduc(foundationPool));
app.use("/api/initiatives", initiatives(foundationPool));

app.use("/api/plots", plots(plotsPool));
app.use("/api/casual-labourers", casualLabourer(valueAdditionPool));
app.use("/api/labour", labour(valueAdditionPool));
app.use("/api/projects", projects(valueAdditionPool));
app.use("/api/suppliers", suppliers(valueAdditionPool));
app.use("/api/tasks", valueAdditionsTasks(valueAdditionPool));
app.use("/api/value-additions", valueAddition(valueAdditionPool));
app.use("/api/construction-managers", constructionManager(valueAdditionPool));
app.use("/api/value_addition_users", value_addition_users(valueAdditionPool));
app.use("/api/requisitions", requisition(valueAdditionPool));
app.use(
  "/api/value_addition_payments",
  valueAdditionPayments(valueAdditionPool)
);
app.use("/api/levels", level(valueAdditionPool));
app.use("/api/level-value-additions", level_value_addition(valueAdditionPool));
app.use("/api/value-addition-level", value_addition_level(valueAdditionPool));

app.use("/api/perf", tasks(perfPool));
app.use("/api/referal", referal(perfPool));
app.use("/api/monthly-sales", monthlysales(perfPool));
app.use("/api/county", countyRoutes(perfPool));
app.use("/api/event", regionRoutes(perfPool));
app.use("/api/chart", chartRoutes(perfPool));
app.use("/api/mib", mibRoutes(defaultDBPool));
app.use("/api/sla", salesAgreementRoutes(defaultDBPool));
app.use("/api/category", categoryRoutes(perfPool));

//conference
app.use("/api/rooms", roomsRoutes(conferencePool));
app.use("/api/bookings", roomBookingRoutes(conferencePool));
app.use("/api/event-bookings", eventBookingRoutes(conferencePool));

//gmc conference
app.use("/api/gmc-rooms", gmcRoomsRoutes(gmcConferencePool));
app.use("/api/gmc-bookings", gmcRoomBookingRoutes(gmcConferencePool));
// Optiven Homes.
app.use("/api/homes-clients", homesClients(optivenHomesPool));
app.use("/api/design", designPhase(optivenHomesPool));

// system survey
app.use("/api/survey", surveyRoutes(surveyPool));
app.use("/api/customer-survey", customersurevyRoutes(surveyPool));

// work diary
app.use("/api/work-diary", workDiary(workDiaryPool));
app.use("/api/fow", fowRoutes(perfPool));

// loyalty
app.use("/api/loyalty", loyalty(loyaltyPool));

// referrals
app.use("/api/referrals", cp(customerPortalPool));
// admin
app.use("/api/campaigns", cpadmin(customerPortalPool));

// Serve static files from the uploads directory
app.use("/uploads", express.static(path.join(__dirname, "uploads")));

app.use(
  "/uploads",
  express.static(path.join(__dirname, "../server/routes/uploads"))
);

// Set up Socket.IO connection handling
io.on("connection", (socket) => {
  console.log("Connected");
  socket.on("disconnect", () => {
    `1`;
    console.log("Disconnected");
  });
});

app.get("/", (req, res) => {
  res.sendFile(path.join(__dirname, "/index.html"));
});

app.get("/api/test-db", (req, res) => {
  workDiaryPool.query("SELECT 1 + 1 AS solution", (err, results) => {
    if (err) {
      return res
        .status(500)
        .json({ message: "Error connecting to database", error: err });
    }
    res.json({
      message: "Database connected successfully",
      result: results[0].solution,
    });
  });
});

// Listen for incoming requests
server.listen(8080, () => {
  console.log("Server started on port 8080");
});
