import React, { useEffect, useState } from "react";
import Sidebar from "../../components/sidebar/Sidebar";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import useMeasure from "react-use-measure";
import { Filter, X } from "react-feather";
import Mo<PERSON> from "react-modal";
import {
  useDragControls,
  useMotionValue,
  useAnimate,
  motion,
} from "framer-motion";
import { toast } from "react-toastify";

// Utility functions for formatting date and time
const formatDate = (dateString) => {
  if (!dateString) return null;
  const date = new Date(dateString);
  return date
    .toLocaleDateString("en-CA", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    })
    .replace(/\//g, "-");
};

const formatTime = (timeString) => {
  if (!timeString) return null;
  const time = new Date(`1970-01-01T${timeString}`);
  return time.toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "numeric",
    hour12: true,
  });
};

// DragCloseDrawer Component for the details drawer
const DragCloseDrawer = ({ open, setOpen, children }) => {
  const [scope, animate] = useAnimate();
  const [drawerRef, { height }] = useMeasure();
  const y = useMotionValue(0);
  const controls = useDragControls();

  const handleClose = async () => {
    await new Promise((resolve) => setTimeout(resolve, 50)); // 50ms delay

    if (!scope.current) {
      console.error("Element associated with the scope is not found.");
      return;
    }

    await animate(scope.current, {
      opacity: [1, 0],
    });

    const yStart = typeof y.get() === "number" ? y.get() : 0;

    await animate("#drawer", {
      y: [yStart, height],
    });

    setOpen(false);
  };

  return (
    <>
      {open && (
        <motion.div
          ref={scope}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          onClick={handleClose}
          className="fixed inset-0 z-50 bg-neutral-950/70"
        >
          <motion.div
            id="drawer"
            ref={drawerRef}
            onClick={(e) => e.stopPropagation()}
            initial={{ y: "100%" }}
            animate={{ y: "0%" }}
            transition={{
              ease: "easeInOut",
            }}
            className="absolute bottom-0 h-[75vh] w-full overflow-hidden rounded-t-3xl bg-neutral-900"
            style={{ y }}
            drag="y"
            dragControls={controls}
            onDragEnd={() => {
              if (y.get() >= 100) {
                handleClose();
              }
            }}
            dragListener={false}
            dragConstraints={{
              top: 0,
              bottom: 0,
            }}
            dragElastic={{
              top: 0,
              bottom: 0.5,
            }}
          >
            <div className="absolute left-0 right-0 top-0 z-10 flex justify-center bg-neutral-900 p-4">
              <button
                onPointerDown={(e) => {
                  controls.start(e);
                }}
                className="h-2 w-14 cursor-grab touch-none rounded-full bg-neutral-700 active:cursor-grabbing"
              ></button>
            </div>
            <div className="relative z-0 h-full overflow-y-scroll p-4 pt-12">
              {children}
            </div>
          </motion.div>
        </motion.div>
      )}
    </>
  );
};

// Modal for rating candidates
const RatingModal = ({ isOpen, onClose, onSubmit }) => {
  const [rating, setRating] = useState(5);

  const handleSubmit = () => {
    if (rating >= 1 && rating <= 10) {
      onSubmit(rating);
      onClose();
    } else {
      toast.error("Please select a rating between 1 and 10.");
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      overlayClassName="fixed inset-0 bg-black bg-opacity-50"
    >
      <div className="relative bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
        <button
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-800"
          onClick={onClose}
        >
          <X size={24} />
        </button>
        <h3 className="text-lg font-bold">Rate the Candidate</h3>

        <input
          type="range"
          min={1}
          max={10}
          value={rating}
          onChange={(e) => setRating(Number(e.target.value))}
          className="range range-purple mt-4"
          step={1}
        />
        <div className="flex w-full justify-between px-2 text-xs text-purple-600">
          <span>1</span>
          <span>2</span>
          <span>3</span>
          <span>4</span>
          <span>5</span>
          <span>6</span>
          <span>7</span>
          <span>8</span>
          <span>9</span>
          <span>10</span>
        </div>

        <button onClick={handleSubmit} className="btn btn-primary mt-4">
          Submit Rating
        </button>
      </div>
    </Modal>
  );
};

// Modal for progressing candidates
const ProgressionModal = ({ isOpen, onClose, onProceed }) => {
  const [nextDate, setNextDate] = useState("");
  const [nextTime, setNextTime] = useState("");

  const handleProceed = () => {
    if (nextDate && nextTime) {
      onProceed(nextDate, nextTime);
      onClose();
    } else {
      toast.error("Please enter both date and time for the next interview.");
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      overlayClassName="fixed inset-0 bg-black bg-opacity-50"
    >
      <div className="relative bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
        <button
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-800"
          onClick={onClose}
        >
          <X size={24} />
        </button>
        <h3 className="text-lg font-bold">Proceed to Next Level</h3>

        <input
          type="date"
          value={nextDate}
          onChange={(e) => setNextDate(e.target.value)}
          className="w-full mt-2 p-2 border"
          placeholder="Next Interview Date"
        />
        <input
          type="time"
          value={nextTime}
          onChange={(e) => setNextTime(e.target.value)}
          className="w-full mt-2 p-2 border"
          placeholder="Next Interview Time"
        />
        <button onClick={handleProceed} className="btn btn-primary mt-4">
          Proceed
        </button>
      </div>
    </Modal>
  );
};

// Modal for dropping candidates
const DropModal = ({ isOpen, onClose, onDrop }) => {
  const [dropReason, setDropReason] = useState("");

  const handleDrop = () => {
    if (dropReason) {
      onDrop(dropReason);
      onClose();
    } else {
      toast.error("Please enter a reason for dropping the candidate.");
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      overlayClassName="fixed inset-0 bg-black bg-opacity-50"
    >
      <div className="relative bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
        <button
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-800"
          onClick={onClose}
        >
          <X size={24} />
        </button>
        <h3 className="text-lg font-bold">Drop Candidate</h3>
        <textarea
          value={dropReason}
          onChange={(e) => setDropReason(e.target.value)}
          className="w-full mt-2 p-2 border"
          placeholder="Reason for dropping"
        />
        <button onClick={handleDrop} className="btn btn-error mt-4">
          Drop
        </button>
      </div>
    </Modal>
  );
};

// Modal for recommending candidates
const RecommendModal = ({ isOpen, onClose, onRecommend }) => {
  const [comment, setComment] = useState("");
  const [recommend, setRecommend] = useState(false);

  const handleSubmit = () => {
    if (comment.trim() === "") {
      toast.error("Please enter a comment.");
      return;
    }
    onRecommend(comment, recommend);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      overlayClassName="fixed inset-0 bg-black bg-opacity-50"
    >
      <div className="relative bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
        <button
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-800"
          onClick={onClose}
        >
          <X size={24} />
        </button>
        <h3 className="text-lg font-bold">Recommend Candidate</h3>

        <textarea
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          className="w-full mt-2 p-2 border"
          placeholder="Enter your comment"
        />

        <div className="mt-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={recommend}
              onChange={() => setRecommend(true)}
              className="mr-2"
            />
            Recommend
          </label>
          <label className="flex items-center mt-2">
            <input
              type="checkbox"
              checked={!recommend}
              onChange={() => setRecommend(false)}
              className="mr-2"
            />
            Do Not Recommend
          </label>
        </div>

        <button onClick={handleSubmit} className="btn btn-info mt-4">
          Confirm Recommendation
        </button>
      </div>
    </Modal>
  );
};

// Main component
const ViewScheduledInterviews = () => {
  const [interviews, setInterviews] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [positionSearch, setPositionSearch] = useState("");
  const [levelSearch, setLevelSearch] = useState("");
  const [statusSearch, setStatusSearch] = useState("");
  const [selectedInterview, setSelectedInterview] = useState(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [ratingModalOpen, setRatingModalOpen] = useState(false);
  const [progressionModalOpen, setProgressionModalOpen] = useState(false);
  const [dropModalOpen, setDropModalOpen] = useState(false);
  const [recommendModalOpen, setRecommendModalOpen] = useState(false);
  const [hasRated, setHasRated] = useState(false); // New state to track if the candidate has been rated
  const token = useSelector((state) => state.user.token);
  const userId = useSelector((state) => state.user.user.user_id); // Correctly get user ID
  const navigate = useNavigate();

  Modal.setAppElement("#root");

  useEffect(() => {
    const fetchInterviews = async () => {
      try {
        const response = await fetch(
          "https://workspace.optiven.co.ke/api/interviews",
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        const data = await response.json();

        if (Array.isArray(data)) {
          setInterviews(data);
        } else {
          console.error("Invalid response format. Expected an array.");
        }
      } catch (error) {
        console.error(error);
      }
    };

    fetchInterviews();
  }, [token]);

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const handlePositionSearch = (e) => {
    setPositionSearch(e.target.value);
  };

  const handleLevelSearch = (e) => {
    setLevelSearch(e.target.value);
  };

  const handleStatusSearch = (e) => {
    setStatusSearch(e.target.value);
  };

  const filteredInterviews = interviews.filter((interview) => {
    const matchesSearchTerm =
      (interview.name &&
        interview.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (interview.phone_number && interview.phone_number.includes(searchTerm));

    const matchesPosition =
      !positionSearch ||
      interview.position.toLowerCase().includes(positionSearch.toLowerCase());

    const matchesLevel =
      !levelSearch ||
      interview.current_level.toLowerCase().includes(levelSearch.toLowerCase());

    const matchesStatus =
      !statusSearch ||
      (interview.status &&
        interview.status.toLowerCase().includes(statusSearch.toLowerCase()));

    return (
      matchesSearchTerm && matchesPosition && matchesLevel && matchesStatus
    );
  });

  const handleAdmit = async (interviewId, currentLevel) => {
    try {
      const currentTime = new Date();
      const hours = currentTime.getHours().toString().padStart(2, "0");
      const minutes = currentTime.getMinutes().toString().padStart(2, "0");
      const seconds = currentTime.getSeconds().toString().padStart(2, "0");
      const currentTimeString = `${hours}:${minutes}:${seconds}`;

      let reportTimeField;
      switch (currentLevel.toLowerCase()) {
        case "general":
          reportTimeField = "report_time";
          break;
        case "technical":
          reportTimeField = "technical_report_time";
          break;
        case "executive":
          reportTimeField = "executive_report_time";
          break;
        default:
          reportTimeField = "report_time";
      }

      const response = await fetch(
        `https://workspace.optiven.co.ke/api/interviews/admit/${interviewId}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({ [reportTimeField]: currentTimeString }),
        }
      );

      if (response.ok) {
        toast.success("Candidate admitted successfully.");
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } else {
        toast.error("Failed to admit candidate.");
      }
    } catch (error) {
      console.error("Error admitting candidate:", error);
      toast.error("An error occurred while admitting the candidate.");
    }
  };

  const deleteInterview = (interviewId) => {
    fetch(`https://workspace.optiven.co.ke/api/interviews/${interviewId}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        setInterviews((prevInterviews) =>
          prevInterviews.filter((interview) => interview.id !== interviewId)
        );
      })
      .catch((error) => {
        console.error("There was a problem with the fetch operation:", error);
      });
  };

  // const handleDetailsClick = (interview) => {
  //   setSelectedInterview(interview);
  //   refreshInterviewDetails(interview.id); // Load interview details and rating status on details click
  //   setDrawerOpen(true);
  // };

  const handleRateCandidate = async (rating) => {
    try {
      const payload = {
        rating,
        userId: userId, // Correctly pass userId
      };

      const response = await fetch(
        `https://workspace.optiven.co.ke/api/interviews/rate/${selectedInterview.id}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(payload),
        }
      );

      if (response.ok) {
        setHasRated(true); // Ensure hasRated is set to true
        setRatingModalOpen(false);
        await refreshInterviewDetails(selectedInterview.id); // Fetch updated interview data
        toast.success("Rating submitted successfully.");
      } else {
        // Log the response status and text for debugging
        const errorData = await response.json();
        console.error("Failed to rate candidate:", errorData);
        toast.error("Failed to rate candidate.");
      }
    } catch (error) {
      toast.error("An error occurred while rating the candidate.");
      console.error("An error occurred while rating the candidate:", error);
    }
  };

  // Ensure that `hasRated` and decision logic is correctly checked to display the buttons
  const handleDetailsClick = (interview) => {
    try {
      // Use try-catch when parsing to avoid invalid JSON issues
      const parsedDecisionBy = Array.isArray(interview.decision_by)
        ? interview.decision_by
        : JSON.parse(interview.decision_by || "[]");
      const parsedDecisionComments = Array.isArray(interview.decision_comments)
        ? interview.decision_comments
        : JSON.parse(interview.decision_comments || "[]");
      const parsedDecisionRecommend = Array.isArray(
        interview.decision_recommend
      )
        ? interview.decision_recommend
        : JSON.parse(interview.decision_recommend || "[]");

      // Set the selected interview with parsed data
      setSelectedInterview({
        ...interview,
        decision_by: parsedDecisionBy,
        decision_comments: parsedDecisionComments,
        decision_recommend: parsedDecisionRecommend,
      });

      setDrawerOpen(true);
    } catch (error) {
      console.error("Error parsing interview details:", error);
    }
  };

  // Function to refresh interview details, including whether the current user has rated
  const refreshInterviewDetails = async (interviewId) => {
    try {
      const response = await fetch(
        `https://workspace.optiven.co.ke/api/interviews/${interviewId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        console.error(
          `Failed to fetch interview details: ${response.statusText}`
        );
        return;
      }

      const updatedInterview = await response.json();

      // Log the response data for debugging
      console.log("Updated Interview Details:", updatedInterview);

      const ratedByUsers = updatedInterview.rated_by_users
        ? JSON.parse(JSON.stringify(updatedInterview.rated_by_users))
        : {};
      const currentLevel = updatedInterview.current_level.toLowerCase();
      const hasRated = ratedByUsers[currentLevel]?.includes(userId.toString());

      // Set the average ratings for all levels
      setSelectedInterview({
        ...updatedInterview,
        generalAverageRating: updatedInterview.generalAverageRating, // Store general average rating
        technicalAverageRating: updatedInterview.technicalAverageRating, // Store technical average rating
        executiveAverageRating: updatedInterview.executiveAverageRating, // Store executive average rating
      });

      setHasRated(hasRated);
    } catch (error) {
      console.error(
        "An error occurred while refreshing interview details:",
        error
      );
    }
  };

  const handleProceedToNextLevel = async (nextDate, nextTime) => {
    try {
      const response = await fetch(
        `https://workspace.optiven.co.ke/api/interviews/proceed/${selectedInterview.id}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            next_interview_date: nextDate,
            next_interview_time: nextTime,
          }),
        }
      );

      if (response.ok) {
        setProgressionModalOpen(false);
        await refreshInterviewDetails(selectedInterview.id); // Fetch updated interview data
        toast.success("Proceeded to the next level successfully.");
      } else {
        toast.error("Failed to proceed candidate to next level.");
      }
    } catch (error) {
      toast.error(
        "An error occurred while proceeding the candidate to the next level."
      );
      console.error(
        "An error occurred while proceeding the candidate to the next level:",
        error
      );
    }
  };

  const handleDropCandidate = async (reason) => {
    try {
      const response = await fetch(
        `https://workspace.optiven.co.ke/api/interviews/drop/${selectedInterview.id}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            drop_reason: reason,
            status: "Dropped",
          }),
        }
      );

      if (response.ok) {
        await refreshInterviewDetails(selectedInterview.id); // Refresh data
        setDropModalOpen(false);
        // setDropReason("");
        toast.success("Candidate dropped successfully.");
      } else {
        toast.error("Failed to drop candidate.");
      }
    } catch (error) {
      toast.error("An error occurred while dropping the candidate.");
      console.error("An error occurred while dropping the candidate:", error);
    }
  };

  const handleRecommendCandidate = async (comment, recommend) => {
    try {
      const response = await fetch(
        `https://workspace.optiven.co.ke/api/interviews/recommend/${selectedInterview.id}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            comment,
            recommend,
            userId,
          }),
        }
      );

      if (response.ok) {
        await refreshInterviewDetails(selectedInterview.id); // Refresh data
        setRecommendModalOpen(false);
        toast.success("Candidate decision submitted successfully.");
      } else {
        toast.error("Failed to submit decision.");
      }
    } catch (error) {
      toast.error("An error occurred while submitting the decision.");
      console.error("An error occurred while submitting the decision:", error);
    }
  };

  // Function to calculate average rating
  function calculateAverageRating(ratings) {
    if (!ratings) return "Not yet rated";

    const ratingsArray = ratings
      .split(",")
      .map(Number)
      .filter((n) => !isNaN(n));
    if (ratingsArray.length === 0) return "Not yet rated";

    const total = ratingsArray.reduce((acc, curr) => acc + curr, 0);
    const average = total / ratingsArray.length;

    return average.toFixed(1);
  }

  // Function to return badge class based on the level
  const getBadgeClassByLevel = (level) => {
    switch (level.toLowerCase()) {
      case "general":
        return "bg-blue-500 text-white";
      case "technical":
        return "bg-yellow-500 text-white";
      case "executive":
        return "bg-green-500 text-white";
      default:
        return "bg-gray-400 text-white"; // Default gray badge for unknown level
    }
  };

  // Calculate average ratings
  const generalAverageRating = selectedInterview
    ? calculateAverageRating(selectedInterview.general_ratings)
    : "Not yet rated";
  const technicalAverageRating = selectedInterview
    ? calculateAverageRating(selectedInterview.technical_ratings)
    : "Not yet rated";
  const executiveAverageRating = selectedInterview
    ? calculateAverageRating(selectedInterview.executive_ratings)
    : "Not yet rated";

  const StarRating = ({ rating }) => {
    const totalStars = 5; // 5 full stars
    const validRating = Math.max(0, Math.min(rating / 2, totalStars)); // Convert 10-scale to 5-scale
    const fullStars = Math.floor(validRating);
    const halfStar = validRating - fullStars >= 0.5;
    const emptyStars = totalStars - fullStars - (halfStar ? 1 : 0);

    return (
      <div className="rating rating-half" style={{ fontSize: "24px" }}>
        {Array.from({ length: fullStars }).map((_, i) => (
          <React.Fragment key={`full-${i}`}>
            <input
              type="radio"
              name={`rating-${i}`}
              className="rating-hidden"
            />
            <input
              type="radio"
              name={`rating-${i}`}
              className="mask mask-star-2 mask-half-1 bg-green-500"
              style={{ fontSize: "24px" }}
            />
            <input
              type="radio"
              name={`rating-${i}`}
              className="mask mask-star-2 mask-half-2 bg-green-500"
              style={{ fontSize: "24px" }}
            />
          </React.Fragment>
        ))}
        {halfStar && (
          <React.Fragment>
            <input type="radio" name="rating-half" className="rating-hidden" />
            <input
              type="radio"
              name="rating-half"
              className="mask mask-star-2 mask-half-1 bg-green-500"
              style={{ fontSize: "24px" }}
            />
            <input
              type="radio"
              name="rating-half"
              className="mask mask-star-2 mask-half-2 bg-gray-400"
              style={{ fontSize: "24px" }}
            />
          </React.Fragment>
        )}
        {Array.from({ length: emptyStars }).map((_, i) => (
          <React.Fragment key={`empty-${i}`}>
            <input type="radio" name={`empty-${i}`} className="rating-hidden" />
            <input
              type="radio"
              name={`empty-${i}`}
              className="mask mask-star-2 mask-half-1 bg-gray-400"
              style={{ fontSize: "24px" }}
            />
            <input
              type="radio"
              name={`empty-${i}`}
              className="mask mask-star-2 mask-half-2 bg-gray-400"
              style={{ fontSize: "24px" }}
            />
          </React.Fragment>
        ))}
      </div>
    );
  };

  return (
    <Sidebar>
      <div className="container px-4 py-2 mx-auto">
        <div className="flex justify-center mb-2 space-x-4">
          {/* Search filters */}
          <div className="flex items-center border border-gray-300 rounded-md w-72 px-3 py-2">
            <Filter size={16} className="mr-2" />
            <input
              type="text"
              className="flex-1 focus:outline-none"
              placeholder="Search by Name or Phone"
              value={searchTerm}
              onChange={handleSearch}
            />
          </div>
          <div className="flex items-center border border-gray-300 rounded-md w-72 px-3 py-2">
            <Filter size={16} className="mr-2" />
            <input
              type="text"
              className="flex-1 focus:outline-none"
              placeholder="Search by Position"
              value={positionSearch}
              onChange={handlePositionSearch}
            />
          </div>
          <div className="flex items-center border border-gray-300 rounded-md w-72 px-3 py-2">
            <Filter size={16} className="mr-2" />
            <input
              type="text"
              className="flex-1 focus:outline-none"
              placeholder="Search by Level"
              value={levelSearch}
              onChange={handleLevelSearch}
            />
          </div>
          <div className="flex items-center border border-gray-300 rounded-md w-72 px-3 py-2">
            <Filter size={16} className="mr-2" />
            <input
              type="text"
              className="flex-1 focus:outline-none"
              placeholder="Search by Status"
              value={statusSearch}
              onChange={handleStatusSearch}
            />
          </div>
        </div>

        <div className="overflow-x-auto card bg-base-100 shadow-xl w-full">
          <table className="table table-compact w-full">
            <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Interview Date</th>
                <th>Interview Time</th>
                <th>Position</th>
                <th>Level</th>
                <th>Status</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              {filteredInterviews.map((interview) => (
                <tr key={interview.id}>
                  <td>{interview.name}</td>
                  <td>{interview.email}</td>
                  <td>{formatDate(interview.interview_date)}</td>
                  <td>{formatTime(interview.interview_time)}</td>
                  <td>{interview.position}</td>
                  <td>{interview.current_level}</td>
                  <td>{interview.status}</td>
                  <td>
                    <button
                      className="btn btn-outline btn-sm ml-1"
                      onClick={() => handleDetailsClick(interview)}
                    >
                      Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {selectedInterview && (
          <DragCloseDrawer open={drawerOpen} setOpen={setDrawerOpen}>
            <div className="mx-auto max-w-4xl space-y-6 p-4 bg-neutral-800 rounded-lg shadow-lg text-neutral-200">
              <h2 className="text-3xl font-bold text-center">
                Interviewee Details
              </h2>

              <div className="flex justify-between space-x-6">
                {/* Left Section: Interview Details */}
                <div className="w-1/2 bg-neutral-900 p-4 rounded-lg shadow-md">
                  <h3 className="text-xl font-semibold mb-4">
                    Basic Information
                  </h3>
                  <p>
                    <strong>Name:</strong> {selectedInterview.name}
                  </p>
                  <p>
                    <strong>Email:</strong> {selectedInterview.email}
                  </p>
                  <p>
                    <strong>Phone Number:</strong>{" "}
                    {selectedInterview.phone_number}
                  </p>
                  <p>
                    <strong>Position:</strong> {selectedInterview.position}
                  </p>
                  <p className="flex items-center">
                    <strong>Level:</strong>
                    <span
                      className={`ml-2 py-1 px-3 rounded-full text-sm font-semibold ${getBadgeClassByLevel(
                        selectedInterview.current_level
                      )}`}
                    >
                      {selectedInterview.current_level}
                    </span>
                  </p>
                  <p>
                    <strong>Status:</strong> {selectedInterview.status}
                  </p>
                  <p>
                    <strong>Interview Date:</strong>{" "}
                    {formatDate(selectedInterview.interview_date)}
                  </p>
                  <p>
                    <strong>Interview Time:</strong>{" "}
                    {formatTime(selectedInterview.interview_time)}
                  </p>
                  <p>
                    <strong>Report Time (General):</strong>{" "}
                    {selectedInterview.report_time
                      ? formatTime(selectedInterview.report_time)
                      : "N/A"}
                  </p>
                  <p>
                    <strong>Report Time (Technical):</strong>{" "}
                    {selectedInterview.technical_report_time
                      ? formatTime(selectedInterview.technical_report_time)
                      : "N/A"}
                  </p>
                  <p>
                    <strong>Report Time (Executive):</strong>{" "}
                    {selectedInterview.executive_report_time
                      ? formatTime(selectedInterview.executive_report_time)
                      : "N/A"}
                  </p>

                  <div className="bg-neutral-900 p-4 rounded-lg shadow-md">
                    <h3 className="text-xl font-semibold mb-4">
                      Decision History
                    </h3>

                    {/* Ensure decision_by is an array before mapping */}
                    {Array.isArray(selectedInterview.decision_by) &&
                    selectedInterview.decision_by.length > 0 ? (
                      selectedInterview.decision_by.map(
                        (decision_by, index) => (
                          <div key={index} className="mb-2">
                            <p>
                              <strong>Decision By:</strong> {decision_by}
                            </p>
                            {/* Similarly check for decision_comments and decision_recommend */}
                            <p>
                              <strong>Comment:</strong>{" "}
                              {Array.isArray(
                                selectedInterview.decision_comments
                              ) && selectedInterview.decision_comments[index]
                                ? selectedInterview.decision_comments[index]
                                : "N/A"}
                            </p>
                            <p>
                              <strong>Recommendation:</strong>{" "}
                              {Array.isArray(
                                selectedInterview.decision_recommend
                              ) && selectedInterview.decision_recommend[index]
                                ? selectedInterview.decision_recommend[index]
                                : "N/A"}
                            </p>
                            <hr className="my-2" />
                          </div>
                        )
                      )
                    ) : (
                      <p>No decisions made yet.</p>
                    )}
                  </div>
                </div>

                {/* Right Section: Attached CV and Ratings */}
                <div className="w-1/2 space-y-4">
                  <div className="bg-neutral-900 p-4 rounded-lg shadow-md">
                    <h3 className="text-xl font-semibold mb-4">Attached CV</h3>

                    {/* Display CV in iframe for PDFs, else show a download link */}
                    {selectedInterview.cv_file_path ? (
                      <>
                        {selectedInterview.cv_file_path.endsWith(".pdf") ? (
                          <iframe
                            src={`https://workspace.optiven.co.ke/uploads/${selectedInterview.cv_file_path}`}
                            title="CV"
                            width="100%"
                            height="300px"
                            className="border border-gray-300 rounded-md shadow-sm"
                          />
                        ) : (
                          <p className="text-white">
                            Preview not available for this file type. Please
                            download the file to view it.
                          </p>
                        )}
                        <a
                          href={`https://workspace.optiven.co.ke/api/interviews/download/${selectedInterview.cv_file_path}`}
                          download
                          className="btn btn-primary mt-2"
                        >
                          Download CV
                        </a>
                      </>
                    ) : (
                      <p className="text-white">No CV attached.</p>
                    )}
                  </div>

                  {/* Ratings Section */}
                  <div className="bg-neutral-900 p-4 rounded-lg shadow-md">
                    <h3 className="text-xl font-semibold mb-4">Ratings</h3>
                    <p>
                      <strong>General Average Rating:</strong>{" "}
                      {generalAverageRating}
                      <StarRating rating={generalAverageRating} />
                    </p>
                    <p>
                      <strong>Technical Average Rating:</strong>{" "}
                      {technicalAverageRating}
                      <StarRating rating={technicalAverageRating} />
                    </p>
                    <p>
                      <strong>Executive Average Rating:</strong>{" "}
                      {executiveAverageRating}
                      <StarRating rating={executiveAverageRating} />
                    </p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-center space-x-4 mt-4">
                {/* Edit and Delete buttons visible when interview is in early stages */}
                {selectedInterview.status !== "Completed" &&
                  !selectedInterview.report_time && (
                    <>
                      <button
                        className="btn btn-warning text-white"
                        onClick={() =>
                          navigate(
                            `/edit-scheduled-interviews/${selectedInterview.id}`
                          )
                        }
                      >
                        Edit
                      </button>
                      <button
                        className="btn btn-error text-white"
                        onClick={() => deleteInterview(selectedInterview.id)}
                      >
                        Delete
                      </button>
                    </>
                  )}

                {/* Admit button logic based on the current level */}
                {/* Admit at General Level */}
                {!selectedInterview.report_time &&
                  selectedInterview.current_level === "General" && (
                    <button
                      className="btn btn-outline btn-info"
                      onClick={() =>
                        handleAdmit(selectedInterview.id, "report_time")
                      }
                    >
                      Admit at General Level
                    </button>
                  )}

                {/* Admit at Technical Level */}
                {selectedInterview.current_level === "Technical" &&
                  !selectedInterview.technical_report_time && (
                    <button
                      className="btn btn-outline btn-info"
                      onClick={() =>
                        handleAdmit(
                          selectedInterview.id,
                          "technical_report_time"
                        )
                      }
                    >
                      Admit at Technical Level
                    </button>
                  )}

                {/* Admit at Executive Level */}
                {selectedInterview.current_level === "Executive" &&
                  !selectedInterview.executive_report_time && (
                    <button
                      className="btn btn-outline btn-info"
                      onClick={() =>
                        handleAdmit(
                          selectedInterview.id,
                          "executive_report_time"
                        )
                      }
                    >
                      Admit at Executive Level
                    </button>
                  )}

                {/* Rate button appears after admitting for each level */}
                {selectedInterview.current_level === "General" &&
                  selectedInterview.report_time &&
                  !hasRated && (
                    <button
                      className="btn btn-primary"
                      onClick={() => setRatingModalOpen(true)}
                    >
                      Rate
                    </button>
                  )}

                {selectedInterview.current_level === "Technical" &&
                  selectedInterview.technical_report_time &&
                  !hasRated && (
                    <button
                      className="btn btn-primary"
                      onClick={() => setRatingModalOpen(true)}
                    >
                      Rate
                    </button>
                  )}

                {selectedInterview.current_level === "Executive" &&
                  selectedInterview.executive_report_time &&
                  !hasRated && (
                    <button
                      className="btn btn-primary"
                      onClick={() => setRatingModalOpen(true)}
                    >
                      Rate
                    </button>
                  )}

                {/* Proceed to Next Level and Drop buttons visible after rating, but not for Executive level */}
                {hasRated &&
                  selectedInterview.current_level !== "Executive" && (
                    <>
                      <button
                        className="btn btn-success"
                        onClick={() => setProgressionModalOpen(true)}
                      >
                        Proceed to Next Level
                      </button>
                      <button
                        className="btn btn-error"
                        onClick={() => setDropModalOpen(true)}
                      >
                        Drop Candidate
                      </button>
                    </>
                  )}

                {/* For Executive level: Only Recommend / Decision button should appear after rating */}
                {selectedInterview.current_level === "Executive" &&
                  hasRated &&
                  !selectedInterview.decision && (
                    <button
                      className="btn btn-info"
                      onClick={() => setRecommendModalOpen(true)}
                    >
                      Recommend / Decision
                    </button>
                  )}

                {/* After making a decision, no buttons should appear */}
              </div>
            </div>
          </DragCloseDrawer>
        )}

        {/* Modal Components */}
        {selectedInterview && (
          <>
            <RatingModal
              isOpen={ratingModalOpen}
              onClose={() => setRatingModalOpen(false)}
              onSubmit={handleRateCandidate}
            />
            <ProgressionModal
              isOpen={progressionModalOpen}
              onClose={() => setProgressionModalOpen(false)}
              onProceed={handleProceedToNextLevel}
            />
            <DropModal
              isOpen={dropModalOpen}
              onClose={() => setDropModalOpen(false)}
              onDrop={handleDropCandidate}
            />
            <RecommendModal
              isOpen={recommendModalOpen}
              onClose={() => setRecommendModalOpen(false)}
              onRecommend={handleRecommendCandidate}
            />
          </>
        )}
      </div>
    </Sidebar>
  );
};

export default ViewScheduledInterviews;
