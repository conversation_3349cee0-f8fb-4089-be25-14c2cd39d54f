import React from "react";
import { toast } from "react-toastify";

const DeleteConfirmation = ({ isOpen, onConfirm, onCancel }) => {
  const handleConfirm = () => {
    onConfirm();
    toast.success("Item deleted successfully!", {
      position: "top-center",
      autoClose: 2000,
      hideProgressBar: true,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
    });
  };

  const handleCancel = () => {
    onCancel();
    toast.info("Deletion canceled.", {
      position: "top-center",
      autoClose: 2000,
      hideProgressBar: true,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
    });
  };

  return (
    <>
    {isOpen && (
    <div className="border rounded-lg shadow relative max-w-sm">
      <div className="p-6 pt-0 text-center">
        <h3 className="text-xl font-normal text-gray-500 mt-5 mb-6">
        Are you sure you want to delete?
        </h3>
        <button
          onClick={handleConfirm}
          className="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-base inline-flex items-center px-3 py-2.5 text-center mr-2"
        >
          Yes, I'm sure
        </button>
        <button
          onClick={handleCancel}
          className="text-gray-900 bg-white hover:bg-gray-100 focus:ring-4 focus:ring-cyan-200 border border-gray-200 font-medium inline-flex items-center rounded-lg text-base px-3 py-2.5 text-center"
        >
          No, cancel
        </button>
      </div>
    </div>
    )}
    </>
  );
};

export default DeleteConfirmation;
