const express = require("express");
const router = express.Router();
const pdfMake = require("pdfmake/build/pdfmake");
const vfsFonts = require("pdfmake/build/vfs_fonts");

// Register fonts
pdfMake.vfs = vfsFonts.pdfMake.vfs;

module.exports = (pool) => {
  // GET all regions
  router.get("/", (req, res) => {
    const query = `SELECT * FROM regions`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        res.json(results);
      }
    });
  });

  // GET regions for region 1
  router.get("/region1", (req, res) => {
    const regions = ["LEOPARDS", "LIONS", "HQ PLATINUM", "TIGERS"];
    const regionQuery = regions.map((region) => `'${region}'`).join(", ");

    const query = `
    SELECT * 
    FROM regions
    WHERE region IN (${regionQuery})`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        res.json(results);
      }
    });
  });

  // GET regions for region 2
  router.get("/region2", (req, res) => {
    const regions = [
      "PUMA GREEN",
      "PUMA YELLOW",
      "JAGUAR GREEN",
      "JAGUAR YELLOW",
      "GLOBAL PLATINUM",
    ];
    const regionQuery = regions.map((region) => `'${region}'`).join(", ");

    const query = `
    SELECT * 
    FROM regions
    WHERE region IN (${regionQuery})`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        res.json(results);
      }
    });
  });

  // GET a specific region by ID
  router.get("/:id", (req, res) => {
    const { id } = req.params;
    const query = `SELECT * FROM regions WHERE id = ?`;

    pool.query(query, [id], (err, result) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else if (result.length > 0) {
        res.json(result[0]);
      } else {
        res.status(404).json({ message: "Region not found" });
      }
    });
  });

  // CREATE new regions
  router.post("/", (req, res) => {
    const regions = Array.isArray(req.body) ? req.body : [req.body];

    // Check if regions is an array and it's not empty
    if (!regions.length) {
      return res.status(400).json({ message: "Invalid region data" });
    }

    const query =
      "INSERT INTO regions (startDate, endDate, region, event, dataCollected, siteVisitDone, scheduledVisits, sales, referrals, cost) VALUES (?,?,?, ?, ?, ?, ?, ?, ?, ?)";

    // Array to store the results of each INSERT query
    const results = [];

    regions.forEach((regionData) => {
      const {
        startDate,
        endDate,
        region: regionName,
        event,
        dataCollected,
        siteVisitDone,
        scheduledVisits,
        sales,
        referrals,
        cost,
      } = regionData;

      pool.query(
        query,
        [
          startDate,
          endDate,
          regionName,
          event,
          dataCollected,
          siteVisitDone,
          scheduledVisits,
          sales,
          referrals,
          cost,
        ],
        (err, result) => {
          if (err) {
            console.error(err);
            results.push({
              error: true,
              message: "Error creating region",
            });
          } else {
            results.push({
              error: false,
              message: "Region created successfully",
            });
          }

          // Check if all queries have completed
          if (results.length === regions.length) {
            // Check if any errors occurred during the queries
            const hasErrors = results.some((result) => result.error);
            if (hasErrors) {
              res.status(500).json({ message: "Server Error" });
            } else {
              res.json({ message: "Regions created successfully" });
            }
          }
        }
      );
    });
  });

  // DELETE a region by ID
  router.delete("/:id", (req, res) => {
    const { id } = req.params;
    const query = `DELETE FROM regions WHERE id = ?`;

    pool.query(query, [id], (err, result) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else if (result.affectedRows > 0) {
        res.json({ message: "Region deleted successfully" });
      } else {
        res.status(404).json({ message: "Region not found" });
      }
    });
  });

  // NEW: Generate a PDF report for regions using pdfMake
  router.get("/download-pdf/region-report", (req, res) => {
    const { startDate, endDate } = req.query; // Assuming you pass the dates in the query parameters

    // Fetch data from the database
    const query = `SELECT * FROM regions
    WHERE startDate >= '${startDate}' AND endDate <= '${endDate}'`;
    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        return res.status(500).json({ message: "Server Error" });
      }

      // Create a definition for the table with zebra stripe pattern, auto width, and centered alignment
      const tableDefinition = {
        pageSize: "A4",
        pageOrientation: "landscape",
        content: [
          {
            text: `Region Report (${startDate} - ${endDate})`,
            style: "header",
            alignment: "center",
          },
          { text: "\n" },
          {
            table: {
              headerRows: 1,
              widths: [
                "auto",
                "auto",
                "auto",
                "auto",
                "auto",
                "auto",
                "auto",
                "auto",
                "auto",
              ], // Adjust the widths as needed
              body: [
                [
                  "ID",

                  "Region",
                  "Event",
                  "Data Collected",
                  "Site Visits Done",
                  "Scheduled Visits",
                  "Sales",
                  "Referrals",
                  "Cost",
                ],
                ...results.map((record, index) => [
                  {
                    text: record.id.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },

                  {
                    text: record.region,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.event,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.dataCollected,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.siteVisitDone,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.scheduledVisits,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.sales,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.referrals,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.cost,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                ]),
              ],
              // Center the table
              alignment: "center",
            },
          },
        ],
        styles: {
          header: {
            fontSize: 16,
            bold: true,
          },
        },
      };

      // Create the PDF document
      const pdfDoc = pdfMake.createPdf(tableDefinition);

      // Set response headers
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        'attachment; filename="region_report.pdf"'
      );

      // Pipe the PDF content to the response
      pdfDoc.getBuffer((buffer) => {
        res.end(buffer);
      });
    });
  });

  // Download PDF report for regions - HOS
  router.get("/download-pdf/region-report-hos", (req, res) => {
    const { startDate, endDate } = req.query;

    // Fetch data from the database - Adjust the query based on your data structure
    const query = `
    SELECT 
      *
    FROM regions 
    WHERE startDate >= '${startDate}' AND endDate <= '${endDate}' AND region IN ('LEOPARDS', 'LIONS', 'HQ PLATINUM', 'TIGERS', 'HQ BLUE')`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        return res.status(500).json({ message: "Server Error" });
      }

      const tableDefinition = {
        pageSize: "A4",
        pageOrientation: "landscape",
        content: [
          {
            text: `Region Report (${startDate} - ${endDate})`,
            style: "header",
            alignment: "center",
          },
          { text: "\n" },
          {
            table: {
              headerRows: 1,
              widths: [
                "auto",
                "auto",
                "auto",
                "auto",
                "auto",
                "auto",
                "auto",
                "auto",
                "auto",
              ], // Adjust the widths as needed
              body: [
                [
                  "ID",

                  "Region",
                  "Event",
                  "Data Collected",
                  "Site Visits Done",
                  "Scheduled Visits",
                  "Sales",
                  "Referrals",
                  "Cost",
                ],
                ...results.map((record, index) => [
                  {
                    text: record.id.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },

                  {
                    text: record.region,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.event,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.dataCollected,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.siteVisitDone,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.scheduledVisits,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.sales,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.referrals,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.cost,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                ]),
              ],
              // Center the table
              alignment: "center",
            },
          },
        ],
        styles: {
          header: {
            fontSize: 16,
            bold: true,
          },
        },
      };

      // Create the PDF document
      const pdfDoc = pdfMake.createPdf(tableDefinition);

      // Set response headers
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        'attachment; filename="region_report_hos.pdf"'
      );

      // Pipe the PDF content to the response
      pdfDoc.getBuffer((buffer) => {
        res.end(buffer);
      });
    });
  });

  // Download PDF report for regions - GM
  router.get("/download-pdf/region-report-gm", (req, res) => {
    const { startDate, endDate } = req.query;

    // Fetch data from the database - Adjust the query based on your data structure
    const query = `
    SELECT 
      *
    FROM regions 
    WHERE startDate >= '${startDate}' AND endDate <= '${endDate}' AND region IN ('PUMA GREEN',
    'PUMA YELLOW',
    'JAGUAR GREEN',
    'JAGUAR YELLOW',
    'GLOBAL PLATINUM',
    'KAREN BLUE')`;

    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        return res.status(500).json({ message: "Server Error" });
      }

      const tableDefinition = {
        pageSize: "A4",
        pageOrientation: "landscape",
        content: [
          {
            text: `Region Report (${startDate} - ${endDate})`,
            style: "header",
            alignment: "center",
          },
          { text: "\n" },
          {
            table: {
              headerRows: 1,
              widths: [
                "auto",
                "auto",
                "auto",
                "auto",
                "auto",
                "auto",
                "auto",
                "auto",
                "auto",
              ], // Adjust the widths as needed
              body: [
                [
                  "ID",

                  "Region",
                  "Event",
                  "Data Collected",
                  "Site Visits Done",
                  "Scheduled Visits",
                  "Sales",
                  "Referrals",
                  "Cost",
                ],
                ...results.map((record, index) => [
                  {
                    text: record.id.toString(),
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },

                  {
                    text: record.region,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.event,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.dataCollected,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.siteVisitDone,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.scheduledVisits,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.sales,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.referrals,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                  {
                    text: record.cost,
                    fillColor: index % 2 === 0 ? "#D3D3D3" : null, // Zebra stripe pattern
                  },
                ]),
              ],
              // Center the table
              alignment: "center",
            },
          },
        ],
        styles: {
          header: {
            fontSize: 16,
            bold: true,
          },
        },
      };

      // Create the PDF document
      const pdfDoc = pdfMake.createPdf(tableDefinition);

      // Set response headers
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        'attachment; filename="region_report_gm.pdf"'
      );

      // Pipe the PDF content to the response
      pdfDoc.getBuffer((buffer) => {
        res.end(buffer);
      });
    });
  });

  // NEW: Calculate the total cost of the current month
  router.get("/total-cost/current-month", (req, res) => {
    // Get the current month and year in local time zone
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = currentDate.getFullYear();

    // Fetch data from the database for the current month and year in local time zone
    const query = `
    SELECT cost
    FROM regions
    WHERE MONTH(startDate) = ? AND YEAR(startDate) = ?`;

    pool.query(query, [currentMonth, currentYear], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        // Calculate the total cost for the current month
        const totalCost = results.reduce((sum, record) => {
          const cost = parseFloat(record.cost);
          if (!isNaN(cost)) {
            return sum + cost;
          } else {
            console.error(
              `Invalid cost value for record: ${JSON.stringify(record)}`
            );
            // Handle the error as needed, e.g., skip this record
            return sum;
          }
        }, 0);

        // Prepare the response in the desired format
        const responseData = {
          totalCost: totalCost.toFixed(2),
        };

        // Send the response to the frontend
        res.json(responseData);
      }
    });
  });

  // NEW: Calculate the total cost of the previous month
  router.get("/total-cost/previous-month", (req, res) => {
    // Get the first day of the current month in local time zone
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth(); // Subtract 1 to get the previous month
    const currentYear = currentDate.getFullYear();

    // If the current month is January, adjust the year and month accordingly
    const prevMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const prevYear = currentMonth === 0 ? currentYear - 1 : currentYear;

    // Fetch data from the database for the previous month and year in local time zone
    const query = `
    SELECT cost
    FROM regions
    WHERE MONTH(startDate) = ? AND YEAR(startDate) = ?`;

    pool.query(query, [prevMonth + 1, prevYear], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        // Calculate the total cost for the previous month
        const totalCost = results.reduce((sum, record) => {
          const cost = parseFloat(record.cost);
          if (!isNaN(cost)) {
            return sum + cost;
          } else {
            console.error(
              `Invalid cost value for record: ${JSON.stringify(record)}`
            );
            // Handle the error as needed, e.g., skip this record
            return sum;
          }
        }, 0);

        // Prepare the response in the desired format
        const responseData = {
          totalCost: totalCost.toFixed(2),
        };

        // Send the response to the frontend
        res.json(responseData);
      }
    });
  });
   // NEW: Calculate the total cost of the current month
   router.get("/total-cost/current-month/karen", (req, res) => {
    // Get the current month and year in local time zone
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = currentDate.getFullYear();

    // Fetch data from the database for the current month and year in local time zone
    const query = `
    SELECT cost
    FROM regions
    WHERE  region IN ('PUMA GREEN',
    'PUMA YELLOW',
    'JAGUAR GREEN',
    'JAGUAR YELLOW',
    'GLOBAL PLATINUM',
    'KAREN BLUE') AND MONTH(startDate) = ? AND YEAR(startDate) = ?`;

    pool.query(query, [currentMonth, currentYear], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        // Calculate the total cost for the current month
        const totalCost = results.reduce((sum, record) => {
          const cost = parseFloat(record.cost);
          if (!isNaN(cost)) {
            return sum + cost;
          } else {
            console.error(
              `Invalid cost value for record: ${JSON.stringify(record)}`
            );
            // Handle the error as needed, e.g., skip this record
            return sum;
          }
        }, 0);

        // Prepare the response in the desired format
        const responseData = {
          totalCost: totalCost.toFixed(2),
        };

        // Send the response to the frontend
        res.json(responseData);
      }
    });
  });

  // NEW: Calculate the total cost of the previous month
  router.get("/total-cost/previous-month", (req, res) => {
    // Get the first day of the current month in local time zone
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth(); // Subtract 1 to get the previous month
    const currentYear = currentDate.getFullYear();

    // If the current month is January, adjust the year and month accordingly
    const prevMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const prevYear = currentMonth === 0 ? currentYear - 1 : currentYear;

    // Fetch data from the database for the previous month and year in local time zone
    const query = `
    SELECT cost
    FROM regions
    WHERE  region IN ('PUMA GREEN',
    'PUMA YELLOW',
    'JAGUAR GREEN',
    'JAGUAR YELLOW',
    'GLOBAL PLATINUM',
    'KAREN BLUE') AND MONTH(startDate) = ? AND YEAR(startDate) = ?`;

    pool.query(query, [prevMonth + 1, prevYear], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        // Calculate the total cost for the previous month
        const totalCost = results.reduce((sum, record) => {
          const cost = parseFloat(record.cost);
          if (!isNaN(cost)) {
            return sum + cost;
          } else {
            console.error(
              `Invalid cost value for record: ${JSON.stringify(record)}`
            );
            // Handle the error as needed, e.g., skip this record
            return sum;
          }
        }, 0);

        // Prepare the response in the desired format
        const responseData = {
          totalCost: totalCost.toFixed(2),
        };

        // Send the response to the frontend
        res.json(responseData);
      }
    });
  });

   // NEW: Calculate the total cost of the current month
   router.get("/total-cost/current-month/absa", (req, res) => {
    // Get the current month and year in local time zone
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = currentDate.getFullYear();

    // Fetch data from the database for the current month and year in local time zone
    const query = `
    SELECT cost
    FROM regions
    WHERE region IN ('LEOPARDS', 'LIONS', 'HQ PLATINUM', 'TIGERS', 'HQ BLUE')AND  MONTH(startDate) = ? AND YEAR(startDate) = ?`;

    pool.query(query, [currentMonth, currentYear], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        // Calculate the total cost for the current month
        const totalCost = results.reduce((sum, record) => {
          const cost = parseFloat(record.cost);
          if (!isNaN(cost)) {
            return sum + cost;
          } else {
            console.error(
              `Invalid cost value for record: ${JSON.stringify(record)}`
            );
            // Handle the error as needed, e.g., skip this record
            return sum;
          }
        }, 0);

        // Prepare the response in the desired format
        const responseData = {
          totalCost: totalCost.toFixed(2),
        };

        // Send the response to the frontend
        res.json(responseData);
      }
    });
  });

  // NEW: Calculate the total cost of the previous month
  router.get("/total-cost/previous-month", (req, res) => {
    // Get the first day of the current month in local time zone
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth(); // Subtract 1 to get the previous month
    const currentYear = currentDate.getFullYear();

    // If the current month is January, adjust the year and month accordingly
    const prevMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const prevYear = currentMonth === 0 ? currentYear - 1 : currentYear;

    // Fetch data from the database for the previous month and year in local time zone
    const query = `
   SELECT cost
    FROM regions
    WHERE region IN ('LEOPARDS', 'LIONS', 'HQ PLATINUM', 'TIGERS', 'HQ BLUE')AND  MONTH(startDate) = ? AND YEAR(startDate) = ?`;

    pool.query(query, [prevMonth + 1, prevYear], (err, results) => {
      if (err) {
        console.error(err);
        res.status(500).json({ message: "Server Error" });
      } else {
        // Calculate the total cost for the previous month
        const totalCost = results.reduce((sum, record) => {
          const cost = parseFloat(record.cost);
          if (!isNaN(cost)) {
            return sum + cost;
          } else {
            console.error(
              `Invalid cost value for record: ${JSON.stringify(record)}`
            );
            // Handle the error as needed, e.g., skip this record
            return sum;
          }
        }, 0);

        // Prepare the response in the desired format
        const responseData = {
          totalCost: totalCost.toFixed(2),
        };

        // Send the response to the frontend
        res.json(responseData);
      }
    });
  });


  return router;
};
