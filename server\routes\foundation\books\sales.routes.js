const express = require("express");
const pdfMakePrinter = require("pdfmake/src/printer");
const authenticateJWT = require("../../../middleware/authenticateJWT");
const router = express.Router();

// Define your fonts
var fonts = {
  Roboto: {
    normal: "node_modules/roboto-font/fonts/Roboto/roboto-regular-webfont.ttf",
    bold: "node_modules/roboto-font/fonts/Roboto/roboto-bold-webfont.ttf",
    italic: "node_modules/roboto-font/fonts/Roboto/roboto-italic-webfont.ttf",
    bolditalics:
      "node_modules/roboto-font/fonts/Roboto/roboto-bolditalic-webfont.ttf",
  },
};

// Create a new printer with the fonts
var printer = new pdfMakePrinter(fonts);

// Define your dataToPdfRows function
function dataToPdfRows(data) {
  return data.map((item, index) => [
    index + 1 || 0,
    item.book_name || "",
    item.book_code || "",
    item.book_price || 0,
    item.book_copies || 0,
    item.person_responsible || "",
    item.book_amount_expected || 0,
    item.book_amount_given || 0,
  ]);
}

module.exports = (pool, io) => {
  router.post("/", async (req, res) => {
    const {
      book_code,
      book_copies,
      store,
      person_responsible,
      book_amount_given,
      book_status,
      book_issued,
      book_event,
      event_date,
    } = req.body;
    const state = "PENDING";
    try {
      // Check if book_code is provided
      if (!book_code) {
        return res.status(400).json({
          success: false,
          message: "book_code is required.",
        });
      }

      // Fetch the book name and price based on the provided book_code
      const getBookData = () => {
        return new Promise((resolve, reject) => {
          pool.query(
            "SELECT book_name, book_price FROM book_uploads WHERE book_code = ?",
            [book_code],
            (err, result) => {
              if (err) {
                reject(err);
              } else {
                resolve(result);
              }
            }
          );
        });
      };

      const bookData = await getBookData();

      // Check if book_data is fetched successfully
      if (bookData.length === 0) {
        return res.status(404).json({
          success: false,
          message: "Book not found for the provided book_code.",
        });
      }

      const { book_name, book_price } = bookData[0];

      // Insert sale data into the 'book_sales' table
      const insertSale = () => {
        return new Promise((resolve, reject) => {
          pool.query(
            "INSERT INTO `book_sales` (`book_name`, `book_code`, `book_price`, `book_copies`, `store`, `person_responsible`, `book_amount_given`, `book_status`, `book_issued`, `created_at`, `book_condition`, `book_event`, `event_date`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?, ?)",
            [
              book_name,
              book_code,
              book_price,
              book_copies,
              store,
              person_responsible,
              book_amount_given,
              book_status,
              book_issued,
              state,
              book_event,
              event_date,
            ],
            (err, result) => {
              if (err) {
                reject(err);
              } else {
                resolve(result);
              }
            }
          );
        });
      };

      const result = await insertSale();

      // Check if the insertion was successful
      if (result.affectedRows > 0) {
        res.status(201).json({
          success: true,
          message: "Book Sale added successfully!",
        });
      } else {
        res.status(500).json({
          success: false,
          message: "Failed to add Book Sale.",
        });
      }
    } catch (error) {
      console.error("Error:", error);
      res.status(500).json({
        success: false,
        message: "An error occurred while adding the Book Sale.",
      });
    }
  });

  //   Route to get Sales Data
  router.get("/", async (req, res) => {
    try {
      pool.query("SELECT * FROM book_sales", (err, results) => {
        if (err) throw err;

        res.json(results);
      });
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching the Book Sale",
      });
    }
  });

  //Route to download PDF
  router.get("/download-pdf", async (req, res) => {
    try {
      const startDate = req.query.startDate;
      const endDate = req.query.endDate;
      const query = `SELECT * FROM book_sales WHERE created_at BETWEEN ? AND ? ORDER BY created_at DESC;`;

      pool.query(query, [startDate, endDate], (err, results) => {
        if (err) throw err;

        const docDefinition = {
          pageSize: "A4",
          pageOrientation: "landscape",
          content: [
            {
              table: {
                headerRows: 1,
                widths: [
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                  "auto",
                ],
                body: [
                  [
                    {
                      text: "Index",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Name Of the Book",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Code of The Book",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Price of the Book",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Copies of the Book",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Person In Charge",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Amount Expected",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                    {
                      text: "Amount Given",
                      fillColor: "#202A44",
                      style: "tableHeader",
                      bold: true,
                    },
                  ],
                ],
              },
              layout: {
                hLineWidth: function (i, node) {
                  return 0;
                },
                vLineWidth: function (i, node) {
                  return 0;
                },
                fillColor: function (rowIndex, node, columnIndex) {
                  return rowIndex % 2 === 0 ? "#D3D3D3" : null;
                },
              },
            },
          ],
          styles: {
            tableHeader: {
              fontSize: 13,
              color: "white",
            },
            tableBody: {
              italic: true,
            },
          },
        };
        // Populate the body array with your dataToPdfRows
        docDefinition.content[0].table.body.push(...dataToPdfRows(results));

        // Create the PDF and send it as a response
        const pdfDoc = printer.createPdfKitDocument(docDefinition);
        res.setHeader("Content-Type", "application/pdf");
        pdfDoc.pipe(res);
        pdfDoc.end();
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  //   Route to get Cleared Sales Data
  router.get("/cleared", async (req, res) => {
    try {
      pool.query(
        "SELECT * FROM book_sales WHERE book_condition='CLEARED'",
        (err, results) => {
          if (err) throw err;

          res.json(results);
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching the Book Sale",
      });
    }
  });
  // Route to get marketers data
  router.get("/marketer", async (req, res) => {
    try {
      pool.query(
        "SELECT * FROM defaultdb.users WHERE category = 'Converter';",
        (err, results) => {
          if (err) throw err;

          res.json(results);
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching the Book Sale",
      });
    }
  });

  router.get("/sum", (req, res) => {
    try {
      pool.query(
        "SELECT SUM(book_copies) AS totalBookCopies FROM book_sales",
        (err, results) => {
          if (err) {
            console.error("Error executing query:", err);
            res.status(500).json({ error: "Internal Server Error" });
          } else {
            const totalBookCopies = results[0].totalBookCopies;
            res.json({ totalBookCopies });
          }
        }
      );
    } catch (error) {
      console.error("An error occurred while fetching the SUM:", error);
      res.status(500).json({
        message: "An error occurred while fetching the SUM",
      });
    }
  });
  // Route to get marketers data
  router.get("/offices", async (req, res) => {
    try {
      pool.query(
        "SELECT book_office_issued FROM book_issuance;",
        (err, results) => {
          if (err) throw err;

          res.json(results);
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching the Book Sale",
      });
    }
  });
  //   Route to Update Specific Sale Data
  router.patch("/:id", async (req, res) => {
    const { id } = req.params;
    const { book_copies, book_amount_given, book_condition } = req.body;
    try {
      pool.query(
        "UPDATE book_sales SET book_copies=?, book_amount_given=?, book_condition=? WHERE book_id =?",
        [book_copies, book_amount_given, book_condition, id],
        (err, result) => {
          if (err) {
            console.error(err);
            res.status(500).json({ message: "Server Error" });
          } else if (result.affectedRows > 0) {
            res.json({
              message: "Student details updated successfully",
            });
          } else {
            res.status(404).json({ message: "Student details not found" });
          }
        }
      );
    } catch (error) {
      res.status(500).json({
        message: "An error occurred while fetching Student information.",
      });
    }
  });
  return router;
};
//"SELECT u.*, bu.* FROM defaultdb.users u JOIN foundation.books_turn_over bu ON u.user_id = bu.staff_id WHERE category = 'Converter'",
