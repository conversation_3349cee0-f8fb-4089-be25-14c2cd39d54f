import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Sidebar from "../../components/Sidebar";
import { Link, useNavigate } from "react-router-dom";
import huh from "../../../assets/app-illustrations/Shrug-bro.png";

function formatDate(inputDate) {
  // Create a new Date object from the input string
  const date = new Date(inputDate);

  // Extract year, month, and day components
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-based
  const day = String(date.getDate()).padStart(2, "0");

  // Construct the formatted date string
  const formattedDate = `${day}/${month}/${year}`;

  return formattedDate;
}

const ApproveWorkplans = () => {
  const [workplans, setWorkplans] = useState([]);
  const token = useSelector((state) => state.user.token);
  const userId = useSelector((state) => state.user.user.user_id);
  const navigate = useNavigate();

  useEffect(() => {
    // Fetch workplans data from the server
    const fetchPendingWorkplans = async () => {
      try {
        const response = await fetch(
          `https://workspace.optiven.co.ke/api/workplans/pending?user_id=${userId}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        const data = await response.json();
        console.log("workplans:", data);

        // Filter out rejected workplans that are older than 12 hours
        const currentTime = new Date().getTime();
        const filteredWorkplans = data.filter((workplan) => {
          if (
            workplan.status === "rejected" &&
            workplan.approval_rejection_time
          ) {
            const rejectionTime = new Date(
              workplan.approval_rejection_time
            ).getTime();
            // Keep the workplan if it's less than 12 hours since rejection
            return currentTime - rejectionTime <= 12 * 60 * 60 * 1000;
          }
          // Keep non-rejected workplans
          return true;
        });

        setWorkplans(
          filteredWorkplans.filter(
            (workplan) => new Date() < new Date(workplan.start_date)
          )
        );
      } catch (error) {
        console.error(error);
        toast.error("Failed to fetch workplans. Please try again.", {
          position: "top-center",
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
      }
    };

    fetchPendingWorkplans();
  }, [token, userId]);

  return (
    <Sidebar>
      <div className="container px-4 py-6 mx-auto">
        <div className="block mx-20">
          <div className="text-sm breadcrumbs">
            <ul>
              <li>
                <Link to="/workplan-home">Home</Link>
              </li>
              <li>Approve Work Plans</li>
            </ul>
          </div>
        </div>
        <div className="block mx-20">
          {Array.isArray(workplans) && workplans.length > 0 ? (
            workplans.map((workplan) => (
              <table className="table table-zebra w-full bg-base-100 shadow-xl">
                <tr key={workplan.id}>
                  <td className="w-64">
                    <div>
                      <h1 className="label text-sm text-gray-500">Marketer</h1>
                      <p>{workplan.marketer_name}</p>
                    </div>
                  </td>
                  <td>
                    <div>
                      <h1 className="label text-sm text-gray-500">
                        Start Date
                      </h1>
                      <p>{formatDate(workplan.start_date)}</p>
                    </div>
                  </td>
                  <td>
                    <div>
                      <h1 className="label text-sm text-gray-500">End Date</h1>
                      <p>{formatDate(workplan.end_date)}</p>
                    </div>
                  </td>
                  <td>
                    <div>
                      <h1 className="label text-sm text-gray-500">Status</h1>
                      <p className="uppercase font-extrabold">
                        {workplan.status === "pending_rejection"
                          ? "Under Review"
                          : workplan.status.toUpperCase()}
                      </p>
                    </div>
                  </td>
                  <td>
                    <div className="flex">
                      {!(new Date() > new Date(workplan.start_date)) && (
                        <div>
                          <button
                            className="btn btn-sm btn-outline ml-2"
                            onClick={() =>
                              navigate(
                                `/pending-workplan-details/${workplan.id}`
                              )
                            }
                          >
                            View
                          </button>
                        </div>
                      )}
                    </div>
                  </td>
                </tr>
              </table>
            ))
          ) : (
            <div className="flex justify-center">
              <div className="flex flex-col items-center mt-20">
                <img src={huh} alt="huh" className="lg:w-96" />
                <h1 className="font-bold text-center">
                  No work plans left to approve. Check back later.
                </h1>
              </div>
            </div>
          )}
        </div>
      </div>
    </Sidebar>
  );
};

export default ApproveWorkplans;
