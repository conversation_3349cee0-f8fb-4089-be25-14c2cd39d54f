// work_diary.routes.js
const express = require("express");
const authenticateJWT = require("../../middleware/authenticateJWT");
const router = express.Router();
const { isAfter, subHours, parseISO } = require("date-fns");
const nodemailer = require("nodemailer");

const getISOWeekNumber = (date) => {
  const target = new Date(date.valueOf());
  const dayNr = (date.getDay() + 6) % 7;
  target.setDate(target.getDate() - dayNr + 3);
  const firstThursday = target.valueOf();
  target.setMonth(0, 1);
  if (target.getDay() !== 4) {
    target.setMonth(0, 1 + ((4 - target.getDay() + 7) % 7));
  }
  return 1 + Math.ceil((firstThursday - target) / 604800000);
};

// const isWeekClosed = (date) => {
//   const entryDate = new Date(date);
//   const entryWeekNumber = getISOWeekNumber(entryDate);
//   const currentWeekNumber = getISOWeekNumber(new Date());
//   if (entryWeekNumber < currentWeekNumber) return true;
//   if (entryWeekNumber === currentWeekNumber) {
//     const currentDate = new Date();
//     const weekClosingTime = new Date();
//     weekClosingTime.setDate(
//       weekClosingTime.getDate() + ((7 - currentDate.getDay() + 7) % 7)
//     );
//     weekClosingTime.setHours(0, 0, 0, 0);
//     if (currentDate > weekClosingTime) return true;
//   }
//   return false;
// };

const isWithinWorkingHours = (startDateTimeISO, endDateTimeISO) => {
  const startTime = new Date(startDateTimeISO);
  const endTime = new Date(endDateTimeISO);
  const workStart = new Date(startDateTimeISO);
  workStart.setHours(8, 0, 0, 0);
  const workEnd = new Date(startDateTimeISO);
  workEnd.setHours(17, 0, 0, 0);
  return startTime >= workStart && endTime <= workEnd;
};

const checkForOverlappingEntries = (
  employee_id,
  date,
  startTime,
  endTime,
  pool,
  callback,
  excludeId = null
) => {
  let overlapQuery = `
    SELECT * FROM \`work-diary\`.activities
    WHERE employee_id = ?
    AND date = ?
    AND (
      (start_time < ? AND end_time > ?) OR
      (start_time >= ? AND start_time < ?)
    )
  `;
  const queryParams = [
    employee_id,
    date,
    endTime,
    startTime,
    startTime,
    endTime,
  ];
  if (excludeId) {
    overlapQuery += " AND id != ?";
    queryParams.push(excludeId);
  }
  pool.query(overlapQuery, queryParams, (err, results) => {
    if (err) {
      console.error("Error checking for overlapping activities:", err);
      return callback(err, null);
    }
    return callback(null, results.length > 0);
  });
};

async function sendEmail(userEmail, subject, text) {
  let transporter = nodemailer.createTransport({
    host: "smtp.zoho.com",
    port: 465,
    secure: true,
    auth: {
      user: process.env.DOMAIN_EMAIL,
      pass: process.env.DOMAIN_PASSWORD,
    },
  });
  await transporter.sendMail({
    from: '"Optiven Work Daily Register" <<EMAIL>>',
    to: userEmail,
    subject: subject,
    text: text,
  });
}



  const kenyanHolidays = [
    '2025-01-01',  
    '2025-04-18', 
    '2025-05-01',  
    '2025-06-01', 
    '2025-10-20', 
    '2025-12-12', 
    '2025-12-25',  
    '2025-12-26', 
  ];

  function isKenyanHoliday(dateStr) {
    return kenyanHolidays.includes(dateStr);
  }

  function formatDateToYYYYMMDD(dateObj) {
    if (!dateObj || !(dateObj instanceof Date)) {
        console.warn("Invalid date object received:", dateObj);
        return null; 
    }
    try {
      const year = dateObj.getFullYear();
      const month = String(dateObj.getMonth() + 1).padStart(2, '0');
      const dayOfMonth = String(dateObj.getDate()).padStart(2, '0');
      return `${year}-${month}-${dayOfMonth}`;
    } catch (e) {
      console.error("Error formatting date:", dateObj, e);
      return null; 
    }
  }

const emailFilter = "email NOT LIKE '%inactive%'";
const deptFilter = "LOWER(department) NOT IN ('cashier','cashiergmc','boh','board of directors','kitchen','games','foh','waitron','test')";

module.exports = (pool) => {
  router.post("/send-reminders", authenticateJWT, async (req, res) => {
    const { emails } = req.body;
    if (!emails || emails.length === 0) {
      return res.status(400).json({ message: "No valid emails provided" });
    }
    try {
      for (const email of emails) {
        const subject = "Reminder: Compliance Status";
        const message = `Dear employee,
\n\n
This is a reminder regarding your compliance status. Please ensure you log your activities within the required timeframe.

Best regards,
Your Supervisor`;
        try {
          await sendEmail(email, subject, message);
        } catch (error) {
          console.error(`Failed to send email to ${email}:`, error);
        }
      }
      res.status(200).json({ message: "Reminders sent successfully." });
    } catch (err) {
      console.error("Error sending reminders:", err);
      res.status(500).json({ message: "Server Error" });
    }
  });

  router.get("/employees", authenticateJWT, (req, res) => {
    const query = `
      SELECT a.employee_id, u.user_id, u.fullnames AS name, u.role
      FROM activities a
      LEFT JOIN defaultdb.users u ON a.employee_id = u.user_id
      WHERE ${emailFilter} AND ${deptFilter}
    `;
    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        return res.status(500).json({ message: "Server Error" });
      }
      res.status(results.length > 0 ? 200 : 404)
         .json(results.length > 0 ? results : { message: "No employees found" });
    });
  });

  router.get("/users", authenticateJWT, (req, res) => {
    const query = `
      SELECT * FROM defaultdb.users
      WHERE ${emailFilter} AND ${deptFilter}
    `;
    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        return res.status(500).json({ message: "Server Error" });
      }
      res.status(results.length > 0 ? 200 : 404)
         .json(results.length > 0 ? results : { message: "No users found" });
    });
  });

  router.get("/activities", authenticateJWT, (req, res) => {
    const query = "SELECT * FROM activities";
    pool.query(query, (err, results) => {
      if (err) {
        console.error(err);
        return res.status(500).json({ message: "Server Error" });
      }
      res.status(results.length > 0 ? 200 : 404)
         .json(results.length > 0 ? results : { message: "No activities found" });
    });
  });

  router.get("/calendar-activities/:user_id", authenticateJWT, (req, res) => {
    const { user_id } = req.params;
    const query = "SELECT * FROM activities WHERE employee_id = ?";
    pool.query(query, [user_id], (err, results) => {
      if (err) {
        console.error("Error fetching user activities:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      res.status(results.length > 0 ? 200 : 404)
         .json(results.length > 0 ? results : { message: "No activities found for this user" });
    });
  });

  router.get("/user-profile/:user_id", authenticateJWT, (req, res) => {
    const { user_id } = req.params;
    const userProfileQuery = `
      SELECT fullnames AS name, role, department, Employee_No, Manager
      FROM defaultdb.users
      WHERE user_id = ? AND ${emailFilter} AND ${deptFilter}
    `;
    pool.query(userProfileQuery, [user_id], (err, userResults) => {
      if (err) {
        console.error("Error fetching user profile:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      if (userResults.length === 0 || !userResults[0].Employee_No) {
        return res.status(404).json({ message: "User not found or Employee_No missing" });
      }
      const userProfile = userResults[0];
      console.log(`Fetched user details -> user_id: ${user_id}, fullnames: ${userProfile.name}`);
      const managerEmployeeNo = userProfile.Manager;
      if (!managerEmployeeNo) {
        return res.json({ ...userProfile, supervisor: null });
      }
      const supervisorDetailsQuery = `
        SELECT fullnames
        FROM defaultdb.users
        WHERE Employee_No = ? AND ${emailFilter} AND ${deptFilter}
      `;
      pool.query(supervisorDetailsQuery, [managerEmployeeNo], (err, supervisorDetailsResults) => {
        if (err) {
          console.error("Error fetching supervisor details:", err);
          return res.status(500).json({ message: "Server Error" });
        }
        if (supervisorDetailsResults.length === 0) {
          return res.json({ ...userProfile, supervisor: null });
        }
        return res.json({ ...userProfile, supervisor: supervisorDetailsResults[0].fullnames });
      });
    });
  });

  router.get("/supervisor/:user_id", authenticateJWT, (req, res) => {
    const { user_id } = req.params;
    const userQuery = `
      SELECT Employee_No, Manager
      FROM defaultdb.users
      WHERE user_id = ? AND ${emailFilter} AND ${deptFilter}
    `;
    pool.query(userQuery, [user_id], (err, userResults) => {
      if (err) {
        console.error("Error fetching user:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      if (userResults.length === 0 || !userResults[0].Employee_No) {
        return res.status(404).json({ message: "User not found or Employee_No missing" });
      }
      const managerEmployeeNo = userResults[0].Manager;
      if (!managerEmployeeNo) {
        return res.status(404).json({ message: "No supervisor found for the user" });
      }
      const supervisorDetailsQuery = `
        SELECT user_id, fullnames
        FROM defaultdb.users
        WHERE Employee_No = ? AND ${emailFilter} AND ${deptFilter}
      `;
      pool.query(supervisorDetailsQuery, [managerEmployeeNo], (err, supervisorDetailsResults) => {
        if (err) {
          console.error("Error fetching supervisor details:", err);
          return res.status(500).json({ message: "Server Error" });
        }
        if (supervisorDetailsResults.length === 0) {
          return res.status(404).json({ message: "Supervisor details not found" });
        }
        res.json({
          user_id: supervisorDetailsResults[0].user_id,
          fullnames: supervisorDetailsResults[0].fullnames,
        });
      });
    });
  });

  router.get("/is-supervisor/:user_id", authenticateJWT, (req, res) => {
    const { user_id } = req.params;
    console.log("User", user_id);
    const query = `
      SELECT COUNT(*) AS supervisorCount
      FROM defaultdb.users
      WHERE Manager = (
        SELECT Employee_No
        FROM defaultdb.users
        WHERE user_id = ? AND ${emailFilter} AND ${deptFilter}
      )
      AND ${emailFilter} AND ${deptFilter}
    `;
    pool.query(query, [user_id], (err, results) => {
      if (err) {
        console.error("Error fetching supervisor status:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      const isSupervisor = results[0].supervisorCount > 0;
      res.json({ isSupervisor });
    });
  });

  router.get("/compliance-report", authenticateJWT, (req, res) => {
    if (!req.user || !req.user.id) { 
        console.error("No id found in JWT token");
        return res.status(401).json({ message: "Unauthorized: No valid user ID" });
    }

    const { employees, startDate, endDate } = req.query;
    const supervisorUserId = req.user.id; 

    
    const today = new Date();
    const actualStartDate = startDate || formatDateToYYYYMMDD(today);
    const actualEndDate = endDate || formatDateToYYYYMMDD(today);

    if (!actualStartDate || !actualEndDate) {
        return res.status(400).json({ message: "Invalid date format provided or failed to get current date." });
    }

    const verifyUserQuery = `
      SELECT u.user_id, u.Employee_No
      FROM defaultdb.users u
      WHERE u.user_id = ? 
      /* Add filters if they apply to the supervisor: AND ${emailFilter} AND ${deptFilter} */
    `; 

    pool.query(verifyUserQuery, [supervisorUserId], (verifyErr, verifyResults) => {
        if (verifyErr) {
            console.error("Error verifying user:", verifyErr);
            return res.status(500).json({ message: "Server Error", error: verifyErr.message });
        }

        if (verifyResults.length === 0) {
            return res.status(404).json({ message: "Supervisor user not found in database" });
        }

        const supervisor = verifyResults[0]; 

        
        const supervisorCheckQuery = `
          SELECT COUNT(*) AS supervisorCount
          FROM defaultdb.users
          WHERE Manager = ? 
          /* Add filters if they restrict which employees are counted: AND ${emailFilter} AND ${deptFilter} */
        `; 

        pool.query(supervisorCheckQuery, [supervisor.Employee_No], (checkErr, checkResults) => {
            if (checkErr) {
                console.error("Error checking supervisor status:", checkErr);
                return res.status(500).json({ message: "Server Error", error: checkErr.message });
            }

            if (checkResults[0].supervisorCount === 0) {
                return res.status(403).json({ message: "Access denied: User does not supervise any employees" });
            }

           
            let query = `
              WITH RECURSIVE dates(date) AS (
                SELECT DATE(?) AS date -- Param 1: actualStartDate
                UNION ALL
                SELECT DATE_ADD(date, INTERVAL 1 DAY) FROM dates WHERE date < DATE(?) -- Param 2: actualEndDate
              ),
              employee_compliance AS (
                SELECT
                  u.user_id,
                  u.fullnames AS employee_name,
                  u.email,
                  u.department,
                  dates.date AS activity_date,
                  DAYOFWEEK(dates.date) AS day_of_week, -- MySQL: 1=Sun, 2=Mon,... 7=Sat
                  COALESCE(SUM(CASE WHEN a.focus_area != 'Break' THEN TIMESTAMPDIFF(MINUTE, a.start_time, a.end_time) ELSE 0 END) / 60, 0) AS hours_worked,

                  -- *** MODIFIED required_hours calculation ***
                  CASE
                    -- Rule 1: Sunday is always 0 required hours
                    WHEN DAYOFWEEK(dates.date) = 1 THEN 0
                    -- Rule 2: If the date is a Kenyan Holiday (and not Sunday), it's 0 required hours
                    -- Compare using formatted date string against the list passed as parameter
                    WHEN DATE_FORMAT(dates.date, '%Y-%m-%d') IN (?) THEN 0 -- Param 3: kenyanHolidays array
                    -- Rule 3: Apply weekday rules if not Sunday or Holiday
                    WHEN DAYOFWEEK(dates.date) = 2 THEN 6 -- Monday
                    WHEN DAYOFWEEK(dates.date) = 7 THEN 6 -- Saturday
                    ELSE 7                             -- Tue, Wed, Thu, Fri
                  END AS required_hours
                  -- *** End of Modification ***

                FROM
                  defaultdb.users u
                  CROSS JOIN dates
                  LEFT JOIN activities a ON a.employee_id = u.user_id AND DATE(a.date) = dates.date -- Ensure date comparison is robust
                WHERE
                  u.Manager = ? -- Param 4: supervisor.Employee_No
                  /* Add filters if they apply to the employees being reported on: AND ${emailFilter} AND ${deptFilter} */
                  ${employees ? "AND u.user_id IN (?)" : "-- No specific employee filter"} -- Param 5 (Optional): employees array
                GROUP BY
                  u.user_id, u.fullnames, u.email, u.department, dates.date, DAYOFWEEK(dates.date)
              ),
              user_totals AS (
                SELECT
                  user_id,
                  employee_name,
                  email,
                  department,
                  -- Summing should now use the correctly calculated daily required_hours
                  -- We still exclude Sundays from the total summation as per original logic
                  SUM(CASE WHEN day_of_week != 1 THEN hours_worked ELSE 0 END) AS total_hours_worked,
                  SUM(CASE WHEN day_of_week != 1 THEN required_hours ELSE 0 END) AS total_required_hours
                FROM
                  employee_compliance
                GROUP BY
                  user_id, employee_name, email, department
              )
              SELECT
                user_id,
                employee_name,
                email,
                department,
                total_hours_worked AS hours_logged,
                total_required_hours AS required_hours
              FROM
                user_totals;
            `;

          
            const queryParams = [
                actualStartDate,            
                actualEndDate,             
                kenyanHolidays,             
                supervisor.Employee_No      
            ];

            
            if (employees) {
                const employeeList = employees.split(",").map(id => id.trim()).filter(id => id); // Clean up employee IDs
                if (employeeList.length > 0) {
                   queryParams.push(employeeList); 
                } else {
                   query = query.replace("AND u.user_id IN (?)", "-- Employees query param provided but empty");
                }
            }

            pool.query(query, queryParams, (err, results) => {
                if (err) {
                    console.error("Error fetching compliance report data:", err);
                    return res.status(500).json({ message: "Server Error", error: err.message });
                }

                if (results.length === 0) {
                    res.json([]); 
                } else {
                    const complianceData = results.map((item) => {
                        const hoursWorked = parseFloat(item.hours_logged) || 0;
                        const requiredHours = parseFloat(item.required_hours) || 0;

                        
                        const complianceRate = requiredHours > 0
                          ? Math.min(100, parseFloat(((hoursWorked / requiredHours) * 100).toFixed(1)))
                          : 100; 

                        return {
                            ...item,
                            hours_logged: hoursWorked.toFixed(2), // Format output
                            required_hours: requiredHours.toFixed(2), // Format output
                            compliance_percentage: complianceRate.toFixed(1),
                            is_compliant: complianceRate >= 100
                        };
                    });
                    res.json(complianceData);
                }
            });
        });
    });
});
  
  router.get("/team-members/:supervisor_id", authenticateJWT, (req, res) => {
    const { supervisor_id } = req.params;
    const query = `
      SELECT DISTINCT u.user_id, u.fullnames AS name, u.email
      FROM defaultdb.users u
      WHERE u.Employee_No IN (
        SELECT Employee_No
        FROM defaultdb.users
        WHERE Manager = (
          SELECT Employee_No FROM defaultdb.users
          WHERE user_id = ? AND ${emailFilter} AND ${deptFilter}
        )
        AND ${emailFilter} AND ${deptFilter}
      )
      AND ${emailFilter} AND ${deptFilter}
    `;
    pool.query(query, [supervisor_id], (err, results) => {
      if (err) {
        console.error("Error fetching team members:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      res.json(results);
    });
  });

  router.get("/focus-areas-by-supervisor/:supervisor_id", authenticateJWT, (req, res) => {
    const { supervisor_id } = req.params;
    const query = `
      SELECT DISTINCT fa.focus_area
      FROM \`work-diary\`.focus_area fa
      JOIN defaultdb.users u ON u.Employee_No = fa.staff_no
      WHERE u.Manager = (
        SELECT Employee_No FROM defaultdb.users
        WHERE user_id = ? AND ${emailFilter} AND ${deptFilter}
      )
      AND ${emailFilter} AND ${deptFilter}
    `;
    pool.query(query, [supervisor_id], (err, results) => {
      if (err) {
        console.error("Error fetching focus areas:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      res.json(results);
    });
  });

  router.get("/supervisor/:supervisor_id/focus-areas/report", authenticateJWT, (req, res) => {
    const { supervisor_id } = req.params;
    const { employees, focusAreas, startDate, endDate } = req.query;
    
    // First, get all team members under this supervisor
    const teamMembersQuery = `
      SELECT 
        u.user_id,
        u.fullnames AS employee_name,
        u.department,
        u.Employee_No
      FROM defaultdb.users u
      WHERE u.Manager = (
        SELECT Employee_No FROM defaultdb.users
        WHERE user_id = ? AND ${emailFilter} AND ${deptFilter}
      )
      AND ${emailFilter} AND ${deptFilter}
      ${employees ? "AND u.user_id IN (?)" : ""}
    `;
    
    const teamMembersParams = [supervisor_id];
    if (employees) {
      teamMembersParams.push(employees.split(","));
    }
    
    pool.query(teamMembersQuery, teamMembersParams, (err, teamMembers) => {
      if (err) {
        console.error("Error fetching team members:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      
      if (teamMembers.length === 0) {
        return res.json([]);
      }
      
      // Now get activities data with focus areas
      let activitiesQuery = `
        SELECT
          u.user_id,
          u.fullnames AS employee_name,
          u.department,
          fa.focus_area AS focus_area,
          SUM(TIMESTAMPDIFF(MINUTE, a.start_time, a.end_time)) / 60 AS hours_logged
        FROM activities a
        JOIN defaultdb.users u ON a.employee_id = u.user_id
        JOIN \`work-diary\`.focus_area fa ON fa.staff_no = u.Employee_No AND fa.focus_area = a.focus_area
        WHERE u.Manager = (
          SELECT Employee_No FROM defaultdb.users
          WHERE user_id = ? AND ${emailFilter} AND ${deptFilter}
        )
        AND ${emailFilter} AND ${deptFilter}
        AND fa.focus_area != 'Break'
      `;
      
      const activitiesParams = [supervisor_id];
      
      if (employees) {
        activitiesQuery += " AND u.user_id IN (?)";
        activitiesParams.push(employees.split(","));
      }
      
      if (focusAreas) {
        activitiesQuery += " AND fa.focus_area IN (?)";
        activitiesParams.push(focusAreas.split(","));
      }
      
      if (startDate && endDate) {
        activitiesQuery += " AND a.date BETWEEN ? AND ?";
        activitiesParams.push(startDate, endDate);
      }
      
      activitiesQuery += " GROUP BY u.user_id, fa.focus_area";
      
      pool.query(activitiesQuery, activitiesParams, (err, activities) => {
        if (err) {
          console.error("Error fetching focus areas report:", err);
          return res.status(500).json({ message: "Server Error" });
        }
        
        // If no activities found for any team member, still return all team members
        if (activities.length === 0) {
          // Create an array with basic employee info but zero hours
          const emptyResults = teamMembers.map(member => ({
            user_id: member.user_id,
            employee_name: member.fullnames || member.employee_name,
            department: member.department,
            focus_area: "No Activity",
            hours_logged: 0
          }));
          return res.json(emptyResults);
        }
        
        // Create a set of user_ids who have activities
        const employeesWithActivities = new Set(activities.map(a => a.user_id));
        
        // Add team members who don't have any activities
        const combinedResults = [...activities];
        
        teamMembers.forEach(member => {
          if (!employeesWithActivities.has(member.user_id)) {
            combinedResults.push({
              user_id: member.user_id,
              employee_name: member.fullnames || member.employee_name,
              department: member.department,
              focus_area: "No Activity",
              hours_logged: 0
            });
          }
        });
        
        res.json(combinedResults);
      });
    });
  });
  // Supervisor dashboard activities
  router.get("/supervisor-activities/:employee_id", authenticateJWT, (req, res) => {
    const { employee_id } = req.params;
    const { startDate, endDate } = req.query;
    const supervisorId = req.user.id;
    
    // Verify that the employee belongs to this supervisor's team
    const verifyTeamMemberQuery = `
      SELECT u.user_id
      FROM defaultdb.users u
      WHERE u.user_id = ?
      AND u.Manager = (
        SELECT Employee_No FROM defaultdb.users
        WHERE user_id = ? AND ${emailFilter} AND ${deptFilter}
      )
      AND ${emailFilter} AND ${deptFilter}
    `;
    
    pool.query(verifyTeamMemberQuery, [employee_id, supervisorId], (verifyErr, verifyResults) => {
      if (verifyErr) {
        console.error("Error verifying team member:", verifyErr);
        return res.status(500).json({ message: "Server Error" });
      }
      
      if (verifyResults.length === 0) {
        return res.status(403).json({ message: "Access denied: Not your team member" });
      }
      
      // Get detailed activities
      const query = `
        SELECT 
          a.id,
          a.date, 
          a.start_time, 
          a.end_time, 
          a.focus_area, 
          a.activity_description, 
          a.comments,
          TIMESTAMPDIFF(MINUTE, a.start_time, a.end_time) / 60 AS hours_logged
        FROM activities a
        WHERE a.employee_id = ?
        AND a.date BETWEEN ? AND ?
        ORDER BY a.date DESC, a.start_time DESC
      `;
      
      pool.query(query, [employee_id, startDate, endDate], (err, results) => {
        if (err) {
          console.error("Error fetching employee activities:", err);
          return res.status(500).json({ message: "Server Error" });
        }
        
        res.json(results);
      });
    });
  });

  router.get("/current-week", authenticateJWT, (req, res) => {
    const currentDate = new Date();
    const weekNumber = getISOWeekNumber(currentDate);
    res.json({ weekNumber });
  });

  router.get("/weekly-summary/:user_id", authenticateJWT, (req, res) => {
    const { user_id } = req.params;
    const { week } = req.query;
    const query = `
      SELECT
        SUM(TIMESTAMPDIFF(MINUTE, start_time, end_time)) / 60 AS productiveHours,
        COUNT(DISTINCT focus_area) AS focusAreasCovered,
        ROUND(AVG(CASE WHEN is_compliant = 1 THEN 100 ELSE 0 END), 2) AS productivityScore,
        DAYNAME(date) AS mostProductiveDay
      FROM activities
      WHERE employee_id = ? AND WEEK(date) = WEEK(CURDATE()) - ?
      GROUP BY DAYNAME(date)
      ORDER BY productiveHours DESC
      LIMIT 1;
    `;
    pool.query(query, [user_id, week], (err, results) => {
      if (err) {
        console.error("Error fetching weekly summary:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      const result = results[0] || {
        productiveHours: 0,
        focusAreasCovered: 0,
        mostProductiveDay: "N/A",
        productivityScore: 0,
      };
      res.json(result);
    });
  });

  router.get("/daily-activities/:user_id", authenticateJWT, (req, res) => {
    const { user_id } = req.params;
    const query = `
      SELECT
        DAYNAME(date) AS day,
        COUNT(*) AS activityCount
      FROM activities
      WHERE employee_id = ? AND WEEK(date, 1) = WEEK(CURDATE(), 1)
      GROUP BY DAY(date), DAYNAME(date)
      ORDER BY DAY(date)
    `;
    pool.query(query, [user_id], (err, results) => {
      if (err) {
        console.error("Error fetching daily activities:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      const daysOfWeek = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
      const dailyActivities = daysOfWeek.map((day) => {
        const activity = results.find((a) => a.day === day);
        return { day, activityCount: activity ? activity.activityCount : 0 };
      });
      res.json(dailyActivities);
    });
  });

  router.get("/focus-area-breakdown/:user_id", authenticateJWT, (req, res) => {
    const { user_id } = req.params;
    const { week } = req.query;
    const query = `
      SELECT
        focus_area AS name,
        SUM(TIMESTAMPDIFF(MINUTE, start_time, end_time)) / 60 AS hours
      FROM activities
      WHERE employee_id = ? AND week_number = ?
      GROUP BY focus_area
    `;
    pool.query(query, [user_id, week], (err, results) => {
      if (err) {
        console.error("Error fetching focus area breakdown:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      res.json(results);
    });
  });

  router.get("/time-distribution/:user_id", authenticateJWT, (req, res) => {
    const { user_id } = req.params;
    const query = `
      SELECT
        DATE_FORMAT(start_time, '%H:%i') AS time,
        TIMESTAMPDIFF(MINUTE, start_time, end_time) AS duration
      FROM activities
      WHERE employee_id = ?
    `;
    pool.query(query, [user_id], (err, results) => {
      if (err) {
        console.error("Error fetching time distribution:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      res.json(results);
    });
  });

  router.get("/compliance/:user_id", authenticateJWT, (req, res) => {
    const { user_id } = req.params;
    const { week } = req.query;
    const weekNumber = parseInt(week, 10);
  
    if (isNaN(weekNumber)) {
      return res.status(400).json({ message: "Invalid week number" });
    }
  
    const getWeekStartDate = (weekNum) => {
      const currentYear = new Date().getFullYear();
      const firstDayOfYear = new Date(currentYear, 0, 1); 
      const dayOfWeekJan1 = firstDayOfYear.getDay(); 
      const daysOffset = (weekNum - 1) * 7 - (dayOfWeekJan1 === 0 ? 6 : dayOfWeekJan1 - 1); 
      const startDate = new Date(currentYear, 0, 1 + daysOffset);
      return startDate;
    };
  
    const startDateObj = getWeekStartDate(weekNumber);
    const endDateObj = new Date(startDateObj);
    endDateObj.setDate(endDateObj.getDate() + 6);
  
    const startDate = formatDateToYYYYMMDD(startDateObj);
    const endDate = formatDateToYYYYMMDD(endDateObj);
  
    if (!startDate || !endDate) {
        return res.status(500).json({ message: "Failed to calculate date range." });
    }
  
    const query = `
      WITH RECURSIVE dates(date) AS (
        SELECT DATE(?) AS date -- Use the formatted start date string
        UNION ALL
        SELECT DATE_ADD(date, INTERVAL 1 DAY) FROM dates WHERE date < DATE(?) -- Use the formatted end date string
      )
      SELECT
        d.date AS activityDate, -- This will be retrieved as a Date object by the driver
        DAYOFWEEK(d.date) AS dayOfWeek, -- MySQL: 1=Sun, 2=Mon,... 7=Sat
        COALESCE(SUM(CASE WHEN a.focus_area != 'Break' THEN TIMESTAMPDIFF(MINUTE, a.start_time, a.end_time) ELSE 0 END) / 60, 0) AS hoursWorked
      FROM dates d
      LEFT JOIN activities a ON DATE(a.date) = d.date AND a.employee_id = ? -- Ensure DATE() casting if a.date is DATETIME
      WHERE d.date BETWEEN ? AND ?
      GROUP BY d.date, DAYOFWEEK(d.date)
      ORDER BY d.date; -- Good practice to order results
    `;
  
    pool.query(query, [startDate, endDate, user_id, startDate, endDate], (err, results) => {
      if (err) {
        console.error("Error fetching compliance data:", err);
        return res.status(500).json({ message: "Server Error" });
      }
  
      const complianceData = results.map((day) => {
        const activityDateObj = day.activityDate;
  
       
        const date = formatDateToYYYYMMDD(activityDateObj);
  
        if (!date) {
           console.error("Could not format date for row:", day);
           return null; 
        }
  
        const dayOfWeek = day.dayOfWeek; 
        
        const isHoliday = isKenyanHoliday(date);
  
        let requiredHours = 0;
        if (dayOfWeek === 1) { 
            requiredHours = 0;
        } else if (isHoliday) { 
            requiredHours = 0;
        } else {
            requiredHours = (dayOfWeek === 2 || dayOfWeek === 7) ? 6 : 7;
        }
  
        const hoursWorked = parseFloat(day.hoursWorked || 0);
  
        const compliancePercentage = requiredHours === 0
          ? "100.00" 
          : ((hoursWorked / requiredHours) * 100).toFixed(2);
  
        return {
          date: date, 
          day: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"][dayOfWeek - 1],
          dayOfWeek, 
          hoursWorked: hoursWorked.toFixed(2), 
          requiredHours: requiredHours.toFixed(2), 
          compliancePercentage,
          isCompliant: requiredHours === 0 || parseFloat(compliancePercentage) >= 100,
          isHoliday: isHoliday
        };
      }).filter(day => day !== null); 
  
      
      const complianceDataFiltered = complianceData.filter((day) => day.dayOfWeek !== 1);
  
      
      const totalHoursWorked = complianceDataFiltered.reduce((sum, day) => sum + parseFloat(day.hoursWorked), 0);
      const totalRequiredHours = complianceDataFiltered.reduce((sum, day) => sum + parseFloat(day.requiredHours), 0);
  
      
      const complianceRate = totalRequiredHours > 0
        ? Math.min(100, parseFloat(((totalHoursWorked / totalRequiredHours) * 100).toFixed(1))) 
        : 100; 
  
      res.json({
        isCompliant: complianceRate >= 100,
        complianceRate: complianceRate.toFixed(1), 
        totalHoursWorked: totalHoursWorked.toFixed(2), 
        totalRequiredHours: totalRequiredHours.toFixed(2), 
        complianceData: complianceDataFiltered,
      });
    });
  });
  

  router.get("/total-hours/:user_id", authenticateJWT, (req, res) => {
    const { user_id } = req.params;
    const { week } = req.query;
    const query = `
      SELECT SUM(TIMESTAMPDIFF(MINUTE, start_time, end_time)) / 60 AS totalHoursLogged
      FROM activities
      WHERE employee_id = ? AND WEEK(date, 1) = ?
    `;
    pool.query(query, [user_id, week], (err, results) => {
      if (err) {
        console.error("Error fetching total hours:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      const totalHoursLogged = results[0]?.totalHoursLogged || 0;
      res.json({ totalHoursLogged });
    });
  });

  router.post("/insert-activity", authenticateJWT, (req, res) => {
    const {
      employee_id,
      supervisor_id,
      week_number,
      date,
      startTime,
      endTime,
      focusArea,
      activityDescription,
      comments,
    } = req.body;
    // if (isWeekClosed(date)) {
    //   return res.status(400).json({ message: "Cannot submit entries for a closed week." });
    // }
    const entryStartDateTime = parseISO(`${date}T${startTime}`);
    const entryEndDateTime = parseISO(`${date}T${endTime}`);
    const now = new Date();
    const fortyEightHoursAgo = subHours(now, 48);
    if (isAfter(fortyEightHoursAgo, entryEndDateTime)) {
      return res.status(400).json({ message: "Entries can only be made for the last 48 hours" });
    }
    const workStart = new Date(`${date}T00:00:00`);
    const workEnd = new Date(`${date}T23:59:59`);
    if (entryStartDateTime < workStart || entryEndDateTime > workEnd) {
      return res.status(400).json({ message: "Entries can only be made between midnight and midnight." });
    }
    const duration = (entryEndDateTime - entryStartDateTime) / (1000 * 60 * 60);
    if (duration > 4) {
      return res.status(400).json({ message: "Maximum allowed duration is 4 hours per entry." });
    }
    if (date !== entryEndDateTime.toISOString().split("T")[0]) {
      return res.status(400).json({ message: "Entries cannot span across multiple dates." });
    }
    checkForOverlappingEntries(employee_id, date, startTime, endTime, pool, (err, isOverlapping) => {
      if (err) return res.status(500).json({ message: "Server Error" });
      if (isOverlapping) return res.status(400).json({ message: "This entry overlaps with an existing activity." });
      if (focusArea === "add_new") {
        const insertFocusAreaQuery = `
          INSERT INTO \`work-diary\`.focus_area (staff_no, supervisor_employee_no, focus_area)
          VALUES (?, ?, ?)
        `;
        const getUserInfoQuery = `
          SELECT u.Employee_No AS employeeNo, s.Employee_No AS supervisorEmployeeNo
          FROM defaultdb.users u
          LEFT JOIN defaultdb.users s ON s.user_id = ?
          WHERE u.user_id = ?
        `;
        pool.query(getUserInfoQuery, [supervisor_id, employee_id], (err, userInfoResults) => {
          if (err) {
            console.error("Error fetching user info:", err);
            return res.status(500).json({ message: "Server Error" });
          }
          if (userInfoResults.length === 0) {
            return res.status(404).json({ message: "User or Supervisor not found." });
          }
          const { employeeNo, supervisorEmployeeNo } = userInfoResults[0];
          pool.query(insertFocusAreaQuery, [employeeNo, supervisorEmployeeNo, focusArea], (err) => {
            if (err) {
              console.error("Error inserting new focus area:", err);
              return res.status(500).json({ message: "Server Error" });
            }
            pool.query(
              `SELECT focus_area FROM \`work-diary\`.focus_area WHERE staff_no = ?`,
              [employeeNo],
              (err, focusAreas) => {
                if (err) {
                  console.error("Error fetching focus areas:", err);
                  return res.status(500).json({ message: "Server Error" });
                }
                insertActivity(focusArea, focusAreas);
              }
            );
          });
        });
      } else {
        insertActivity(focusArea, []);
      }
      function insertActivity(finalFocusArea, focusAreas) {
        const commentText = comments || "";
        const mysqlDate = date;
        const mysqlStartTime = startTime;
        const mysqlEndTime = endTime;
        const query = `
          INSERT INTO \`work-diary\`.activities
          (employee_id, supervisor_id, week_number, date, start_time, end_time, focus_area, activity_description, comments)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        pool.query(
          query,
          [
            employee_id,
            supervisor_id,
            week_number,
            mysqlDate,
            mysqlStartTime,
            mysqlEndTime,
            finalFocusArea,
            activityDescription,
            commentText,
          ],
          (err, result) => {
            if (err) {
              console.error("Error inserting activity:", err);
              return res.status(500).json({ message: "Server Error" });
            }
            const newEntryId = result.insertId;
            pool.query(`SELECT * FROM activities WHERE id = ?`, [newEntryId], (err, activityResults) => {
              if (err) {
                console.error("Error fetching new activity:", err);
                return res.status(500).json({ message: "Server Error" });
              }
              res.status(201).json({
                message: "Activity inserted successfully",
                newEntry: activityResults[0],
                focusAreas: focusAreas,
              });
            });
          }
        );
      }
    });
  });

  router.post("/add-focus-area", authenticateJWT, (req, res) => {
    const { employee_id, supervisor_id, focusArea } = req.body;
    const query = `
      INSERT INTO \`work-diary\`.focus_area (staff_no, supervisor_employee_no, focus_area)
      VALUES (?, ?, ?)
    `;
    pool.query(query, [employee_id, supervisor_id, focusArea], (err) => {
      if (err) {
        console.error("Error inserting new focus area:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      res.status(201).json({ message: "Focus area added successfully." });
    });
  });

  router.get("/users/:user_id", authenticateJWT, (req, res) => {
    const { user_id } = req.params;
    const query = `
      SELECT Employee_No
      FROM defaultdb.users
      WHERE user_id = ? AND ${emailFilter} AND ${deptFilter}
    `;
    pool.query(query, [user_id], (err, results) => {
      if (err) {
        console.error("Error fetching user details:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      if (results.length === 0) {
        return res.status(404).json({ message: "User not found" });
      }
      res.json(results[0]);
    });
  });

  router.get("/focus-areas/:user_id", authenticateJWT, (req, res) => {
    const { user_id } = req.params;
    const userQuery = `
      SELECT Employee_No
      FROM defaultdb.users
      WHERE user_id = ? AND ${emailFilter} AND ${deptFilter}
    `;
    pool.query(userQuery, [user_id], (err, userResults) => {
      if (err) {
        console.error("Error fetching user details:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      if (userResults.length === 0 || !userResults[0].Employee_No) {
        return res.status(404).json({ message: "User not found or Employee_No missing" });
      }
      const employeeNo = userResults[0].Employee_No;
      const focusAreaQuery = `
        SELECT *
        FROM \`work-diary\`.focus_area
        WHERE staff_no = ?
      `;
      pool.query(focusAreaQuery, [employeeNo], (err, results) => {
        if (err) {
          console.error("Error fetching focus areas:", err);
          return res.status(500).json({ message: "Server Error" });
        }
        res.json(results);
      });
    });
  });

  router.get("/last-activity/:user_id", authenticateJWT, (req, res) => {
    const { user_id } = req.params;
    const query = `
      SELECT DATE_FORMAT(date, '%Y-%m-%d') AS date,
             TIME_FORMAT(end_time, '%H:%i:%s') AS end_time
      FROM activities
      WHERE employee_id = ?
      ORDER BY date DESC, end_time DESC
      LIMIT 1
    `;
    pool.query(query, [user_id], (err, results) => {
      if (err) {
        console.error("Error fetching last logged activity:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      if (results.length === 0) {
        return res.status(404).json({ message: "No activities found for this user" });
      }
      res.status(200).json(results[0]);
    });
  });

  router.put("/update-activity/:id", authenticateJWT, (req, res) => {
    const { id } = req.params;
    const { date, startTime, endTime, focusArea, activityDescription, comments } = req.body;
    const querySelect = `SELECT *, CONCAT(date, ' ', end_time) AS end_datetime FROM activities WHERE id = ?`;
    pool.query(querySelect, [id], (err, results) => {
      if (err) {
        console.error("Error fetching activity:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      const activity = results[0];
      if (!activity) {
        return res.status(404).json({ message: "Activity not found" });
      }
      const employee_id = activity.employee_id;
      const now = new Date();
      const endDateTime = new Date(activity.end_datetime);
      const fortyEightHoursAgo = subHours(now, 48); 
      if (endDateTime < fortyEightHoursAgo) {
        return res.status(400).json({ message: "You can only edit entries within 48 hours of submission." });
      }
      const entryStartDateTime = parseISO(`${date}T${startTime}`);
      const entryEndDateTime = parseISO(`${date}T${endTime}`);
      if (isAfter(fortyEightHoursAgo, entryEndDateTime)) {
        return res.status(400).json({ message: "Entries can only be made for the last 48 hours" });
      }
      const duration = (entryEndDateTime - entryStartDateTime) / (1000 * 60 * 60);
      if (duration > 4) {
        return res.status(400).json({ message: "Maximum allowed duration is 4 hours per entry." });
      }
      if (date !== entryEndDateTime.toISOString().split("T")[0]) {
        return res.status(400).json({ message: "Entries cannot span across multiple dates." });
      }
      checkForOverlappingEntries(employee_id, date, startTime, endTime, pool, (err, isOverlapping) => {
        if (err) return res.status(500).json({ message: "Server Error" });
        if (isOverlapping) return res.status(400).json({ message: "This entry overlaps with an existing activity." });
        if (focusArea === "add_new") {
          const insertFocusAreaQuery = `
            INSERT INTO \`work-diary\`.focus_area (staff_no, supervisor_employee_no, focus_area)
            VALUES (?, ?, ?)
          `;
          pool.query(insertFocusAreaQuery, [employee_id, activity.supervisor_id, focusArea], (err) => {
            if (err) {
              console.error("Error inserting new focus area:", err);
              return res.status(500).json({ message: "Server Error" });
            }
            updateActivity(focusArea);
          });
        } else {
          updateActivity(focusArea);
        }
        function updateActivity(finalFocusArea) {
          const mysqlDate = date;
          const mysqlStartTime = startTime;
          const mysqlEndTime = endTime;
          const queryUpdate = `
            UPDATE activities
            SET date = ?, start_time = ?, end_time = ?, focus_area = ?, activity_description = ?, comments = ?
            WHERE id = ?
          `;
          pool.query(queryUpdate, [mysqlDate, mysqlStartTime, mysqlEndTime, finalFocusArea, activityDescription, comments, id], (err) => {
            if (err) {
              console.error("Error updating activity:", err);
              return res.status(500).json({ message: "Server Error" });
            }
            pool.query(`SELECT * FROM activities WHERE id = ?`, [id], (err, activityResults) => {
              if (err) {
                console.error("Error fetching updated activity:", err);
                return res.status(500).json({ message: "Server Error" });
              }
              res.status(200).json({
                message: "Activity updated successfully",
                updatedEntry: activityResults[0],
              });
            });
          });
        }
      }, id);
    });
  });

  router.get("/activities/user/:user_id", authenticateJWT, (req, res) => {
    const { user_id } = req.params;
    const { week } = req.query;
    if (!week) {
      return res.status(400).json({ message: "Week number is required" });
    }
    const query = `
      SELECT * FROM activities
      WHERE employee_id = ? AND WEEK(date, 1) = ?
      ORDER BY date DESC, start_time DESC
    `;
    pool.query(query, [user_id, week], (err, results) => {
      if (err) {
        console.error("Error fetching user activities:", err);
        return res.status(500).json({ message: "Database query failed", error: err.message });
      }
      res.json(results);
    });
  });

  router.get("/activities/user-reports/:user_id", authenticateJWT, (req, res) => {
    const { user_id } = req.params;
    const { startDate, endDate } = req.query;
    let query = `
      SELECT * FROM activities
      WHERE employee_id = ?
    `;
    const queryParams = [user_id];
    if (startDate && endDate) {
      query += " AND date BETWEEN ? AND ?";
      queryParams.push(startDate, endDate);
    }
    query += " ORDER BY date DESC, start_time DESC";
    pool.query(query, queryParams, (err, results) => {
      if (err) {
        console.error("Error fetching activities:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      res.json(results);
    });
  });
  // employee past 48 hours activities
  router.get("/activities/:user_id", authenticateJWT, (req, res) => {
    const { user_id } = req.params;
    const query = `
      SELECT DATE_FORMAT(date, '%Y-%m-%d') AS date,
             TIME_FORMAT(start_time, '%H:%i:%s') AS start_time,
             TIME_FORMAT(end_time, '%H:%i:%s') AS end_time,
             activity_description,
             comments,
             focus_area,
             id,
             supervisor_id,
             week_number,
             employee_id,
             CONCAT(date, ' ', end_time) AS end_datetime
      FROM activities
      WHERE employee_id = ? AND CONCAT(date, ' ', end_time) >= NOW() - INTERVAL 48 HOUR
      ORDER BY date DESC, start_time DESC
    `;
    pool.query(query, [user_id], (err, results) => {
      if (err) {
        console.error("Error fetching user activities:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      const activities = results.map((entry) => ({ ...entry }));
      res.json(activities);
    });
  });

  router.delete("/delete-entry/:id", authenticateJWT, (req, res) => {
    const { id } = req.params;
    const selectQuery = `
      SELECT *, CONCAT(date, ' ', end_time) AS end_datetime
      FROM activities
      WHERE id = ?
    `;
    pool.query(selectQuery, [id], (err, results) => {
      if (err) {
        console.error("Error fetching entry:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      if (results.length === 0) {
        return res.status(404).json({ message: "Entry not found" });
      }
      const entry = results[0];
      const endDateTime = new Date(entry.end_datetime);
      const fortyEightHoursAgo = new Date(Date.now() - 48 * 60 * 60 * 1000);
      if (endDateTime < fortyEightHoursAgo) {
        return res.status(400).json({ message: "Cannot delete entries older than 48 hours." });
      }
      const deleteQuery = `DELETE FROM activities WHERE id = ?`;
      pool.query(deleteQuery, [id], (err, result) => {
        if (err) {
          console.error("Error deleting entry:", err);
          return res.status(500).json({ message: "Server Error" });
        }
        res.status(200).json({ message: "Entry deleted successfully" });
      });
    });
  });

  router.post("/focus-areas", authenticateJWT, (req, res) => {
    const { user_id } = req.user;
    const { focusAreas } = req.body;
    if (!Array.isArray(focusAreas) || focusAreas.length === 0) {
      return res.status(400).json({ message: "Focus areas are required." });
    }
    const values = focusAreas.map((area) => [user_id, area]);
    const query = "INSERT INTO focus_areas (user_id, focus_area) VALUES ?";
    pool.query(query, [values], (err) => {
      if (err) {
        console.error("Error inserting focus areas:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      res.status(201).json({ message: "Focus areas added successfully." });
    });
  });

  router.get("/focus-areas", authenticateJWT, (req, res) => {
    const { user_id } = req.user;
    const query = "SELECT id, focus_area FROM focus_areas WHERE user_id = ?";
    pool.query(query, [user_id], (err, results) => {
      if (err) {
        console.error("Error fetching focus areas:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      res.status(200).json(results);
    });
  });

  router.put("/focus-areas/:id", authenticateJWT, (req, res) => {
    const { id } = req.params;
    const { user_id } = req.user;
    const { focus_area } = req.body;
    const query = "UPDATE focus_areas SET focus_area = ? WHERE id = ? AND user_id = ?";
    pool.query(query, [focus_area, id, user_id], (err, result) => {
      if (err) {
        console.error("Error updating focus area:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Focus area not found or not authorized." });
      }
      res.status(200).json({ message: "Focus area updated successfully." });
    });
  });

  router.delete("/focus-areas/:id", authenticateJWT, (req, res) => {
    const { id } = req.params;
    const { user_id } = req.user;
    const query = "DELETE FROM focus_areas WHERE id = ? AND user_id = ?";
    pool.query(query, [id, user_id], (err, result) => {
      if (err) {
        console.error("Error deleting focus area:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Focus area not found or not authorized." });
      }
      res.status(200).json({ message: "Focus area deleted successfully." });
    });
  });

  router.get("/hr-admin/compliance", authenticateJWT, (req, res) => {
    const { startDate, endDate, role, employeeId, departments } = req.query;

    if (!startDate || !endDate) {
        return res.status(400).json({ message: "Start date and end date are required." });
    }

    let query = `
      WITH RECURSIVE dates(date) AS (
        SELECT DATE(?) AS date -- Param 1: startDate
        UNION ALL
        -- Use <= for inclusive end date as in original query
        SELECT DATE_ADD(date, INTERVAL 1 DAY) FROM dates WHERE date <= DATE(?) -- Param 2: endDate
      ),
      daily_data AS (
        -- Calculate daily worked and required hours accurately first
        SELECT
          u.user_id,
          u.fullnames,
          u.role,
          u.department,
          d.date,
          DAYOFWEEK(d.date) as day_of_week, -- MySQL: 1=Sun, 2=Mon,... 7=Sat
          -- Calculate hours worked on this specific day
          COALESCE(SUM(CASE WHEN a.focus_area != 'Break' THEN TIMESTAMPDIFF(MINUTE, a.start_time, a.end_time) ELSE 0 END) / 60, 0) AS daily_hours_worked,
          -- Calculate CORRECT required hours for this specific day, including holidays
          CASE
            WHEN DAYOFWEEK(d.date) = 1 THEN 0 -- Sunday
            WHEN DATE_FORMAT(d.date, '%Y-%m-%d') IN (?) THEN 0 -- Holiday Check (Param 3: kenyanHolidays)
            WHEN DAYOFWEEK(d.date) = 2 THEN 6 -- Monday
            WHEN DAYOFWEEK(d.date) = 7 THEN 6 -- Saturday
            ELSE 7                             -- Tue, Wed, Thu, Fri
          END AS daily_required_hours
        FROM defaultdb.users u
        CROSS JOIN dates d
        LEFT JOIN activities a ON u.user_id = a.employee_id AND a.date = d.date
        WHERE d.date BETWEEN ? AND ? -- Apply date range filter early (Params 4 & 5: startDate, endDate)
          -- Apply optional user/role/dept filters here
          ${role ? "AND u.role = ?" : ""} -- Param 6 (Optional)
          ${employeeId ? "AND u.user_id IN (?)" : ""} -- Param 7 (Optional)
          ${departments ? "AND u.department IN (?)" : ""} -- Param 8 (Optional)
          /* AND ${emailFilter} AND ${deptFilter} - Apply relevant filters if needed */
        GROUP BY
          u.user_id, u.fullnames, u.role, u.department, d.date -- Group by day to get daily figures
      )
      -- Final Aggregation: Sum up daily figures, but only consider Mon-Fri for the totals
      SELECT
        user_id,
        fullnames AS employee_name,
        role,
        department,
        -- Sum hours worked ONLY from Mon-Fri (day_of_week 2 to 6)
        SUM(CASE WHEN day_of_week BETWEEN 2 AND 6 THEN daily_hours_worked ELSE 0 END) AS hours_logged,
        -- Sum required hours ONLY from Mon-Fri (these daily values already account for holidays)
        SUM(CASE WHEN day_of_week BETWEEN 2 AND 6 THEN daily_required_hours ELSE 0 END) AS required_hours
      FROM daily_data
      GROUP BY user_id, fullnames, role, department; -- Final grouping by user
    `;

    // Prepare parameters carefully in order
    const queryParams = [
        startDate,        
        endDate,          
        kenyanHolidays,   
        startDate,        
        endDate           
    ];

    
    if (role) {
        queryParams.push(role); 
    }
    if (employeeId) {
        const employeeList = employeeId.split(",").map(id => id.trim()).filter(id => id);
         if (employeeList.length > 0) {
             queryParams.push(employeeList); 
         } else {
             
             query = query.replace("AND u.user_id IN (?)", "-- employeeId param was empty");
         }
    }
    if (departments) {
        const departmentList = departments.split(",").map(dep => dep.trim()).filter(dep => dep);
         if (departmentList.length > 0) {
            queryParams.push(departmentList); // Param 8
         } else {
            
            query = query.replace("AND u.department IN (?)", "-- departments param was empty");
         }
    }

    pool.query(query, queryParams, (err, results) => {
        if (err) {
            console.error("Error fetching HR compliance data:", err);
            return res.status(500).json({ message: "Server Error", error: err.message });
        }

        if (results.length === 0) {
            return res.json([]);
        }

        const calculateCompliancePercentage = (hoursLogged, requiredHours) => {
            const totalLogged = parseFloat(hoursLogged) || 0;
            const totalRequired = parseFloat(requiredHours) || 0;
            if (totalRequired === 0) {
                return "100.00";
            }
            
            return Math.min(100, (totalLogged / totalRequired) * 100).toFixed(2);
        };

        const reports = results.map((item) => {
            const compliance_percentage = calculateCompliancePercentage(item.hours_logged, item.required_hours);
            return {
                ...item,
                hours_logged: parseFloat(item.hours_logged || 0).toFixed(2), 
                required_hours: parseFloat(item.required_hours || 0).toFixed(2), 
                compliance_percentage,
                is_compliant: parseFloat(compliance_percentage) >= 100,
            };
        });

        res.json(reports);
    });
});


  router.get("/hr-admin/departments", authenticateJWT, (req, res) => {
    const query = `
      SELECT dp_id, dp_name
      FROM defaultdb.departments
      WHERE dp_name IS NOT NULL AND dp_name != ''
      AND LOWER(dp_name) NOT IN ('cashier','cashiergmc','boh','board of directors','kitchen','games','foh','waitron','test')
      ORDER BY dp_name
      
    `;
    pool.query(query, (err, results) => {
      if (err) {
        console.error("Error fetching departments:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      res.json(results);
    });
  });

  router.get("/hr-admin/employees", authenticateJWT, (req, res) => {
    const { departments } = req.query;
    let query = `
      SELECT u.user_id, u.fullnames AS name, u.department
      FROM defaultdb.users u
      WHERE 1=1
      AND ${emailFilter} AND ${deptFilter}
    `;
    const queryParams = [];
    if (departments) {
      query += " AND u.department IN (?)";
      queryParams.push(departments.split(","));
    }
    query += " ORDER BY name";
    pool.query(query, queryParams, (err, results) => {
      if (err) {
        console.error("Error fetching employees:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      res.json(results);
    });
  });

  router.get("/hr-admin/focus-areas", authenticateJWT, (req, res) => {
    const { departments, employees } = req.query;
    let query = `
      SELECT DISTINCT fa.focus_area AS focus_area
      FROM \`work-diary\`.focus_area fa
      JOIN defaultdb.users u ON fa.staff_no = u.Employee_No
      WHERE 1=1 AND ${emailFilter} AND ${deptFilter}
    `;
    const queryParams = [];
    if (departments) {
      query += " AND u.department IN (?)";
      queryParams.push(departments.split(","));
    }
    if (employees) {
      query += " AND u.user_id IN (?)";
      queryParams.push(employees.split(","));
    }
    query += " ORDER BY fa.focus_area";
    pool.query(query, queryParams, (err, results) => {
      if (err) {
        console.error("Error fetching focus areas:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      res.json(results.map((fa) => fa.focus_area));
    });
  });

  router.get("/hr-admin/focus-areas/report", authenticateJWT, (req, res) => {
    const { departments, employees, focusAreas, startDate, endDate } = req.query;
    let query = `
      SELECT
        u.user_id,
        u.fullnames AS employee_name,
        u.department,
        fa.focus_area AS focus_area,
        SUM(TIMESTAMPDIFF(MINUTE, a.start_time, a.end_time)) / 60 AS hours_logged
      FROM activities a
      JOIN defaultdb.users u ON a.employee_id = u.user_id
      JOIN \`work-diary\`.focus_area fa ON fa.staff_no = u.Employee_No AND fa.focus_area = a.focus_area
      WHERE 1=1 AND fa.focus_area != 'Break'
      AND ${emailFilter} AND ${deptFilter}
    `;
    const queryParams = [];
    if (departments) {
      query += " AND u.department IN (?)";
      queryParams.push(departments.split(","));
    }
    if (employees) {
      query += " AND u.user_id IN (?)";
      queryParams.push(employees.split(","));
    }
    if (focusAreas) {
      query += " AND fa.focus_area IN (?)";
      queryParams.push(focusAreas.split(","));
    }
    if (startDate && endDate) {
      query += " AND a.date BETWEEN ? AND ?";
      queryParams.push(startDate, endDate);
    }
    query += " GROUP BY u.user_id, fa.focus_area";
    pool.query(query, queryParams, (err, results) => {
      if (err) {
        console.error("Error fetching focus areas report:", err);
        return res.status(500).json({ message: "Server Error" });
      }
      res.json(results);
    });
  });

  return router;
};
