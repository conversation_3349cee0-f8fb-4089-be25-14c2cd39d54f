import React from "react";
import { useSelector } from "react-redux";
import EnhancedHome from "./home-components/EnhancedHome";
import OriginalHome from "./home-components/OriginalHome";

const Home = () => {
  const accessRole = useSelector((state) => state.user.accessRole);

  const accessRoles = accessRole.split("#");
  const isMarketer = accessRoles.includes("113");
  const isAdmin = accessRoles.includes("logisticsAdmin");

  const shouldShowEnhancedHome = isMarketer || isAdmin;

  return shouldShowEnhancedHome ? <EnhancedHome /> : <OriginalHome />;
};

export default Home;
