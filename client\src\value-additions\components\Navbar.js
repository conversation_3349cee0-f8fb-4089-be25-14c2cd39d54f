import React from "react";
import { Link, NavLink, useNavigate } from "react-router-dom";
import axios from "axios";
import logo from "../assets/optiven-logo-full.png";
import userAvatar from "../assets/gifs/user.gif"
import "./css/Navbar.css";

const Navbar = ({ fullName, role }) => {
  const navigate = useNavigate();

  const logout = (e) => {
    axios
      .post("https://workspace.optiven.co.ke/api/logout")
      .then(() => {
        // Clear authentication token from local storage
        localStorage.removeItem("token");
        console.log("Token cleared. User successfully logged out");
        // Redirect user to login page or wherever you want
        navigate("/login");
      })
      .catch((error) => {
        console.error(error);
      });
  };

  return (
    <div className="navbar bg-primary">
      <div className="flex-1">
        <div
          className="tooltip tooltip-right"
          data-tip="Click to toggle drawer"
        >
          <label
            htmlFor="my-drawer"
            className="drawer-button cursor-pointer btn btn-square btn-ghost"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="black"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M4 6h16M4 12h16M4 18h7"
              />
            </svg>
          </label>
        </div>
        <NavLink className="btn btn-ghost normal-case text-xl" to="/">
          <img src={logo} alt="logo" className="w-40" />
        </NavLink>
      </div>
      <div className="flex-none">
        <div className="dropdown dropdown-end">
          <div className="flex items-center">
            <div className="mr-2 content-center hide-on-mobile">
              <h1 className="font-bold text-sm">{fullName}</h1>
              <p className="text-xs italic">{role.toUpperCase()}</p>
            </div>
            <label tabIndex={0} className="btn btn-ghost btn-circle avatar">
              <div className="w-10 rounded-full">
                <img
                  alt="user"
                  src={userAvatar}
                />
              </div>
            </label>
          </div>
          <ul
            tabIndex={0}
            className="menu menu-compact dropdown-content mt-3 p-2 shadow bg-base-100 rounded-box w-52"
          >
            <li>
              <Link>Profile</Link>
            </li>
            <li>
              <Link>Settings</Link>
            </li>
            <li>
              <Link onClick={logout}>Logout</Link>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default Navbar;
