const express = require("express");
const router = express.Router();
const nodemailer = require("nodemailer");

const sendEmail = async (userEmail, subject, text) => {
  try {
    let transporter = nodemailer.createTransport({
      host: "smtp.zoho.com",
      port: 465,
      secure: true, // true for 465, false for other ports
      auth: {
        user: process.env.DOMAIN_EMAIL, // your domain email account from .env
        pass: process.env.DOMAIN_PASSWORD,
      },
    });

    let info = await transporter.sendMail({
      from: '"Optiven Conference Platform 💂" <<EMAIL>>', // sender address
      to: userEmail, // list of receivers
      subject: subject, // Subject line
      text: text, // plain text body
    });

   // console.log("Message sent: %s", info.messageId);
   // console.log(`Email sent to: ${userEmail}`);
  } catch (error) {
    console.error("Error sending email:", error);
  }
};

module.exports = (pool) => {
  // GET all bookings
  router.get("/", (req, res) => {
    pool.query("SELECT * FROM conference.bookings", (err, results) => {
      if (err) {
        return res
          .status(500)
          .send({ message: "Error retrieving bookings: " + err });
      }
      res.send(results);
    });
  });

  // GET a single booking by ID
  router.get("/:id", (req, res) => {
    const { id } = req.params;
    pool.query(
      "SELECT * FROM conference.bookings WHERE id = ?",
      [id],
      (err, result) => {
        if (err) {
          return res
            .status(500)
            .send({ message: "Error retrieving booking: " + err });
        }
        if (result.length > 0) {
          res.send(result[0]);
        } else {
          res.status(404).send({ message: "Booking not found" });
        }
      }
    );
  });

  // POST a new booking
  router.post("/", (req, res) => {
    const {
      user_id,
      room_id,
      date,
      start_time,
      end_time,
      number_of_people,
      purpose,
    } = req.body;

    if (
      !user_id ||
      !room_id ||
      !date ||
      !start_time ||
      !end_time ||
      !number_of_people ||
      !purpose
    ) {
      return res.status(400).send({ message: "All fields are required." });
    }

    // Fetch the user's email from the database
    pool.query(
      "SELECT email FROM defaultdb.users WHERE user_id = ?",
      [user_id],
      async (err, results) => {
        if (err) {
          console.error("Error retrieving user email:", err);
          return res
            .status(500)
            .send({ message: "Error retrieving user email: " + err });
        }

        if (results.length === 0) {
          return res.status(404).send({ message: "User not found" });
        }

        const email = results[0].email;

        // Check for booking conflicts in the same room
        const conflictQuery = `
        SELECT * FROM bookings
        WHERE room_id = ? AND date = ? AND NOT (end_time <= ? OR start_time >= ?)
      `;

        pool.query(
          conflictQuery,
          [room_id, date, start_time, end_time],
          (err, results) => {
            if (err) {
              console.error("Error checking for booking conflicts:", err);
              return res.status(500).send({
                message: "Error checking for booking conflicts: " + err,
              });
            }

            if (results.length > 0) {
              // Conflict found, send an error response
              return res.status(409).send({
                message:
                  "Booking conflict detected. Please choose another time or room.",
              });
            }

            // No conflicts, proceed to insert the booking
            const insertQuery = `
          INSERT INTO bookings (user_id, room_id, date, start_time, end_time, number_of_people, purpose)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `;
            pool.query(
              insertQuery,
              [
                user_id,
                room_id,
                date,
                start_time,
                end_time,
                number_of_people,
                purpose,
              ],
              async (err, result) => {
                if (err) {
                  console.error("Error adding booking:", err);
                  return res
                    .status(500)
                    .send({ message: "Error adding booking: " + err });
                }

                // Send confirmation email
                await sendEmail(
                  email,
                  "Booking Confirmation",
                  `Dear Customer,

  We are pleased to confirm your booking for ${date} from ${start_time} to ${end_time}.

  Thank you for choosing our services. If you have any questions or need further assistance, please do not hesitate to contact us.

  Best regards,`
                );

                // Schedule email notifications
                const bookingStart = new Date(`${date}T${start_time}`);
                const reminderTime = new Date(
                  bookingStart.getTime() - 10 * 60 * 1000
                ); // 10 minutes before start time
                const endReminderTime = new Date(
                  new Date(`${date}T${end_time}`).getTime() + 15 * 60 * 1000
                ); // 15 minutes after end time

                setTimeout(() => {
                  sendEmail(
                    email,
                    "Booking Reminder",
                    `Dear Customer,

    This is a friendly reminder that your booking will start in 10 minutes.

    Please ensure you are prepared and have everything you need for your session.

    Best regards,`
                  );
                }, reminderTime.getTime() - Date.now());

                setTimeout(() => {
                  sendEmail(
                    email,
                    "Booking Ended",
                    `Dear Customer,

    We hope your session was productive. Your booking ended 15 minutes ago.

    Please ensure the room is vacated and all your belongings are collected.

    Thank you for using our services.

    Best regards,`
                  );
                }, endReminderTime.getTime() - Date.now());

                res.status(201).send({
                  message: "Booking added successfully",
                  bookingId: result.insertId,
                });
              }
            );
          }
        );
      }
    );
  });

  // POST overwrite a booking
  router.post("/overwrite", (req, res) => {
    const {
      user_id,
      room_id,
      date,
      start_time,
      end_time,
      number_of_people,
      purpose,
      email,
    } = req.body;

    if (
      !user_id ||
      !room_id ||
      !date ||
      !start_time ||
      !end_time ||
      !number_of_people ||
      !purpose
    ) {
      return res.status(400).send({ message: "All fields are required." });
    }

    // Fetch the conflicting booking
    const conflictQuery = `
      SELECT * FROM bookings
      WHERE room_id = ? AND date = ? AND NOT (end_time <= ? OR start_time >= ?)
    `;

    pool.query(
      conflictQuery,
      [room_id, date, start_time, end_time],
      async (err, results) => {
        if (err) {
          console.error("Error checking for booking conflicts:", err);
          return res.status(500).send({
            message: "Error checking for booking conflicts: " + err,
          });
        }

        if (results.length > 0) {
          // Conflict found, proceed to overwrite
          const conflictingBooking = results[0];

          // Retrieve the email of the user for the conflicting booking
          pool.query(
            "SELECT email FROM defaultdb.users WHERE user_id = ?",
            [conflictingBooking.user_id],
            async (err, emailResults) => {
              if (err) {
                console.error("Error retrieving user email:", err);
                return res.status(500).send({
                  message: "Error retrieving user email: " + err,
                });
              }

              if (emailResults.length === 0) {
                return res.status(404).send({ message: "User not found" });
              }

              const conflictingUserEmail = emailResults[0].email;

              // Delete the conflicting booking
              const deleteQuery = "DELETE FROM bookings WHERE id = ?";
              pool.query(deleteQuery, [conflictingBooking.id], async (err) => {
                if (err) {
                  console.error("Error deleting conflicting booking:", err);
                  return res.status(500).send({
                    message: "Error deleting conflicting booking: " + err,
                  });
                }

                // Notify the original user about the cancellation
                //console.log(`Sending cancellation email to: ${conflictingUserEmail}`);
                await sendEmail(
                  conflictingUserEmail,
                  "Booking Cancellation",
                  `Dear Customer,

                  Your booking for ${date} from ${conflictingBooking.start_time} to ${conflictingBooking.end_time} has been cancelled.

                  Please make a new booking at your earliest convenience.

                  Best regards,`
                );

                // Insert the new booking
                const insertQuery = `
                  INSERT INTO bookings (user_id, room_id, date, start_time, end_time, number_of_people, purpose)
                  VALUES (?, ?, ?, ?, ?, ?, ?)
                `;
                pool.query(
                  insertQuery,
                  [
                    user_id,
                    room_id,
                    date,
                    start_time,
                    end_time,
                    number_of_people,
                    purpose,
                  ],
                  async (err, result) => {
                    if (err) {
                      console.error("Error adding booking:", err);
                      return res
                        .status(500)
                        .send({ message: "Error adding booking: " + err });
                    }

                    // Send confirmation email
                    await sendEmail(
                      email,
                      "Booking Confirmation",
                      `Dear Customer,

                      We are pleased to confirm your booking for ${date} from ${start_time} to ${end_time}.

                      Thank you for choosing our services. If you have any questions or need further assistance, please do not hesitate to contact us.

                      Best regards,`
                    );

                    res.status(201).send({
                      message: "Booking overwritten successfully",
                      bookingId: result.insertId,
                    });
                  }
                );
              });
            }
          );
        } else {
          return res.status(404).send({
            message: "No conflicting booking found to overwrite.",
          });
        }
      }
    );
  });

  // PUT to update a booking by ID
  router.put("/:id", (req, res) => {
    const { id } = req.params;
    const {
      user_id,
      room_id,
      date,
      start_time,
      end_time,
      number_of_people,
      purpose,
    } = req.body;
    pool.query(
      "UPDATE conference.bookings SET user_id = ?, room_id = ?, date = ?, start_time = ?, end_time = ?, number_of_people = ?, purpose = ? WHERE id = ?",
      [
        user_id,
        room_id,
        date,
        start_time,
        end_time,
        number_of_people,
        purpose,
        id,
      ],
      (err, result) => {
        if (err) {
          return res
            .status(500)
            .send({ message: "Error updating booking: " + err });
        }
        if (result.affectedRows == 0) {
          return res.status(404).send({ message: "Booking not found" });
        }
        res.send({ message: "Booking updated successfully" });
      }
    );
  });

  // DELETE a booking by ID
  router.delete("/:id", (req, res) => {
    const { id } = req.params;
    pool.query(
      "DELETE FROM conference.bookings WHERE id = ?",
      [id],
      (err, result) => {
        if (err) {
          return res
            .status(500)
            .send({ message: "Error deleting booking: " + err });
        }
        if (result.affectedRows == 0) {
          return res.status(404).send({ message: "Booking not found" });
        }
        res.send({ message: "Booking deleted successfully" });
      }
    );
  });

  return router;
};
